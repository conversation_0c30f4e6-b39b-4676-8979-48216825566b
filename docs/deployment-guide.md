# Deployment Guide

This guide covers deploying the Avantiq Billing System to various environments including development, staging, and production.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Build and Package](#build-and-package)
3. [Environment Configuration](#environment-configuration)
4. [Deployment Options](#deployment-options)
5. [Database Setup](#database-setup)
6. [Security Considerations](#security-considerations)
7. [Monitoring and Health Checks](#monitoring-and-health-checks)
8. [Troubleshooting](#troubleshooting)

## Prerequisites

### Required Tools

- Java 21 runtime
- Docker (for containerized deployments)
- PostgreSQL 15+
- kubectl (for Kubernetes deployments)
- SSL certificates (for production)

### Required Access

- Database server access
- Container registry (Docker Hub, ECR, etc.)
- Cloud provider credentials (AWS, GCP, Azure)
- SSL certificate authority

## Build and Package

### 1. Build the Application

```bash
# Clean build with all tests
./gradlew clean build

# Build without tests (faster)
./gradlew clean build -x test

# Build specific profile
./gradlew clean build -Pprofile=prod
```

### 2. Create Docker Image

```bash
# Build Docker image
docker build -t avantiq-billing:latest .

# Tag for registry
docker tag avantiq-billing:latest your-registry/avantiq-billing:v1.0.0

# Push to registry
docker push your-registry/avantiq-billing:v1.0.0
```

### 3. Create Deployment Artifacts

```bash
# Create distribution zip
./gradlew distZip

# Create distribution tar
./gradlew distTar

# Output location
ls build/distributions/
```

## Environment Configuration

### Environment Variables

Create environment-specific configuration:

#### Production (.env.prod)

```bash
# Database Configuration
DB_HOST=prod-db.example.com
DB_PORT=5432
DB_NAME=avantiq_billing
POSTGRES_USER=avantiq_app
POSTGRES_PASSWORD=${PROD_POSTGRES_PASSWORD}  # From secrets manager
DB_SSL_MODE=require

# Security Configuration
JWT_SECRET=${PROD_JWT_SECRET}  # From secrets manager
JWT_EXPIRATION=86400000  # 24 hours

# Application Configuration
SPRING_PROFILES_ACTIVE=prod
SERVER_PORT=8080
GRPC_PORT=9090

# Rate Limiting
SECURITY_RATE_LIMIT_MAX_REQUESTS=1000
SECURITY_RATE_LIMIT_WINDOW_SECONDS=60

# CORS Configuration
CORS_ALLOWED_ORIGINS=https://app.example.com,https://admin.example.com
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE
CORS_MAX_AGE=3600

# Monitoring
MANAGEMENT_ENDPOINTS_ENABLED=true
MANAGEMENT_ENDPOINT_HEALTH_ENABLED=true
MANAGEMENT_ENDPOINT_METRICS_ENABLED=true
MANAGEMENT_ENDPOINT_INFO_ENABLED=true

# Logging
LOG_LEVEL=INFO
LOG_PATTERN=[%d{ISO8601}] [%thread] %-5level %logger{36} - %msg%n
```

#### Staging (.env.staging)

```bash
# Similar to production but with staging-specific values
DB_HOST=staging-db.example.com
DB_SSL_MODE=prefer
JWT_EXPIRATION=3600000  # 1 hour for faster testing
LOG_LEVEL=DEBUG
```

### Application Properties

Create profile-specific properties:

#### application-prod.properties

```properties
# Production Profile
spring.profiles.active=prod

# Database
spring.datasource.url=jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}?sslmode=${DB_SSL_MODE}
spring.datasource.username=${POSTGRES_USER}
spring.datasource.password=${POSTGRES_PASSWORD}
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000

# JPA
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true

# Security
spring.security.require-ssl=true
server.forward-headers-strategy=native

# gRPC
grpc.server.port=${GRPC_PORT:9090}
grpc.server.security.enabled=true

# Actuator
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=when-authorized
management.metrics.export.prometheus.enabled=true
```

## Deployment Options

### Option 1: Docker Compose (Development/Staging)

```yaml
version: '3.8'

services:
  app:
    image: your-registry/avantiq-billing:v1.0.0
    ports:
      - "8080:8080"
      - "9090:9090"
    environment:
      - SPRING_PROFILES_ACTIVE=staging
    env_file:
      - .env.staging
    depends_on:
      - postgres
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  postgres:
    image: postgres:15-alpine
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      POSTGRES_DB: avantiq_billing
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "5432:5432"

volumes:
  postgres_data:
```

Deploy:

```bash
docker-compose -f docker-compose.prod.yml up -d
```

### Option 2: Kubernetes

#### deployment.yaml

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: avantiq-billing
  namespace: production
spec:
  replicas: 3
  selector:
    matchLabels:
      app: avantiq-billing
  template:
    metadata:
      labels:
        app: avantiq-billing
    spec:
      containers:
      - name: app
        image: your-registry/avantiq-billing:v1.0.0
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 9090
          name: grpc
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: avantiq-secrets
              key: db-password
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: avantiq-secrets
              key: jwt-secret
        envFrom:
        - configMapRef:
            name: avantiq-config
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /actuator/health/liveness
            port: 8081
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8081
          initialDelaySeconds: 30
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: avantiq-billing
  namespace: production
spec:
  selector:
    app: avantiq-billing
  ports:
  - name: http
    port: 8080
    targetPort: 8080
  - name: grpc
    port: 9090
    targetPort: 9090
  type: LoadBalancer
```

#### configmap.yaml

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: avantiq-config
  namespace: production
data:
  DB_HOST: "prod-db.example.com"
  DB_PORT: "5432"
  DB_NAME: "avantiq_billing"
  POSTGRES_USER: "avantiq_app"
  DB_SSL_MODE: "require"
  JWT_EXPIRATION: "86400000"
  GRPC_PORT: "9090"
```

Deploy:

```bash
# Create namespace
kubectl create namespace production

# Create secrets
kubectl create secret generic avantiq-secrets \
  --from-literal=db-password=$POSTGRES_PASSWORD \
  --from-literal=jwt-secret=$JWT_SECRET \
  -n production

# Apply configurations
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/deployment.yaml

# Check status
kubectl get pods -n production
kubectl logs -f deployment/avantiq-billing -n production
```

### Option 3: Cloud-Specific Deployments

#### AWS ECS

```json
{
  "family": "avantiq-billing",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "2048",
  "containerDefinitions": [{
    "name": "app",
    "image": "your-ecr-repo/avantiq-billing:v1.0.0",
    "portMappings": [
      {"containerPort": 8080, "protocol": "tcp"},
      {"containerPort": 9090, "protocol": "tcp"}
    ],
    "environment": [
      {"name": "SPRING_PROFILES_ACTIVE", "value": "prod"}
    ],
    "secrets": [
      {
        "name": "POSTGRES_PASSWORD",
        "valueFrom": "arn:aws:secretsmanager:region:account:secret:db-password"
      },
      {
        "name": "JWT_SECRET",
        "valueFrom": "arn:aws:secretsmanager:region:account:secret:jwt-secret"
      }
    ],
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/avantiq-billing",
        "awslogs-region": "us-east-1",
        "awslogs-stream-prefix": "ecs"
      }
    }
  }]
}
```

#### Google Cloud Run

```yaml
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: avantiq-billing
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/minScale: "1"
        autoscaling.knative.dev/maxScale: "100"
    spec:
      containers:
      - image: gcr.io/project/avantiq-billing:v1.0.0
        ports:
        - containerPort: 8080
        - containerPort: 9090
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: prod
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: db-password
              key: latest
        resources:
          limits:
            cpu: "2"
            memory: "2Gi"
```

## Database Setup

### 1. Create Database and User

```sql
-- Create database
CREATE DATABASE avantiq_billing;

-- Create application user
CREATE USER avantiq_app WITH ENCRYPTED PASSWORD 'secure-password';

-- Grant privileges
GRANT CONNECT ON DATABASE avantiq_billing TO avantiq_app;
GRANT CREATE ON DATABASE avantiq_billing TO avantiq_app;

-- Connect to the database
\c avantiq_billing

-- Create schema
CREATE SCHEMA IF NOT EXISTS billing AUTHORIZATION avantiq_app;

-- Grant schema privileges
GRANT ALL ON SCHEMA billing TO avantiq_app;
GRANT ALL ON ALL TABLES IN SCHEMA billing TO avantiq_app;
GRANT ALL ON ALL SEQUENCES IN SCHEMA billing TO avantiq_app;
```

### 2. Run Migrations

```bash
# Using Flyway CLI
flyway -url=********************************************************** \
       -user=avantiq_app \
       -password=$POSTGRES_PASSWORD \
       -locations=filesystem:./src/main/resources/db/migration \
       migrate

# Or using application startup
java -jar avantiq-billing.jar \
     --spring.flyway.enabled=true \
     --spring.flyway.baseline-on-migrate=true
```

### 3. Verify Migration

```sql
-- Check migration history
SELECT * FROM flyway_schema_history ORDER BY installed_rank;

-- Verify tables exist
\dt billing.*
```

## Security Considerations

### 1. SSL/TLS Configuration

#### Application SSL

```properties
# Enable HTTPS
server.ssl.enabled=true
server.ssl.key-store=/path/to/keystore.p12
server.ssl.key-store-password=${SSL_KEYSTORE_PASSWORD}
server.ssl.key-store-type=PKCS12
server.ssl.key-alias=avantiq

# Force HTTPS
server.ssl.require-ssl=true
security.require-ssl=true
```

#### gRPC TLS

```properties
# gRPC SSL configuration
grpc.server.security.enabled=true
grpc.server.security.certificate-chain=/path/to/server.crt
grpc.server.security.private-key=/path/to/server.key
grpc.server.security.trust-cert-collection=/path/to/ca.crt
```

### 2. Secrets Management

#### AWS Secrets Manager

```bash
# Store secrets
aws secretsmanager create-secret \
  --name avantiq/prod/db-password \
  --secret-string "$POSTGRES_PASSWORD"

aws secretsmanager create-secret \
  --name avantiq/prod/jwt-secret \
  --secret-string "$JWT_SECRET"
```

#### Kubernetes Secrets

```bash
# Create from files
kubectl create secret generic avantiq-tls \
  --from-file=tls.crt=/path/to/tls.crt \
  --from-file=tls.key=/path/to/tls.key \
  -n production
```

### 3. Network Security

#### Firewall Rules

```bash
# PostgreSQL (internal only)
- Source: Application subnet
- Port: 5432
- Protocol: TCP

# Application HTTP (behind load balancer)
- Source: Load balancer subnet
- Port: 8080
- Protocol: TCP

# gRPC
- Source: Allowed client IPs
- Port: 9090
- Protocol: TCP

# Management endpoints (internal only)
- Source: Monitoring subnet
- Port: 8081
- Protocol: TCP
```

### 4. Security Headers

Configure reverse proxy or load balancer:

```nginx
# Nginx example
add_header X-Frame-Options "DENY" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
add_header Content-Security-Policy "default-src 'self'" always;
```

## Monitoring and Health Checks

### 1. Health Check Endpoints

```bash
# Liveness probe
curl http://localhost:8081/actuator/health/liveness

# Readiness probe
curl http://localhost:8081/actuator/health/readiness

# Detailed health
curl http://localhost:8081/actuator/health
```

### 2. Metrics Collection

#### Prometheus Configuration

```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'avantiq-billing'
    metrics_path: '/actuator/prometheus'
    static_configs:
      - targets: ['app:8081']
```

#### Key Metrics to Monitor

- JVM memory usage: `jvm_memory_used_bytes`
- HTTP request rate: `http_server_requests_seconds_count`
- gRPC request rate: `grpc_server_handled_total`
- Database connection pool: `hikaricp_connections_active`
- Business metrics: `customer_created_total`, `payment_processed_total`

### 3. Logging

#### Centralized Logging (ELK Stack)

```xml
<!-- logback-spring.xml -->
<configuration>
  <appender name="LOGSTASH" class="net.logstash.logback.appender.LogstashTcpSocketAppender">
    <destination>logstash:5000</destination>
    <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
      <providers>
        <mdc/>
        <context/>
        <logLevel/>
        <loggerName/>
        <pattern>
          <pattern>
            {
              "app": "avantiq-billing",
              "env": "${SPRING_PROFILES_ACTIVE}"
            }
          </pattern>
        </pattern>
        <threadName/>
        <message/>
        <logstashMarkers/>
        <stackTrace/>
      </providers>
    </encoder>
  </appender>
</configuration>
```

### 4. Alerts

Example alert rules:

```yaml
# Prometheus alerts
groups:
- name: avantiq-billing
  rules:
  - alert: HighErrorRate
    expr: rate(http_server_requests_seconds_count{status=~"5.."}[5m]) > 0.05
    for: 5m
    annotations:
      summary: "High error rate detected"
      
  - alert: DatabaseConnectionPoolExhausted
    expr: hikaricp_connections_active / hikaricp_connections_max > 0.9
    for: 5m
    annotations:
      summary: "Database connection pool near exhaustion"
```

## Troubleshooting

### Common Deployment Issues

#### 1. Application Won't Start

```bash
# Check logs
docker logs avantiq-billing
kubectl logs -f deployment/avantiq-billing

# Common causes:
# - Missing environment variables
# - Database connection failure
# - Port already in use
# - Insufficient memory
```

#### 2. Database Migration Failures

```bash
# Check migration status
SELECT * FROM flyway_schema_history WHERE success = false;

# Repair migrations
./gradlew flywayRepair

# Manually run failed migration
psql -U avantiq_app -d avantiq_billing -f V{version}__{description}.sql
```

#### 3. Performance Issues

```bash
# Enable debug logging
export LOG_LEVEL=DEBUG

# Check slow queries
SELECT query, calls, mean_exec_time
FROM pg_stat_statements
WHERE mean_exec_time > 100
ORDER BY mean_exec_time DESC;

# Monitor connection pool
curl http://localhost:8081/actuator/metrics/hikaricp.connections.active
```

#### 4. Security Issues

```bash
# Verify JWT secret is set
echo $JWT_SECRET | wc -c  # Should be >= 32

# Check SSL configuration
openssl s_client -connect localhost:8080 -showcerts

# Verify CORS headers
curl -I -H "Origin: https://example.com" https://api.example.com
```

### Emergency Procedures

#### Rollback

```bash
# Kubernetes
kubectl rollout undo deployment/avantiq-billing -n production

# Docker
docker-compose down
docker-compose up -d --scale app=0
docker-compose up -d avantiq-billing:previous-version
```

#### Database Backup/Restore

```bash
# Backup
pg_dump -h prod-db.example.com -U avantiq_app -d avantiq_billing > backup.sql

# Restore
psql -h prod-db.example.com -U avantiq_app -d avantiq_billing < backup.sql
```

## Post-Deployment Checklist

- [ ] Application health checks passing
- [ ] All environment variables set correctly
- [ ] Database migrations completed successfully
- [ ] SSL certificates valid and not expiring soon
- [ ] Monitoring and alerting configured
- [ ] Logs flowing to centralized system
- [ ] Backup procedures tested
- [ ] Security scan completed
- [ ] Performance baseline established
- [ ] Documentation updated

## Support

For deployment issues:
1. Check application logs
2. Review monitoring dashboards
3. Consult runbooks
4. Contact DevOps team
5. Escalate to development team if needed