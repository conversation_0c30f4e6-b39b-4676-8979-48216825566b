# gRPC Testing Guide

This guide provides examples for testing all gRPC endpoints using `grpcurl`.

## Prerequisites

- Service running on `localhost:9090`
- `grpcurl` installed
- Service running in plaintext mode (no TLS)

## Service Information

- **Package**: `com.avantiq.billing.customer.grpc`
- **Service**: `CustomerService`
- **Full Service Name**: `com.avantiq.billing.customer.grpc.CustomerService`

## Testing Commands

### 1. Segment Operations

Create segments first as they're referenced by customers:

```bash
# Create a segment
grpcurl -plaintext -d '{"segment": {"name": "Premium", "description": "Premium customers"}}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/CreateSegment

# Create another segment
grpcurl -plaintext -d '{"segment": {"name": "Standard", "description": "Standard customers"}}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/CreateSegment

# List segments to get IDs
grpcurl -plaintext -d '{}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/ListSegments

# Get a specific segment by ID
grpcurl -plaintext -d '{"id": "SEGMENT_ID_HERE"}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/GetSegment

# Update a segment
grpcurl -plaintext -d '{"segment": {"id": "SEGMENT_ID_HERE", "name": "Premium Plus", "description": "Premium Plus customers"}}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/UpdateSegment

# Delete a segment
grpcurl -plaintext -d '{"id": "SEGMENT_ID_HERE"}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/DeleteSegment
```

### 2. Customer Operations

```bash
# Create a customer (without segment_id - will auto-generate)
grpcurl -plaintext -d '{"customer": {"first_name": "John", "last_name": "Doe", "email": "<EMAIL>", "country": "US", "company_name": "Acme Corp"}}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/CreateCustomer

# Create a customer with segment (use segment ID from above)
grpcurl -plaintext -d '{"customer": {"first_name": "Jane", "last_name": "Smith", "email": "<EMAIL>", "country": "US", "segment_id": "SEGMENT_ID_HERE"}}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/CreateCustomer

# Get customer by ID
grpcurl -plaintext -d '{"id": "CUSTOMER_ID_HERE"}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/GetCustomer

# Get customer by email
grpcurl -plaintext -d '{"email": "<EMAIL>"}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/GetCustomer

# Update customer
grpcurl -plaintext -d '{"customer": {"id": "CUSTOMER_ID_HERE", "first_name": "John", "last_name": "Doe", "email": "<EMAIL>", "country": "US", "company_name": "Acme Corporation"}}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/UpdateCustomer

# List customers with pagination
grpcurl -plaintext -d '{"page_size": 10}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/ListCustomers

# List customers with filter
grpcurl -plaintext -d '{"filter": "country:US", "page_size": 5}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/ListCustomers

# Delete customer
grpcurl -plaintext -d '{"id": "CUSTOMER_ID_HERE"}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/DeleteCustomer
```

### 3. Address Operations

```bash
# Create a billing address
grpcurl -plaintext -d '{"address": {"customer_id": "CUSTOMER_ID_HERE", "street_address": "123 Main St", "city": "New York", "state": "NY", "postal_code": "10001", "country": "US", "type": "BILLING"}}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/CreateAddress

# Create a shipping address
grpcurl -plaintext -d '{"address": {"customer_id": "CUSTOMER_ID_HERE", "street_address": "456 Oak Ave", "city": "Boston", "state": "MA", "postal_code": "02101", "country": "US", "type": "SHIPPING"}}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/CreateAddress

# Get address by ID
grpcurl -plaintext -d '{"id": "ADDRESS_ID_HERE"}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/GetAddress

# Update address
grpcurl -plaintext -d '{"address": {"id": "ADDRESS_ID_HERE", "customer_id": "CUSTOMER_ID_HERE", "street_address": "123 Main Street", "city": "New York", "state": "NY", "postal_code": "10001", "country": "US", "type": "BILLING"}}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/UpdateAddress

# List addresses
grpcurl -plaintext -d '{}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/ListAddresses

# List addresses with filter
grpcurl -plaintext -d '{"filter": "customer_id:CUSTOMER_ID_HERE"}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/ListAddresses

# Delete address
grpcurl -plaintext -d '{"id": "ADDRESS_ID_HERE"}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/DeleteAddress
```

### 4. Contact Operations

```bash
# Create a default contact
grpcurl -plaintext -d '{"contact": {"customer_id": "CUSTOMER_ID_HERE", "first_name": "Jane", "last_name": "Smith", "email": "<EMAIL>", "phone": "+*********0", "contact_type": "billing", "is_default": true}}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/CreateContact

# Create a secondary contact
grpcurl -plaintext -d '{"contact": {"customer_id": "CUSTOMER_ID_HERE", "first_name": "Bob", "last_name": "Johnson", "email": "<EMAIL>", "phone": "+1987654321", "contact_type": "technical", "is_default": false}}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/CreateContact

# Get contact by ID
grpcurl -plaintext -d '{"id": "CONTACT_ID_HERE"}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/GetContact

# Update contact
grpcurl -plaintext -d '{"contact": {"id": "CONTACT_ID_HERE", "customer_id": "CUSTOMER_ID_HERE", "first_name": "Jane", "last_name": "Smith", "email": "<EMAIL>", "phone": "+*********0", "contact_type": "billing", "is_default": true}}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/UpdateContact

# List contacts
grpcurl -plaintext -d '{}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/ListContacts

# List contacts with filter
grpcurl -plaintext -d '{"filter": "customer_id:CUSTOMER_ID_HERE"}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/ListContacts

# Delete contact
grpcurl -plaintext -d '{"id": "CONTACT_ID_HERE"}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/DeleteContact
```

### 5. Payment Method Operations

#### Credit Card Payment Method

```bash
# Create a credit card payment method
grpcurl -plaintext -d '{"payment_method": {"customer_id": "CUSTOMER_ID_HERE", "type": "CREDIT_CARD", "is_default": true, "credit_card": {"card_number": "****************", "cardholder_name": "John Doe", "expiration_month": "12", "expiration_year": "2025", "cvv": "123", "brand": "Visa"}}}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/CreatePaymentMethod

# Create a tokenized credit card
grpcurl -plaintext -d '{"payment_method": {"customer_id": "CUSTOMER_ID_HERE", "type": "TOKENIZED_CREDIT_CARD", "is_default": false, "tokenized_credit_card": {"token": "tok_*********0", "brand": "Mastercard"}}}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/CreatePaymentMethod
```

#### Bank Account Payment Method

```bash
# Create a bank account payment method
grpcurl -plaintext -d '{"payment_method": {"customer_id": "CUSTOMER_ID_HERE", "type": "BANK_ACCOUNT", "is_default": false, "bank_account": {"account_number": "*********", "routing_number": "*********"}}}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/CreatePaymentMethod
```

#### Other Payment Methods

```bash
# Create PayPal payment method
grpcurl -plaintext -d '{"payment_method": {"customer_id": "CUSTOMER_ID_HERE", "type": "PAYPAL", "is_default": false, "paypal": {"email": "<EMAIL>"}}}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/CreatePaymentMethod

# Create invoice payment method
grpcurl -plaintext -d '{"payment_method": {"customer_id": "CUSTOMER_ID_HERE", "type": "INVOICE", "is_default": false, "invoice": {"invoice_email": "<EMAIL>", "purchase_order_number": "PO-12345", "payment_term_days": 30}}}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/CreatePaymentMethod
```

#### Payment Method Management

```bash
# Get payment method by ID
grpcurl -plaintext -d '{"id": "PAYMENT_METHOD_ID_HERE"}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/GetPaymentMethod

# Update payment method
grpcurl -plaintext -d '{"payment_method": {"id": "PAYMENT_METHOD_ID_HERE", "customer_id": "CUSTOMER_ID_HERE", "type": "CREDIT_CARD", "is_default": true, "credit_card": {"card_number": "****************", "cardholder_name": "John Doe", "expiration_month": "06", "expiration_year": "2026", "cvv": "456", "brand": "Visa"}}}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/UpdatePaymentMethod

# List payment methods
grpcurl -plaintext -d '{}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/ListPaymentMethods

# List payment methods with filter
grpcurl -plaintext -d '{"filter": "customer_id:CUSTOMER_ID_HERE"}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/ListPaymentMethods

# Delete payment method
grpcurl -plaintext -d '{"id": "PAYMENT_METHOD_ID_HERE"}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/DeletePaymentMethod
```

### 6. Note Operations

```bash
# Create a note
grpcurl -plaintext -d '{"note": {"customer_id": "CUSTOMER_ID_HERE", "content": "Customer called about billing question on 2024-01-15", "created_by": "support_user"}}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/CreateNote

# Create another note
grpcurl -plaintext -d '{"note": {"customer_id": "CUSTOMER_ID_HERE", "content": "Payment method updated successfully", "created_by": "billing_admin"}}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/CreateNote

# Get note by ID
grpcurl -plaintext -d '{"id": "NOTE_ID_HERE"}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/GetNote

# Update note
grpcurl -plaintext -d '{"note": {"id": "NOTE_ID_HERE", "customer_id": "CUSTOMER_ID_HERE", "content": "Customer called about billing question on 2024-01-15. Issue resolved.", "updated_by": "support_user"}}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/UpdateNote

# List notes
grpcurl -plaintext -d '{}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/ListNotes

# List notes with filter
grpcurl -plaintext -d '{"filter": "customer_id:CUSTOMER_ID_HERE"}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/ListNotes

# Delete note
grpcurl -plaintext -d '{"id": "NOTE_ID_HERE"}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/DeleteNote
```

### 7. Relationship Operations

```bash
# Create a paying relationship (parent pays for child)
grpcurl -plaintext -d '{"relationship": {"parent_id": "PARENT_CUSTOMER_ID", "child_id": "CHILD_CUSTOMER_ID", "type": "PAYING", "level": 1}}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/CreateRelationship

# Create a reporting relationship (hierarchical)
grpcurl -plaintext -d '{"relationship": {"parent_id": "PARENT_CUSTOMER_ID", "child_id": "CHILD_CUSTOMER_ID", "type": "REPORTING", "level": 1, "segment_id": "SEGMENT_ID_HERE"}}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/CreateRelationship

# Get relationship by ID
grpcurl -plaintext -d '{"id": "RELATIONSHIP_ID_HERE"}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/GetRelationship

# List relationships
grpcurl -plaintext -d '{}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/ListRelationships

# List relationships with filter
grpcurl -plaintext -d '{"filter": "parent_id:PARENT_CUSTOMER_ID"}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/ListRelationships

# Delete relationship
grpcurl -plaintext -d '{"id": "RELATIONSHIP_ID_HERE"}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/DeleteRelationship
```

## Step-by-Step Testing Flow

### 1. Basic Setup
```bash
# Create a segment
grpcurl -plaintext -d '{"segment": {"name": "Standard", "description": "Standard customers"}}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/CreateSegment

# Create a customer
grpcurl -plaintext -d '{"customer": {"first_name": "Alice", "last_name": "Johnson", "email": "<EMAIL>", "country": "US"}}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/CreateCustomer
```

### 2. Extract IDs
From the responses above, copy the returned IDs:
- `segment.id` from segment creation
- `customer.id` from customer creation

### 3. Add Customer Details
```bash
# Add an address (use customer ID from step 2)
grpcurl -plaintext -d '{"address": {"customer_id": "CUSTOMER_ID_HERE", "street_address": "123 Main St", "city": "Anytown", "state": "CA", "postal_code": "12345", "country": "US", "type": "BILLING"}}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/CreateAddress

# Add a contact
grpcurl -plaintext -d '{"contact": {"customer_id": "CUSTOMER_ID_HERE", "first_name": "Alice", "last_name": "Johnson", "email": "<EMAIL>", "contact_type": "primary", "is_default": true}}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/CreateContact

# Add a payment method
grpcurl -plaintext -d '{"payment_method": {"customer_id": "CUSTOMER_ID_HERE", "type": "CREDIT_CARD", "is_default": true, "credit_card": {"card_number": "****************", "cardholder_name": "Alice Johnson", "expiration_month": "12", "expiration_year": "2025", "cvv": "123", "brand": "Visa"}}}' localhost:9090 com.avantiq.billing.customer.grpc.CustomerService/CreatePaymentMethod
```

## Important Notes

1. **ID Management**: Save the IDs returned from creation operations for use in subsequent operations
2. **Segment IDs**: Must be valid TypeID format (26 characters) - let the system generate them
3. **Customer Status**: Defaults to `ACTIVE` (0), other values: `INACTIVE` (1), `SUSPENDED` (2), `DELETED` (3)
4. **Address Types**: `BILLING` (0), `SHIPPING` (1)
5. **Relationship Types**: `PAYING` (0), `REPORTING` (1)
6. **Payment Method Types**: `CREDIT_CARD` (0), `TOKENIZED_CREDIT_CARD` (1), `PAYPAL` (2), `BANK_ACCOUNT` (3), `INVOICE` (4)

## Common Errors and Solutions

1. **"domain is null"**: Invalid segment_id format - omit the field to auto-generate
2. **"Address already in use"**: gRPC port conflict - check Docker containers
3. **"HTTP/2 client preface string missing"**: TLS/plaintext mismatch - use `-plaintext` flag
4. **"Method not found"**: Use full service name `com.avantiq.billing.customer.grpc.CustomerService`

## List Available Services

```bash
# List all available services
grpcurl -plaintext localhost:9090 list

# List methods for CustomerService
grpcurl -plaintext localhost:9090 list com.avantiq.billing.customer.grpc.CustomerService

# Describe a specific method
grpcurl -plaintext localhost:9090 describe com.avantiq.billing.customer.grpc.CustomerService.CreateCustomer
```