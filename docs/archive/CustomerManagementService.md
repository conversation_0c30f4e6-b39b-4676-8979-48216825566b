# Customer Management Service – Requirements & Design Document

---

## 1. Overview

This document describes the requirements and high-level design for a Customer Management Service in a billing system. The service supports customer creation with duplicate prevention, hierarchical account relationships, segmentation, validation, payment methods, customer notes, and multiple contacts per customer. All domain models are explicitly mapped to the database schema, and robust error handling is implemented in the service and endpoint layers.

---

## 2. Functional Requirements

### 2.1 Customer Creation
- Create a new customer with the following **required fields**:
  - First Name
  - Last Name
  - Address
  - Email (must be unique across all customers)
  - Country
- **Optional fields**:
  - Company Name
  - VAT Number
- **Duplicate Checks**: System will prevent creation of customers with duplicate email addresses.
- **Segment Assignment**: All customers are assigned to a default segment unless specified in the request.

### 2.2 Account Hierarchy Management
- **Link accounts** with two types of hierarchy:
  - **Responsible/Paying (Parent-Child)**: Can only be nested two levels (i.e., one parent, direct children, and one grandchild only).
    - No deeper nesting for responsible/paying relationships.
    - A customer can only have one paying parent.
  - **Subordinate/Child (Reporting)**: Can be nested multiple levels (no limit).
- **Segment Restriction**: Only customers within the same segment can be linked in a parent-child relationship.

### 2.3 Customer Modification
- Customers can update their information (all fields except email for uniqueness).

### 2.4 Multiple Contacts per Customer
- **Default Contact:** The customer’s own information (first name, last name, email) is the default contact.
- **Additional Contacts:** Customers may add multiple additional contacts for notification purposes.
  - **Required fields for additional contacts:**
    - First Name
    - Last Name
    - Email Address
    - Contact Type (Enumerated: e.g., 'billing', 'technical', 'notification', etc.)
  - **Rules:**
    - Each contact’s email must be unique per customer.
    - Contacts are associated with a customer but do not function as login accounts.
    - Contacts can be used for sending notifications, alerts, or other communication as configured.

### 2.5 Payment Methods
- After creation, customers can add payment methods:
  - **Supported Types**: Credit Card, Check, ACH Credit, ACH Debit.
  - **Standard validation** for each payment type (e.g., credit card number format, routing/account for ACH).

### 2.6 Address Validation
- Address must be validated:
  - **Zip code** and **country** fields validated using standard regex per country.

### 2.7 Segmentation
- Customers belong to a "segment" for logical segregation.
- Parent-child relationships only allowed within the same segment.

### 2.8 Address Type Support
- Each customer address now includes an `addressType` field, which can be either `BILLING` or `SHIPPING`.
- The `addressType` is exposed in both REST and gRPC APIs and is mapped to the `address_type` column in the database.
- The proto definition for `Address` includes an `AddressType` enum.
- All endpoints and mappers are updated to support this field.

---

## 3. Non-functional Requirements

- **RESTful API** for all operations.
- **Role-based access control** for sensitive operations (modify, add payment, link hierarchy).
- **Scalability** to support large customer bases.
- **Auditing** for changes to customer data and relationships.

---

## 4. High-Level Architecture Diagram

```
+---------------------+       +------------------+       +-------------------+
|   API Gateway       | ----> | Customer Service | ----> | Database (RDBMS)  |
+---------------------+       +------------------+       +-------------------+
          |                             |                            |
          |                             |                            |
          v                             v                            v
+------------------+         +---------------------+       +-------------------+
| AuthN/AuthZ Svc  |         | Payment Method Svc  |       | External Address  |
+------------------+         +---------------------+       | Validation API    |
                                                            +-------------------+
```

---

## 5. Domain Model

### 5.1 Customer

- **CustomerDomain**
  - id (UUID, PK, generated)
  - firstName (String, required)
  - lastName (String, required)
  - address (AddressDomain, embedded, with explicit column mapping)
  - email (String, required, unique)
  - companyName (String, optional)
  - vatNumber (String, optional)
  - country (String, required)
  - segmentId (UUID, required, FK)
  - createdAt (LocalDateTime)
  - updatedAt (LocalDateTime)
  - status (ACTIVE, INACTIVE, required)

- **AddressDomain (Embeddable)**
  - street (String, mapped to address_line1)
  - city (String)
  - state (String)
  - postalCode (String)
  - country (String, mapped to address_country)
  - addressType (Enum: BILLING, SHIPPING, mapped to address_type)

- **CustomerRelationshipDomain**
  - id (UUID, PK, generated)
  - parentId (UUID, required, FK)
  - childId (UUID, required, FK)
  - type (PAYING, REPORTING, required)
  - level (Integer, required)
  - segmentId (UUID, required, FK)
  - createdAt (LocalDateTime)

- **PaymentMethodDomain**
  - id (UUID, PK)
  - customerId (UUID, required, FK)
  - type (String, ENUM: 'credit_card', 'check', 'ach_credit', 'ach_debit')
  - details (String, JSON as text for H2 compatibility)
  - isPrimary (Boolean, required)
  - createdAt (LocalDateTime)
  - validated (Boolean)

- **CustomerSegmentDomain**
  - id (UUID, PK)
  - name (String, unique)
  - description (String)

- **ContactDomain**
  - id (UUID, PK)
  - customerId (UUID, required, FK)
  - firstName (String, required)
  - lastName (String, required)
  - email (String, required, unique per customer)
  - contactType (String, required, ENUM)
  - isDefault (Boolean, required)
  - createdAt (LocalDateTime)

- **CustomerNotesDomain**
  - id (UUID, PK, generated)
  - customerId (UUID, required, FK)
  - notes (String, required, max 2048)
  - createdAt (LocalDateTime)
  - updatedAt (LocalDateTime)
  - createdBy (String)

---

## 6. Database Schema (Simplified)

```sql
CREATE TABLE segment (
    id UUID PRIMARY KEY,
    name VARCHAR(128) UNIQUE NOT NULL,
    description TEXT
);

CREATE TABLE customer (
    id UUID PRIMARY KEY,
    first_name VARCHAR(128) NOT NULL,
    last_name VARCHAR(128) NOT NULL,
    address_line1 VARCHAR(255) NOT NULL,
    city VARCHAR(128) NOT NULL,
    state VARCHAR(64),
    zip_code VARCHAR(32) NOT NULL,
    address_country VARCHAR(64) NOT NULL,
    address_type VARCHAR(32), -- New: AddressType enum (BILLING, SHIPPING)
    email VARCHAR(255) UNIQUE NOT NULL,
    company_name VARCHAR(255),
    vat_number VARCHAR(64),
    segment_id UUID NOT NULL REFERENCES segment(id),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    status VARCHAR(16) NOT NULL CHECK (status IN ('ACTIVE', 'INACTIVE'))
);

CREATE TABLE customer_relationship (
    id UUID PRIMARY KEY,
    parent_id UUID NOT NULL REFERENCES customer(id),
    child_id UUID NOT NULL REFERENCES customer(id),
    type VARCHAR(16) NOT NULL CHECK (type IN ('PAYING', 'REPORTING')),
    level INTEGER NOT NULL,
    segment_id UUID NOT NULL REFERENCES segment(id),
    created_at TIMESTAMP,
    CONSTRAINT unique_relationship UNIQUE (parent_id, child_id, type),
    CONSTRAINT paying_depth CHECK (
        (type != 'PAYING') OR (level <= 2)
    )
);

CREATE TABLE payment_method (
    id UUID PRIMARY KEY,
    customer_id UUID NOT NULL REFERENCES customer(id),
    type VARCHAR(16) NOT NULL CHECK (type IN ('credit_card', 'check', 'ach_credit', 'ach_debit')),
    details TEXT NOT NULL, -- JSON as text for H2 compatibility
    is_primary BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP,
    validated BOOLEAN NOT NULL DEFAULT FALSE
);

CREATE TABLE contact (
    id UUID PRIMARY KEY,
    customer_id UUID NOT NULL REFERENCES customer(id),
    first_name VARCHAR(128) NOT NULL,
    last_name VARCHAR(128) NOT NULL,
    email VARCHAR(255) NOT NULL,
    contact_type VARCHAR(32) NOT NULL CHECK (contact_type IN ('billing', 'technical', 'notification')),
    is_default BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP,
    CONSTRAINT unique_contact_per_customer UNIQUE (customer_id, email)
);

CREATE TABLE customer_notes_domain (
    id UUID PRIMARY KEY,
    customer_id UUID NOT NULL REFERENCES customer(id),
    notes VARCHAR(2048) NOT NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    created_by VARCHAR(255)
);

CREATE TABLE address (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES customer(id) ON DELETE CASCADE,
    street VARCHAR(255) NOT NULL,
    city VARCHAR(128) NOT NULL,
    state VARCHAR(64) NOT NULL,
    zip_code VARCHAR(32) NOT NULL,
    address_country VARCHAR(64) NOT NULL,
    address_type VARCHAR(16) NOT NULL CHECK (address_type IN ('BILLING', 'SHIPPING'))
);
```

---

## 7. API Overview

- `POST /customers` – Create customer (with duplicate email check, segment assignment)
- `PUT /customers/{id}` – Update customer details
- `POST /customers/{id}/relationships` – Link customer as parent/child (with validation)
- `POST /customers/{id}/payment-methods` – Add payment method
- `GET /customers/{id}` – View customer profile
- `GET /segments` – List segments
- `POST /customers/{id}/contacts` – Add a contact
- `GET /customers/{id}/contacts` – List all contacts for a customer
- `PUT /customers/{id}/contacts/{contact_id}` – Update a contact
- `POST /customers/{id}/notes` – Add a note for a customer
- `GET /customers/{id}/notes` – List all notes for a customer
- `GET /customers/by-email?email=...` – Fetch customer by email (gRPC: `GetCustomerByEmail`)

---

## 8. Customer Notes

The Customer Management Service supports attaching notes to customer records for auditing, support, and historical tracking purposes. Each note is associated with a customer and includes metadata such as creation time and author.

### Capabilities
- **Add Note:**
  - Endpoint: `POST /customers/{id}/notes`
  - Allows users or automated processes to add a new note to a customer profile.
  - Each note includes the note text, the author (createdBy), and a timestamp.
- **Update Note:**
  - Endpoint: `PUT /customers/{id}/notes/{note_id}`
  - Allows editing the content of an existing note. The updatedAt timestamp is set on modification.
- **Get All Notes (Historical View):**
  - Endpoint: `GET /customers/{id}/notes`
  - Returns a chronological list of all notes associated with the customer, including historical notes for full auditability.

### Data Model
- **CustomerNotesDomain**
  - id (UUID, PK, generated)
  - customerId (UUID, required, FK)
  - notes (String, required, max 2048)
  - createdAt (LocalDateTime)
  - updatedAt (LocalDateTime)
  - createdBy (String)

### Usage Scenarios
- Track customer support interactions, requests, or important events.
- Maintain a historical log of changes, issues, or communications for compliance and service quality.
- Enable customer service representatives to quickly review all past notes before engaging with a customer.

---

## 9. Validation Logic

- **Email:** Unique across customers. Contact emails must be unique per customer.
- **Address:** Zip code and country validated per regex.
- **Hierarchy:**
  - **Paying**: Enforce max 2-level depth, no loops.
  - **Reporting**: No depth limit, but no circular references.
  - **Segment match**: Parent and child must belong to same segment.
- **Contact Information:**
  - Contact email must be unique per customer.
  - Contact type must be a valid enumerated value.

---

## 10. Example Customer JSON

```json
{
  "first_name": "Jane",
  "last_name": "Doe",
  "address": {
    "line1": "123 Main St",
    "city": "London",
    "state": "",
    "zip_code": "WC2N 5DU",
    "country": "UK"
  },
  "email": "<EMAIL>",
  "company_name": "Acme Corp",
  "vat_number": "GB123456789",
  "segment_id": "default-segment-uuid",
  "contacts": [
    {
      "first_name": "Jane",
      "last_name": "Doe",
      "email": "<EMAIL>",
      "contact_type": "billing",
      "is_default": true
    },
    {
      "first_name": "John",
      "last_name": "Smith",
      "email": "<EMAIL>",
      "contact_type": "technical",
      "is_default": false
    }
  ]
}
```

---

## 11. Diagram – Domain Model (UML)

```
+----------------+         +----------------------+           +--------------+
|   Customer     |<>-------| CustomerRelationship |           |   Contact    |
+----------------+         +----------------------+           +--------------+
| id             |         | id                   |           | id           |
| first_name     |         | parent_id            |           | customer_id  |
| last_name      |         | child_id             |           | first_name   |
| ...            |         | type                 |           | last_name    |
| segment_id     |         | level                |           | email        |
+----------------+         | segment_id           |           | contact_type |
        |                  +----------------------+           | is_default   |
        | 1             *  |                                  +--------------+
        |------------------|
        |
        | 1             *  +-----------------+
        |------------------|  PaymentMethod  |
        |                  +-----------------+
        |                  | id              |
        |                  | customer_id     |
        |                  | type            |
        |                  | details         |
        |                  +-----------------+
        |
        | 1             *  +----------+
        |------------------| Segment  |
                           +----------+
                           | id       |
                           | name     |
                           +----------+
```

---

## 12. Notes

- All domain models are explicitly mapped to the database schema using @Table and @AttributeOverrides where needed.
- Payment method details are stored as text for test compatibility (H2), but as JSONB in production (Postgres).
- Robust error handling and logging (SLF4J) are implemented in all service, endpoint, and mapper classes.
- Repository interfaces do not contain business logic or error handling.
- All mutation operations are audited.
- Default contact is always created from the customer's own information; additional contacts and notes can be managed via the API.

---

## 13. Migration Scripts

- Migration file `V2__add_address_type_to_customer.sql` adds the `address_type` column to the `customer` table.

---

## 14. gRPC API Changes

- The `Address` message in `customer_service.proto` now includes an `AddressType` enum field (`BILLING`, `SHIPPING`).
- The `GetCustomerByEmail` RPC method is available to fetch a customer by email.

---

## Address Table Schema

The `address` table stores multiple addresses per customer, supporting both billing and shipping types. It is mapped from the `AddressDomain` entity in the Java domain model.

**Table: avantiq.address**

| Column       | Type         | Constraints                                  | Description                       |
|--------------|--------------|----------------------------------------------|-----------------------------------|
| id           | UUID         | PRIMARY KEY, DEFAULT gen_random_uuid()       | Unique address identifier         |
| customer_id  | UUID         | NOT NULL, FK → customer(id), ON DELETE CASCADE | Owning customer                   |
| street       | VARCHAR(255) | NOT NULL                                     | Street address                    |
| city         | VARCHAR(128) | NOT NULL                                     | City                              |
| state        | VARCHAR(64)  | NOT NULL                                     | State/Province                    |
| zip_code     | VARCHAR(32)  | NOT NULL                                     | Postal/ZIP code                   |
| country      | VARCHAR(64)  | NOT NULL                                     | Country                           |
| address_type | VARCHAR(16)  | NOT NULL, CHECK (BILLING/SHIPPING)           | Address type (BILLING/SHIPPING)   |

**Foreign Keys:**
- `customer_id` references `avantiq.customer(id)` (ON DELETE CASCADE)

**Notes:**
- Each customer can have multiple addresses.
- The `address_type` column enforces valid values (`BILLING`, `SHIPPING`).
- The schema is compatible with the `AddressDomain` JPA entity and the gRPC/REST API.

---

# End of Document