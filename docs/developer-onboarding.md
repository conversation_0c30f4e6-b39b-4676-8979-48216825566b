# Developer Onboarding Guide

Welcome to the Avantiq Billing System development team! This guide will help you get up and running with the codebase quickly.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Setup](#environment-setup)
3. [Project Structure](#project-structure)
4. [Development Workflow](#development-workflow)
5. [Code Standards](#code-standards)
6. [Testing Guidelines](#testing-guidelines)
7. [Common Tasks](#common-tasks)
8. [Troubleshooting](#troubleshooting)
9. [Resources](#resources)

## Prerequisites

Before you begin, ensure you have the following installed:

- **Java 21** (LTS) - Use SDKMAN or your preferred Java version manager
- **Docker Desktop** - For PostgreSQL and containerized services
- **Git** - Version control
- **IDE** - IntelliJ IDEA (recommended) or VS Code with Java extensions
- **Postman** or **BloomRPC** - For testing gRPC endpoints

### Recommended Tools

- **SDKMAN** - Java version management
  ```bash
  curl -s "https://get.sdkman.io" | bash
  sdk install java 21.0.1-tem
  ```

- **IntelliJ IDEA Plugins**:
  - Lombok Plugin
  - Protocol Buffer Plugin
  - Spring Boot Plugin
  - CheckStyle-IDEA

## Environment Setup

### 1. Clone the Repository

```bash
git clone https://github.com/your-org/avantiq-billing.git
cd avantiq-billing
```

### 2. Set Up Environment Variables

Create a `.env` file in the project root (copy from `.env.example`):

```bash
cp .env.example .env
```

Edit `.env` with your local settings:

```bash
# Database Configuration
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your-local-password
DB_SSL_MODE=disable  # For local development

# Security Configuration
JWT_SECRET=your-development-jwt-secret-at-least-32-characters

# Optional Development Settings
SPRING_PROFILES_ACTIVE=dev
LOG_LEVEL=DEBUG
```

### 3. Start PostgreSQL

Using Docker Compose:

```bash
docker-compose up -d postgres
```

Or manually:

```bash
docker run -d \
  --name avantiq-postgres \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=your-local-password \
  -e POSTGRES_DB=postgres \
  -p 5432:5432 \
  postgres:15-alpine
```

### 4. Build the Project

```bash
# First build (downloads dependencies)
./gradlew clean build

# Generate gRPC stubs
./gradlew generateProto

# Run database migrations
./gradlew flywayMigrate
```

### 5. Run the Application

```bash
# Using Gradle
./gradlew bootRun

# Or using Java directly
java -jar build/libs/avantiq-billing-*.jar
```

The application will start on:
- gRPC port: 9090
- Management port: 8081 (health, metrics)

## Project Structure

```
avantiq-billing/
├── src/
│   ├── main/
│   │   ├── java/com/avantiq/billing/
│   │   │   ├── domain/           # Domain layer (business logic)
│   │   │   │   ├── customer/     # Customer aggregate
│   │   │   │   ├── product/      # Product aggregate
│   │   │   │   ├── payment/      # Payment aggregate
│   │   │   │   └── security/     # Security domain
│   │   │   ├── application/      # Application services (use cases)
│   │   │   └── infrastructure/   # Infrastructure layer
│   │   │       ├── grpc/         # gRPC endpoints
│   │   │       ├── persistence/  # JPA repositories
│   │   │       └── security/     # Security implementation
│   │   ├── proto/                # Protocol buffer definitions
│   │   └── resources/
│   │       ├── application.properties
│   │       └── db/migration/     # Flyway migrations
│   └── test/                     # Test sources
├── docs/                         # Documentation
├── build.gradle                  # Build configuration
└── docker-compose.yaml          # Docker services
```

### Key Architecture Concepts

1. **Hexagonal Architecture**: Domain logic is isolated from infrastructure
2. **Domain-Driven Design**: Aggregates, entities, value objects, and domain services
3. **Clean Architecture**: Dependencies point inward (infrastructure → application → domain)
4. **Multi-tenancy**: All data is isolated by tenant ID
5. **Security**: JWT-based authentication with role-based access control

## Development Workflow

### 1. Create a Feature Branch

```bash
git checkout -b feature/your-feature-name
```

### 2. Make Changes

Follow the architecture patterns:

#### Adding a New Domain Entity

1. Create the domain model in `domain/{aggregate}/model/`
2. Define the repository interface in `domain/{aggregate}/repository/`
3. Create JPA entity in `infrastructure/persistence/{aggregate}/entity/`
4. Implement repository in `infrastructure/persistence/{aggregate}/repository/`
5. Create mapper in `infrastructure/persistence/{aggregate}/mapper/`

#### Adding a New gRPC Service

1. Define the service in `src/main/proto/{service}.proto`
2. Run `./gradlew generateProto`
3. Create application service in `application/{domain}/`
4. Create gRPC endpoint in `infrastructure/grpc/{domain}/`
5. Add security annotations (`@PreAuthorize`)

### 3. Write Tests

```bash
# Run all tests
./gradlew test

# Run specific test class
./gradlew test --tests CustomerServiceTest

# Run with coverage
./gradlew test jacocoTestReport
```

### 4. Code Quality Checks

```bash
# Format code
./gradlew spotlessApply

# Run all quality checks
./gradlew check

# Individual checks
./gradlew checkstyleMain
./gradlew pmdMain
./gradlew spotbugsMain
```

### 5. Commit and Push

```bash
git add .
git commit -m "feat: Add customer search functionality"
git push origin feature/your-feature-name
```

## Code Standards

### Java Code Style

We use Google Java Style with Spotless enforcement:

- 2 spaces for indentation
- Max line length: 100 characters
- Wildcard imports are prohibited
- Javadoc required for public APIs

### Naming Conventions

- **Domain Models**: Singular nouns (Customer, Product)
- **Repositories**: {Model}Repository (CustomerRepository)
- **Services**: {Domain}Service or {Domain}ApplicationService
- **gRPC Endpoints**: {Domain}GrpcEndpoint
- **JPA Entities**: {Model}JpaEntity
- **Mappers**: {Model}Mapper

### Best Practices

1. **Dependency Injection**: Use constructor injection
2. **Logging**: Use SLF4J with meaningful messages
3. **Exceptions**: Throw domain exceptions, let infrastructure handle mapping
4. **Validation**: Validate at application service boundaries
5. **Transactions**: Use @Transactional at application service level

## Testing Guidelines

### Test Categories

1. **Unit Tests** (`*Test.java`)
   - Test domain logic and services
   - Mock external dependencies
   - Fast and isolated

2. **Integration Tests** (`*IntegrationTest.java`)
   - Test repository implementations
   - Test gRPC endpoints
   - Use test containers or H2

3. **Mapper Tests** (`*MapperTest.java`)
   - Test entity conversions
   - Verify data integrity

### Writing Tests

```java
@ExtendWith(MockitoExtension.class)
class CustomerServiceTest {
    
    @Mock
    private CustomerRepository repository;
    
    @InjectMocks
    private CustomerApplicationService service;
    
    @Test
    void shouldCreateCustomer() {
        // Given
        Customer customer = createTestCustomer();
        when(repository.save(any())).thenReturn(customer);
        
        // When
        Customer result = service.createCustomer(customer);
        
        // Then
        assertThat(result).isNotNull();
        verify(repository).save(customer);
    }
}
```

## Common Tasks

### Running Database Migrations

```bash
# Create new migration
echo "-- V{version}__{description}.sql" > src/main/resources/db/migration/V{version}__{description}.sql

# Run migrations
./gradlew flywayMigrate

# Check migration status
./gradlew flywayInfo

# Repair migrations (if needed)
./gradlew flywayRepair
```

### Testing gRPC Services

Using grpcurl:

```bash
# List services
grpcurl -plaintext localhost:9090 list

# Describe service
grpcurl -plaintext localhost:9090 describe CustomerService

# Call login endpoint
grpcurl -plaintext -d '{
  "username": "<EMAIL>",
  "password": "password"
}' localhost:9090 AuthenticationService/Login
```

### Debugging

1. **Enable Debug Logging**:
   ```properties
   logging.level.com.avantiq.billing=DEBUG
   logging.level.org.springframework.security=DEBUG
   ```

2. **Remote Debugging**:
   ```bash
   java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005 -jar app.jar
   ```

3. **Database Queries**:
   ```properties
   spring.jpa.show-sql=true
   spring.jpa.properties.hibernate.format_sql=true
   ```

## Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   # Find process using port
   lsof -i :9090
   # Kill process
   kill -9 <PID>
   ```

2. **Database Connection Failed**
   - Check PostgreSQL is running: `docker ps`
   - Verify credentials in `.env`
   - Check database exists: `docker exec -it avantiq-postgres psql -U postgres -l`

3. **Build Failures**
   ```bash
   # Clean build
   ./gradlew clean build --refresh-dependencies
   
   # Clear Gradle cache
   rm -rf ~/.gradle/caches/
   ```

4. **Proto Generation Issues**
   ```bash
   # Clean and regenerate
   ./gradlew clean generateProto
   ```

### Getting Help

- Check existing documentation in `/docs`
- Search for similar issues in the codebase
- Ask in the team Slack channel
- Create a GitHub issue for bugs

## Resources

### Internal Documentation

- [Architecture Overview](../README.md)
- [API Documentation](./api-documentation.md)
- [Database Schema](./database-schema.md)
- [Security Guide](../README.md#security)

### External Resources

- [Spring Boot Documentation](https://docs.spring.io/spring-boot/docs/current/reference/html/)
- [gRPC Java Guide](https://grpc.io/docs/languages/java/)
- [Domain-Driven Design Reference](https://www.domainlanguage.com/ddd/reference/)
- [Hexagonal Architecture](https://alistair.cockburn.us/hexagonal-architecture/)

### Development Tools

- [BloomRPC](https://github.com/bloomrpc/bloomrpc) - gRPC GUI client
- [DBeaver](https://dbeaver.io/) - Database management
- [Postman](https://www.postman.com/) - API testing (supports gRPC)

## Next Steps

1. Run the application locally
2. Explore the codebase structure
3. Try making a simple change (e.g., add a field to Customer)
4. Write and run tests
5. Submit your first pull request

Welcome to the team! 🎉