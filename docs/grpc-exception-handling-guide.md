# gRPC Exception Handling Guide

## Overview

This guide provides comprehensive documentation for gRPC exception handling in the Avantiq Billing System. The implementation follows clean architecture principles with proper separation between domain, application, and infrastructure layers.

## Architecture

### Exception Hierarchy

```
Exception
├── DomainException (domain layer)
│   ├── ValidationException
│   ├── BusinessRuleViolationException
│   ├── RepositoryException
│   └── Domain-specific exceptions (CustomerNotFoundException, etc.)
└── ApplicationException (application layer)
    ├── ResourceNotFoundException
    ├── ResourceAlreadyExistsException
    └── InvalidOperationException
```

### Key Components

1. **GlobalGrpcExceptionHandler** (`@GrpcAdvice`)
   - Centralized exception handling for all gRPC services
   - Maps domain/application exceptions to appropriate gRPC status codes
   - Adds metadata with error codes and details

2. **GrpcExceptionHandler** (utility class)
   - Static helper methods for consistent error responses
   - Metadata enrichment with error details

3. **Error Propagation Flow**
   ```
   Domain Layer → Application Layer → Infrastructure Layer → gRPC Response
   ```

## Implementation Details

### Exception Mapping

| Exception Type | gRPC Status | Use Case |
|---|---|---|
| ResourceNotFoundException | NOT_FOUND | Resource doesn't exist |
| ResourceAlreadyExistsException | ALREADY_EXISTS | Duplicate resource |
| ValidationException | INVALID_ARGUMENT | Input validation failures |
| InvalidOperationException | FAILED_PRECONDITION | Business rule violations |
| IllegalArgumentException | INVALID_ARGUMENT | Invalid parameters |
| SecurityException | PERMISSION_DENIED | Access control violations |
| Exception | INTERNAL | Unexpected errors |

### Code Example

```java
@GrpcAdvice
public class GlobalGrpcExceptionHandler {
    
    @GrpcExceptionHandler(ResourceNotFoundException.class)
    public StatusRuntimeException handleResourceNotFoundException(ResourceNotFoundException e) {
        Metadata metadata = new Metadata();
        metadata.put(ERROR_CODE_KEY, e.getErrorCode());
        metadata.put(ERROR_DETAILS_KEY, e.getMessage());
        
        return Status.NOT_FOUND
            .withDescription(e.getMessage())
            .asRuntimeException(metadata);
    }
}
```

## Client-Side Error Handling

### Java Client Example

```java
try {
    CustomerResponse response = stub.getCustomer(request);
} catch (StatusRuntimeException e) {
    Status status = e.getStatus();
    Metadata metadata = e.getTrailers();
    
    switch (status.getCode()) {
        case NOT_FOUND:
            // Handle resource not found
            String errorCode = metadata.get(ERROR_CODE_KEY);
            break;
        case INVALID_ARGUMENT:
            // Handle validation error
            break;
        default:
            // Handle other errors
    }
}
```

### Error Response Format

```json
{
    "code": "NOT_FOUND",
    "description": "Customer with ID 123 not found",
    "metadata": {
        "error-code": "CUSTOMER_NOT_FOUND",
        "error-details": "Customer with ID 123 not found"
    }
}
```

## Best Practices

1. **Layer Separation**
   - Domain exceptions for business logic violations
   - Application exceptions for use case failures
   - Let infrastructure layer handle mapping to gRPC

2. **Consistent Error Codes**
   - Use ErrorCode enum for standardized codes
   - Include error codes in metadata for client handling

3. **Descriptive Messages**
   - Provide clear, actionable error messages
   - Include relevant context (IDs, field names)

4. **Security Considerations**
   - Don't expose internal implementation details
   - Log full errors server-side, return sanitized messages

5. **Testing**
   - Test both success and error scenarios
   - Verify proper exception mapping
   - Test metadata propagation

## Testing Checklist

- [ ] Unit tests for exception scenarios in services
- [ ] Integration tests for gRPC error responses
- [ ] Verify metadata is properly propagated
- [ ] Test all mapped exception types
- [ ] Verify error messages are appropriate
- [ ] Test security exception handling

## Migration from Legacy Error Handling

If migrating from try-catch blocks in endpoints:

1. Remove try-catch from gRPC endpoints
2. Let exceptions propagate to GlobalGrpcExceptionHandler
3. Ensure proper exception types are thrown from services
4. Update clients to handle standardized error responses

## Monitoring and Logging

- All exceptions are logged with appropriate levels
- Security exceptions logged as errors
- Business exceptions logged as warnings
- Metrics can be added to exception handlers for monitoring