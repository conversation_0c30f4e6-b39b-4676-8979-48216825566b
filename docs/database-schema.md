# Database Schema Documentation

## Overview

The Avantiq Billing System uses PostgreSQL as its primary database with a multi-tenant architecture. All tables include tenant isolation and audit fields for tracking changes.

## Core Design Principles

1. **Multi-Tenant Isolation**: Every business entity table includes `tenant_id`
2. **Audit Trail**: All tables include created/modified timestamps and user tracking
3. **UUID Primary Keys**: Using UUIDs for better distribution and security
4. **Optimistic Locking**: Version fields on aggregate roots
5. **Performance Indexes**: Strategic indexes for common query patterns

## Schema Diagram

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│    tenants      │     │     users       │     │     roles       │
├─────────────────┤     ├─────────────────┤     ├─────────────────┤
│ tenant_id (PK)  │<────│ user_id (PK)    │     │ role_id (PK)    │
│ name            │     │ tenant_id (FK)  │     │ name            │
│ domain          │     │ username        │     │ description     │
│ status          │     │ email           │     └─────────────────┘
│ created_at      │     │ password_hash   │              │
└─────────────────┘     │ status          │              │
                        └─────────────────┘              │
                                 │                       │
                                 ├───────────────────────┘
                                 │
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   customers     │     │customer_segments│     │ user_segments   │
├─────────────────┤     ├─────────────────┤     ├─────────────────┤
│ customer_id(PK) │     │ segment_id (PK) │<────│user_segment_id  │
│ tenant_id (FK)  │     │ tenant_id (FK)  │     │ user_id (FK)    │
│ segment_id (FK) │────>│ name            │     │ segment_id (FK) │
│ first_name      │     │ description     │     │ tenant_id (FK)  │
│ last_name       │     └─────────────────┘     └─────────────────┘
│ email           │
│ status          │
└─────────────────┘
```

## Tables Reference

### Core Tables

#### tenants
Multi-tenant system support
```sql
CREATE TABLE tenants (
    tenant_id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    domain VARCHAR(255) UNIQUE,
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

### Customer Domain

#### customers
Core customer information
```sql
CREATE TABLE customers (
    customer_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id BIGINT NOT NULL REFERENCES tenants(tenant_id),
    segment_id UUID REFERENCES customer_segments(segment_id),
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL,
    company_name VARCHAR(255),
    vat_number VARCHAR(50),
    country VARCHAR(2),
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    version BIGINT NOT NULL DEFAULT 0,
    UNIQUE(email, tenant_id)
);

-- Indexes
CREATE INDEX idx_customers_email_tenant ON customers(email, tenant_id);
CREATE INDEX idx_customers_segment_tenant ON customers(segment_id, tenant_id);
CREATE INDEX idx_customers_status_tenant ON customers(status, tenant_id);
```

#### addresses
Customer addresses (value objects)
```sql
CREATE TABLE addresses (
    address_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES customers(customer_id) ON DELETE CASCADE,
    type VARCHAR(20) NOT NULL,
    street VARCHAR(255) NOT NULL,
    city VARCHAR(100) NOT NULL,
    state VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(2) NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_addresses_customer ON addresses(customer_id);
CREATE INDEX idx_addresses_type ON addresses(customer_id, type);
```

#### contacts
Customer contact persons
```sql
CREATE TABLE contacts (
    contact_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES customers(customer_id) ON DELETE CASCADE,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    type VARCHAR(20) NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_contacts_customer ON contacts(customer_id);
CREATE INDEX idx_contacts_email ON contacts(email);
```

#### customer_segments
Customer segmentation for pricing and access control
```sql
CREATE TABLE customer_segments (
    segment_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id BIGINT NOT NULL REFERENCES tenants(tenant_id),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(name, tenant_id)
);

-- Indexes
CREATE INDEX idx_segments_tenant ON customer_segments(tenant_id);
CREATE INDEX idx_segments_name ON customer_segments(name);
```

### Product Domain

#### products
Product catalog
```sql
CREATE TABLE products (
    product_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id BIGINT NOT NULL REFERENCES tenants(tenant_id),
    sku VARCHAR(100) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    family_id UUID REFERENCES product_families(family_id),
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    version BIGINT NOT NULL DEFAULT 0,
    UNIQUE(sku, tenant_id)
);

-- Indexes
CREATE INDEX idx_products_sku_tenant ON products(sku, tenant_id);
CREATE INDEX idx_products_status_tenant ON products(status, tenant_id);
CREATE INDEX idx_products_family ON products(family_id);
CREATE INDEX idx_products_type_tenant ON products(type, tenant_id);
```

#### price_books
Segment-specific pricing containers
```sql
CREATE TABLE price_books (
    price_book_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id BIGINT NOT NULL REFERENCES tenants(tenant_id),
    name VARCHAR(255) NOT NULL,
    segment_id UUID REFERENCES customer_segments(segment_id),
    currency VARCHAR(3) NOT NULL,
    start_date DATE,
    end_date DATE,
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(name, tenant_id)
);

-- Indexes
CREATE INDEX idx_price_books_segment ON price_books(segment_id);
CREATE INDEX idx_price_books_status ON price_books(status);
CREATE INDEX idx_price_books_dates ON price_books(start_date, end_date);
```

#### prices
Product pricing details
```sql
CREATE TABLE prices (
    price_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id BIGINT NOT NULL REFERENCES tenants(tenant_id),
    product_id UUID NOT NULL REFERENCES products(product_id),
    price_book_id UUID NOT NULL REFERENCES price_books(price_book_id),
    billing_frequency VARCHAR(20) NOT NULL,
    currency VARCHAR(3) NOT NULL,
    unit_of_measure VARCHAR(50),
    charge_type VARCHAR(20) NOT NULL,
    charge_strategy VARCHAR(20) NOT NULL,
    billing_strategy VARCHAR(20) NOT NULL,
    charge JSONB NOT NULL,
    proration_policy VARCHAR(20),
    is_default BOOLEAN DEFAULT FALSE,
    is_grandfathered BOOLEAN DEFAULT FALSE,
    version BIGINT NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(product_id, price_book_id)
);

-- Indexes
CREATE INDEX idx_prices_product ON prices(product_id);
CREATE INDEX idx_prices_price_book ON prices(price_book_id);
CREATE INDEX idx_prices_tenant_active ON prices(tenant_id, is_default);
```

### Security Domain

#### users
System users for authentication
```sql
CREATE TABLE users (
    user_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    tenant_id BIGINT NOT NULL REFERENCES tenants(tenant_id),
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING_ACTIVATION',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    last_modified_by VARCHAR(255),
    version BIGINT NOT NULL DEFAULT 0
);

-- Indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_tenant_id ON users(tenant_id);
CREATE INDEX idx_users_status ON users(status);
```

#### roles
System roles for RBAC
```sql
CREATE TABLE roles (
    role_id BIGSERIAL PRIMARY KEY,
    name VARCHAR(30) NOT NULL UNIQUE,
    description VARCHAR(255),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Default roles
INSERT INTO roles (name, description) VALUES
('TENANT_ADMIN', 'Full access to all tenant data'),
('SEGMENT_ADMIN', 'Access to specific segments'),
('USER', 'Read/write access to assigned segments'),
('READONLY', 'Read-only access');
```

#### user_roles
User-role associations
```sql
CREATE TABLE user_roles (
    user_role_id BIGSERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    role_id BIGINT NOT NULL REFERENCES roles(role_id),
    assigned_by UUID REFERENCES users(user_id),
    assigned_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, role_id)
);

-- Indexes
CREATE INDEX idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX idx_user_roles_role_id ON user_roles(role_id);
```

#### user_segments
User access to customer segments
```sql
CREATE TABLE user_segments (
    user_segment_id BIGSERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    segment_id UUID NOT NULL REFERENCES customer_segments(segment_id),
    tenant_id BIGINT NOT NULL REFERENCES tenants(tenant_id),
    assigned_by UUID REFERENCES users(user_id),
    assigned_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, segment_id)
);

-- Indexes
CREATE INDEX idx_user_segments_user_id ON user_segments(user_id);
CREATE INDEX idx_user_segments_segment_id ON user_segments(segment_id);
CREATE INDEX idx_user_segments_tenant_id ON user_segments(tenant_id);
```

### Payment Domain

#### payment_methods
Customer payment methods
```sql
CREATE TABLE payment_methods (
    payment_method_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES customers(customer_id),
    tenant_id BIGINT NOT NULL REFERENCES tenants(tenant_id),
    type VARCHAR(50) NOT NULL,
    details JSONB NOT NULL,
    is_primary BOOLEAN DEFAULT FALSE,
    is_validated BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_payment_methods_customer ON payment_methods(customer_id);
CREATE INDEX idx_payment_methods_tenant ON payment_methods(tenant_id);
CREATE INDEX idx_payment_methods_primary ON payment_methods(customer_id, is_primary);
```

## Performance Considerations

### Indexes Strategy

1. **Primary Keys**: All use B-tree indexes by default
2. **Foreign Keys**: Indexed for join performance
3. **Unique Constraints**: Create implicit indexes
4. **Common Queries**: Strategic indexes on frequently filtered columns
5. **Composite Indexes**: For multi-column filters (e.g., email + tenant_id)

### Partitioning Strategy

For large-scale deployments, consider partitioning:
- Customers table by tenant_id
- Products table by tenant_id
- Audit/log tables by created_at

### Connection Pooling

Recommended settings:
```properties
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
```

## Migration Management

Using Flyway for database migrations:
- Migrations in `src/main/resources/db/migration/`
- Naming: `V{version}__{description}.sql`
- Version tracking in `flyway_schema_history` table

## Backup and Recovery

Recommended backup strategy:
1. Daily full backups
2. Continuous WAL archiving
3. Point-in-time recovery capability
4. Regular backup restoration tests