# Domain-Driven Design Structure Plan

## Current Structure Issues
The current project structure has several issues:

1. Domain entities are in the adapter.persistence.entity package, tightly coupled to JPA
2. No clear separation between domain model and persistence model
3. Service layer directly uses persistence entities
4. No clear boundaries between different bounded contexts
5. No application layer to orchestrate use cases
6. Test packages don't align with main packages (e.g., data vs adapter.persistence)

## Proposed DDD Structure

### Core Principles
- Separate domain model from infrastructure concerns
- Define clear bounded contexts
- Use hexagonal architecture (ports and adapters)
- Implement proper separation of concerns

### Package Structure

```
com.avantiq.billing
├── application                  # Application services/use cases
│   ├── customer                 # Customer-related use cases
│   ├── payment                  # Payment-related use cases
│   └── common                   # Shared application services
├── domain                       # Domain model
│   ├── customer                 # Customer aggregate
│   │   ├── model                # Domain entities
│   │   ├── repository           # Repository interfaces (ports)
│   │   ├── service              # Domain services
│   │   └── event                # Domain events
│   ├── payment                  # Payment aggregate
│   │   ├── model
│   │   ├── repository
│   │   ├── service
│   │   └── event
│   └── common                   # Shared domain concepts
├── infrastructure               # Infrastructure adapters
│   ├── persistence              # Database adapters
│   │   ├── customer             # Customer persistence
│   │   │   ├── entity           # JPA entities
│   │   │   ├── repository       # JPA repository implementations
│   │   │   └── mapper           # Mappers between domain and JPA
│   │   └── payment              # Payment persistence
│   ├── grpc                     # gRPC adapters
│   │   ├── customer             # Customer gRPC endpoints
│   │   ├── payment              # Payment gRPC endpoints
│   │   └── mapper               # Mappers between domain and gRPC
│   └── common                   # Shared infrastructure
└── config                       # Application configuration
```

## Migration Plan

### Domain Layer
1. Create domain model classes that are free from infrastructure concerns
   - Customer, Address, CustomerSegment, etc.
   - These should be POJOs without JPA annotations

2. Define repository interfaces in the domain layer
   - CustomerRepository, CustomerSegmentRepository, etc.
   - These should define methods without Spring Data JPA

### Infrastructure Layer
1. Keep JPA entities in the infrastructure.persistence package
   - Rename to avoid confusion with domain entities (e.g., CustomerJpaEntity)

2. Implement repository interfaces from the domain layer
   - Use Spring Data JPA repositories internally
   - Map between domain entities and JPA entities

3. Move gRPC endpoints to infrastructure.grpc package
   - Update to use application services instead of directly using repositories

### Application Layer
1. Create application services that orchestrate use cases
   - CustomerApplicationService, PaymentApplicationService, etc.
   - These should use domain repositories and services

2. Move business logic from current services to appropriate layers
   - Domain logic to domain services
   - Use case orchestration to application services

### Test Structure
1. Align test packages with main packages
   - domain.customer.service
   - infrastructure.persistence.customer
   - infrastructure.grpc.customer
   - application.customer

## Implementation Steps
1. Create the new package structure
2. Create domain model classes
3. Create repository interfaces
4. Implement infrastructure adapters
5. Create application services
6. Update gRPC endpoints
7. Update tests to match the new structure