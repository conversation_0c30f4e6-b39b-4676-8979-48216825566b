services:
  db:
    image: postgres:17-alpine
    container_name: db
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "5432:5432"
    volumes:
      - db-data:/var/lib/postgresql/data
      - ./ssl/server.crt:/var/lib/postgresql/ssl/server.crt:ro
      - ./ssl/server.key:/var/lib/postgresql/ssl/server.key:ro
      - ./ssl/postgresql.conf:/etc/postgresql/postgresql.conf:ro
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    healthcheck:
      test: [ "CMD", "pg_isready", "-U", "postgres" ]
      interval: 10s
      timeout: 5s
      retries: 5
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: app
    depends_on:
      db:
        condition: service_healthy
    environment:
      SPRING_DATASOURCE_URL: **************************************************
      SPRING_DATASOURCE_USERNAME: ${POSTGRES_USER:-postgres}
      SPRING_DATASOURCE_PASSWORD: ${POSTGRES_PASSWORD}
      SPRING_JPA_HIBERNATE_DDL_AUTO: ${JPA_DDL_AUTO:-validate}
      JWT_SECRET: ${JWT_SECRET}
      GRPC_SERVER_PORT: 9090
    ports:
      - "8080:8080"
      - "9090:9090"
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8080/actuator/health" ]
      interval: 10s
      timeout: 5s
      retries: 5
volumes:
  db-data:
