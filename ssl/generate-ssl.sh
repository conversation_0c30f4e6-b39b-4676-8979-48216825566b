#!/bin/bash
# Generate self-signed SSL certificates for PostgreSQL development

set -e

echo "Generating SSL certificates for PostgreSQL..."

# Create ssl directory if it doesn't exist
mkdir -p ssl

# Generate self-signed certificate
openssl req -new -x509 -days 365 -nodes -text -out ssl/server.crt \
  -keyout ssl/server.key -subj "/CN=db"

# Set proper permissions
chmod 600 ssl/server.key
chmod 644 ssl/server.crt

echo "SSL certificates generated successfully!"
echo "Files created:"
echo "  - ssl/server.crt (certificate)"
echo "  - ssl/server.key (private key)"
echo ""
echo "These files are excluded from git for security."
echo "Run this script on each development machine."