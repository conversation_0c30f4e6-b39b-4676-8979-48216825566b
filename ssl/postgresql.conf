# PostgreSQL SSL Configuration
ssl = on
ssl_cert_file = '/var/lib/postgresql/ssl/server.crt'
ssl_key_file = '/var/lib/postgresql/ssl/server.key'
ssl_ciphers = 'HIGH:MEDIUM:+3DES:!aNULL'
ssl_prefer_server_ciphers = on

# Connection settings
listen_addresses = '*'
port = 5432

# Memory settings (can be adjusted)
shared_buffers = 128MB
effective_cache_size = 256MB

# Logging
log_statement = 'none'
log_min_duration_statement = 1000