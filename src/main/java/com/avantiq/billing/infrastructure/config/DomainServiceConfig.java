package com.avantiq.billing.infrastructure.config;

import com.avantiq.billing.domain.customer.repository.ContactRepository;
import com.avantiq.billing.domain.customer.repository.CustomerNoteRepository;
import com.avantiq.billing.domain.customer.repository.CustomerRelationshipRepository;
import com.avantiq.billing.domain.customer.repository.CustomerRepository;
import com.avantiq.billing.domain.customer.repository.CustomerSegmentRepository;
import com.avantiq.billing.domain.customer.service.impl.ContactServiceImpl;
import com.avantiq.billing.domain.customer.service.impl.CustomerDomainServiceImpl;
import com.avantiq.billing.domain.customer.service.impl.CustomerNoteServiceImpl;
import com.avantiq.billing.domain.customer.service.impl.CustomerRelationshipServiceImpl;
import com.avantiq.billing.domain.customer.service.impl.CustomerSegmentServiceImpl;
import com.avantiq.billing.domain.customer.service.impl.CustomerServiceImpl;
import com.avantiq.billing.domain.customer.service.interfaces.ContactService;
import com.avantiq.billing.domain.customer.service.interfaces.CustomerDomainService;
import com.avantiq.billing.domain.customer.service.interfaces.CustomerNoteService;
import com.avantiq.billing.domain.customer.service.interfaces.CustomerRelationshipService;
import com.avantiq.billing.domain.customer.service.interfaces.CustomerSegmentService;
import com.avantiq.billing.domain.customer.service.interfaces.CustomerService;
import com.avantiq.billing.domain.product.service.impl.ProductDomainServiceImpl;
import com.avantiq.billing.domain.product.service.interfaces.ProductDomainService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration class for registering domain services as Spring beans. This keeps the domain layer
 * framework-agnostic while still using Spring's IoC container.
 */
@Configuration
public class DomainServiceConfig {

  /**
   * Registers the CustomerService domain service as a Spring bean.
   *
   * @param customerRepository the customer repository dependency
   * @return the configured CustomerService instance
   */
  @Bean
  public CustomerService customerService(CustomerRepository customerRepository) {
    return new CustomerServiceImpl(customerRepository);
  }

  /**
   * Registers the CustomerSegmentService domain service as a Spring bean.
   *
   * @param customerSegmentRepository the customer segment repository dependency
   * @return the configured CustomerSegmentService instance
   */
  @Bean
  public CustomerSegmentService customerSegmentService(
      CustomerSegmentRepository customerSegmentRepository) {
    return new CustomerSegmentServiceImpl(customerSegmentRepository);
  }

  /**
   * Registers the ContactService domain service as a Spring bean.
   *
   * @param contactRepository the contact repository dependency
   * @return the configured ContactService instance
   */
  @Bean
  public ContactService contactService(ContactRepository contactRepository) {
    return new ContactServiceImpl(contactRepository);
  }

  /**
   * Registers the CustomerRelationshipService domain service as a Spring bean.
   *
   * @param customerRelationshipRepository the customer relationship repository dependency
   * @return the configured CustomerRelationshipService instance
   */
  @Bean
  public CustomerRelationshipService customerRelationshipService(
      CustomerRelationshipRepository customerRelationshipRepository) {
    return new CustomerRelationshipServiceImpl(customerRelationshipRepository);
  }

  /**
   * Registers the CustomerNoteService domain service as a Spring bean.
   *
   * @param customerNoteRepository the customer note repository dependency
   * @return the configured CustomerNoteService instance
   */
  @Bean
  public CustomerNoteService customerNoteService(CustomerNoteRepository customerNoteRepository) {
    return new CustomerNoteServiceImpl(customerNoteRepository);
  }

  /**
   * Registers the CustomerDomainService domain service as a Spring bean.
   *
   * @return the configured CustomerDomainService instance
   */
  @Bean
  public CustomerDomainService customerDomainService() {
    return new CustomerDomainServiceImpl();
  }

  /**
   * Registers the ProductDomainService domain service as a Spring bean.
   *
   * @return the configured ProductDomainService instance
   */
  @Bean
  public ProductDomainService productDomainService() {
    return new ProductDomainServiceImpl();
  }
}
