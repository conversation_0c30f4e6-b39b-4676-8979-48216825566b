package com.avantiq.billing.infrastructure.persistence.customer.mapper;

import com.avantiq.billing.domain.customer.model.CustomerRelationship;
import com.avantiq.billing.infrastructure.persistence.customer.entity.CustomerRelationshipJpaEntity;
import java.time.LocalDateTime;
import org.springframework.stereotype.Component;

/**
 * Mapper for converting between CustomerRelationship domain model and
 * CustomerRelationshipJpaEntity.
 */
@Component
public class CustomerRelationshipMapper {

  public CustomerRelationship toDomain(CustomerRelationshipJpaEntity entity) {
    if (entity == null) {
      return null;
    }

    return CustomerRelationship.builder()
        .id(entity.getId())
        .parentCustomerId(entity.getParentId())
        .childCustomerId(entity.getChildId())
        .type(mapFromJpaType(entity.getType()))
        .level(entity.getLevel())
        .tenantId(entity.getTenantId())
        .segmentId(entity.getSegmentId())
        .createdAt(entity.getCreatedAt())
        .createdBy(null) // Entity doesn't have this field
        .build();
  }

  public CustomerRelationshipJpaEntity toEntity(CustomerRelationship domain) {
    if (domain == null) {
      return null;
    }

    CustomerRelationshipJpaEntity entity = new CustomerRelationshipJpaEntity();
    entity.setId(domain.getId());
    entity.setParentId(domain.getParentCustomerId());
    entity.setChildId(domain.getChildCustomerId());
    entity.setType(mapToJpaType(domain.getType()));
    entity.setLevel(domain.getLevel() != null ? domain.getLevel() : 1);
    entity.setSegmentId(domain.getSegmentId());
    entity.setTenantId(domain.getTenantId());
    entity.setCreatedAt(
        domain.getCreatedAt() != null ? domain.getCreatedAt() : LocalDateTime.now());

    return entity;
  }

  public void updateEntity(CustomerRelationshipJpaEntity entity, CustomerRelationship domain) {
    if (entity == null || domain == null) {
      return;
    }

    entity.setType(mapToJpaType(domain.getType()));
    entity.setLevel(domain.getLevel() != null ? domain.getLevel() : entity.getLevel());
    entity.setSegmentId(domain.getSegmentId());
  }

  private CustomerRelationship.RelationshipType mapFromJpaType(
      CustomerRelationshipJpaEntity.RelationshipType jpaType) {
    if (jpaType == null) {
      return CustomerRelationship.RelationshipType.REPORTING; // Default
    }

    return switch (jpaType) {
      case PAYING -> CustomerRelationship.RelationshipType.PAYING;
      case REPORTING -> CustomerRelationship.RelationshipType.REPORTING;
    };
  }

  private CustomerRelationshipJpaEntity.RelationshipType mapToJpaType(
      CustomerRelationship.RelationshipType domainType) {
    if (domainType == null) {
      return CustomerRelationshipJpaEntity.RelationshipType.REPORTING; // Default
    }

    return switch (domainType) {
      case PAYING -> CustomerRelationshipJpaEntity.RelationshipType.PAYING;
      case REPORTING -> CustomerRelationshipJpaEntity.RelationshipType.REPORTING;
    };
  }
}
