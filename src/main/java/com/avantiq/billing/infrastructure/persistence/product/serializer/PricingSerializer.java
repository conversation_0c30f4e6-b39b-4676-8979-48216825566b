package com.avantiq.billing.infrastructure.persistence.product.serializer;

import com.avantiq.billing.domain.product.model.Price;
import com.avantiq.billing.domain.product.valueobject.Pricing;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.stereotype.Component;

/** Serializes and deserializes Pricing objects to/from JSON for database storage. */
@Component
public class PricingSerializer {

  private final ObjectMapper objectMapper;

  public PricingSerializer() {
    this.objectMapper = createObjectMapper();
  }

  private ObjectMapper createObjectMapper() {
    ObjectMapper mapper = new ObjectMapper();

    // Register Java time module
    mapper.registerModule(new JavaTimeModule());

    // Polymorphic type handling is now configured via annotations on the Pricing interface
    return mapper;
  }

  /**
   * Serializes a Pricing object to JSON string.
   *
   * @param pricing the pricing object to serialize
   * @return JSON string representation
   * @throws IllegalArgumentException if serialization fails
   */
  public String serialize(Pricing pricing) {
    if (pricing == null) {
      return null;
    }

    try {
      return objectMapper.writeValueAsString(pricing);
    } catch (JsonProcessingException e) {
      throw new IllegalArgumentException("Failed to serialize pricing: " + e.getMessage(), e);
    }
  }

  /**
   * Deserializes a JSON string to a Pricing object.
   *
   * @param json the JSON string
   * @param strategy the expected pricing strategy (for validation)
   * @return the deserialized Pricing object
   * @throws IllegalArgumentException if deserialization fails or strategy mismatch
   */
  public Pricing deserialize(String json, Price.PriceStrategy strategy) {
    if (json == null || json.trim().isEmpty()) {
      return null;
    }

    try {
      Pricing pricing = objectMapper.readValue(json, Pricing.class);

      // Validate that the deserialized pricing matches the expected strategy
      if (pricing != null && pricing.getStrategy() != strategy) {
        throw new IllegalArgumentException(
            String.format(
                "Pricing strategy mismatch. Expected: %s, Got: %s",
                strategy, pricing.getStrategy()));
      }

      // Validate the pricing configuration
      if (pricing != null) {
        pricing.validate();
      }

      return pricing;
    } catch (JsonProcessingException e) {
      throw new IllegalArgumentException("Failed to deserialize pricing: " + e.getMessage(), e);
    }
  }

  /**
   * Deserializes a JSON string to a Pricing object without strategy validation.
   *
   * @param json the JSON string
   * @return the deserialized Pricing object
   * @throws IllegalArgumentException if deserialization fails
   */
  public Pricing deserialize(String json) {
    if (json == null || json.trim().isEmpty()) {
      return null;
    }

    try {
      Pricing pricing = objectMapper.readValue(json, Pricing.class);

      // Validate the pricing configuration
      if (pricing != null) {
        pricing.validate();
      }

      return pricing;
    } catch (JsonProcessingException e) {
      throw new IllegalArgumentException("Failed to deserialize pricing: " + e.getMessage(), e);
    }
  }
}
