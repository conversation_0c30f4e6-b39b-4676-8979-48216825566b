package com.avantiq.billing.infrastructure.persistence.security.entity;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

/**
 * JPA entity for Permission in the security domain. Represents individual permissions that can be
 * granted to roles.
 */
@Entity
@Table(
    name = "permissions",
    indexes = {@Index(name = "idx_permissions_name", columnList = "name")})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class PermissionJpaEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "permission_id")
  private Long permissionId;

  @Enumerated(EnumType.STRING)
  @Column(name = "name", unique = true, nullable = false, length = 50)
  private PermissionTypeEntity name;

  @Column(name = "description", length = 255)
  private String description;

  @Column(name = "resource", length = 100)
  private String resource;

  @Column(name = "action", length = 50)
  private String action;

  @CreatedDate
  @Column(name = "created_at", nullable = false, updatable = false)
  private LocalDateTime createdAt;

  @LastModifiedDate
  @Column(name = "last_modified_at", nullable = false)
  private LocalDateTime lastModifiedAt;

  @OneToMany(mappedBy = "permission", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  private Set<RolePermissionJpaEntity> rolePermissions;

  /** Permission type enumeration for JPA mapping. */
  public enum PermissionTypeEntity {
    READ_ALL_TENANT_DATA,
    WRITE_ALL_TENANT_DATA,
    READ_SEGMENT_DATA,
    WRITE_SEGMENT_DATA,
    MANAGE_USERS,
    MANAGE_SEGMENT_USERS,
    MANAGE_SEGMENTS
  }
}
