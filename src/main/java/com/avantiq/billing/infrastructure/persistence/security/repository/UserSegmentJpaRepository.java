package com.avantiq.billing.infrastructure.persistence.security.repository;

import com.avantiq.billing.infrastructure.persistence.security.entity.UserSegmentJpaEntity;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/** JPA repository for UserSegmentJpaEntity. Manages user-segment associations. */
@Repository
public interface UserSegmentJpaRepository extends JpaRepository<UserSegmentJpaEntity, Long> {

  /** Find all segment assignments for a user. */
  @Query("SELECT us FROM UserSegmentJpaEntity us WHERE us.user.userId = :userId")
  List<UserSegmentJpaEntity> findByUserId(@Param("userId") UUID userId);

  /** Delete all segment assignments for a user. */
  @Modifying
  @Query("DELETE FROM UserSegmentJpaEntity us WHERE us.user.userId = :userId")
  void deleteByUserId(@Param("userId") UUID userId);

  /** Check if user has access to a specific segment. */
  @Query(
      "SELECT COUNT(us) > 0 FROM UserSegmentJpaEntity us WHERE us.user.userId = :userId AND us.segmentId = :segmentId")
  boolean existsByUserIdAndSegmentId(
      @Param("userId") UUID userId, @Param("segmentId") UUID segmentId);

  /** Find all segment assignments for a user in a tenant. */
  @Query(
      "SELECT us FROM UserSegmentJpaEntity us WHERE us.user.userId = :userId AND us.tenantId = :tenantId")
  List<UserSegmentJpaEntity> findByUserIdAndTenantId(
      @Param("userId") UUID userId, @Param("tenantId") Long tenantId);
}
