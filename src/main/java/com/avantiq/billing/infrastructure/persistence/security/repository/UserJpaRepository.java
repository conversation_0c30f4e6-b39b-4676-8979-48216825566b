package com.avantiq.billing.infrastructure.persistence.security.repository;

import com.avantiq.billing.infrastructure.persistence.security.entity.UserJpaEntity;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * JPA repository for UserJpaEntity with tenant-aware queries. Provides data access methods for user
 * management with tenant isolation.
 */
@Repository
public interface UserJpaRepository extends JpaRepository<UserJpaEntity, UUID> {

  /** Find user by username with tenant isolation. */
  @Query("SELECT u FROM UserJpaEntity u WHERE u.username = :username AND u.tenantId = :tenantId")
  Optional<UserJpaEntity> findByUsernameAndTenantId(
      @Param("username") String username, @Param("tenantId") Long tenantId);

  /** Find user by email with tenant isolation. */
  @Query("SELECT u FROM UserJpaEntity u WHERE u.email = :email AND u.tenantId = :tenantId")
  Optional<UserJpaEntity> findByEmailAndTenantId(
      @Param("email") String email, @Param("tenantId") Long tenantId);

  /** Find user by username (global search for authentication). */
  Optional<UserJpaEntity> findByUsername(String username);

  /** Find user by email (global search for authentication). */
  Optional<UserJpaEntity> findByEmail(String email);

  /** Find user with roles and segments eagerly loaded. */
  @Query(
      "SELECT DISTINCT u FROM UserJpaEntity u "
          + "LEFT JOIN FETCH u.userRoles ur "
          + "LEFT JOIN FETCH ur.role "
          + "LEFT JOIN FETCH u.userSegments us "
          + "WHERE u.userId = :userId AND u.tenantId = :tenantId")
  Optional<UserJpaEntity> findByIdWithRolesAndSegments(
      @Param("userId") UUID userId, @Param("tenantId") Long tenantId);

  /** Find all users in a tenant with pagination support. */
  @Query("SELECT u FROM UserJpaEntity u WHERE u.tenantId = :tenantId ORDER BY u.username")
  List<UserJpaEntity> findAllByTenantId(@Param("tenantId") Long tenantId);

  /** Find active users in a tenant. */
  @Query(
      "SELECT u FROM UserJpaEntity u WHERE u.tenantId = :tenantId AND u.status = 'ACTIVE' ORDER BY u.username")
  List<UserJpaEntity> findActiveUsersByTenantId(@Param("tenantId") Long tenantId);

  /** Find users by segment assignment. */
  @Query(
      "SELECT DISTINCT u FROM UserJpaEntity u "
          + "JOIN u.userSegments us "
          + "WHERE us.segmentId = :segmentId AND u.tenantId = :tenantId")
  List<UserJpaEntity> findBySegmentIdAndTenantId(
      @Param("segmentId") UUID segmentId, @Param("tenantId") Long tenantId);

  /** Find users with specific role in tenant. */
  @Query(
      "SELECT DISTINCT u FROM UserJpaEntity u "
          + "JOIN u.userRoles ur "
          + "JOIN ur.role r "
          + "WHERE r.name = :roleName AND u.tenantId = :tenantId")
  List<UserJpaEntity> findByRoleAndTenantId(
      @Param("roleName") String roleName, @Param("tenantId") Long tenantId);

  /** Check if username exists in tenant. */
  @Query(
      "SELECT COUNT(u) > 0 FROM UserJpaEntity u WHERE u.username = :username AND u.tenantId = :tenantId")
  boolean existsByUsernameAndTenantId(
      @Param("username") String username, @Param("tenantId") Long tenantId);

  /** Check if email exists in tenant. */
  @Query(
      "SELECT COUNT(u) > 0 FROM UserJpaEntity u WHERE u.email = :email AND u.tenantId = :tenantId")
  boolean existsByEmailAndTenantId(@Param("email") String email, @Param("tenantId") Long tenantId);

  /** Count users in tenant. */
  @Query("SELECT COUNT(u) FROM UserJpaEntity u WHERE u.tenantId = :tenantId")
  long countByTenantId(@Param("tenantId") Long tenantId);

  /** Count active users in tenant. */
  @Query(
      "SELECT COUNT(u) FROM UserJpaEntity u WHERE u.tenantId = :tenantId AND u.status = 'ACTIVE'")
  long countActiveUsersByTenantId(@Param("tenantId") Long tenantId);
}
