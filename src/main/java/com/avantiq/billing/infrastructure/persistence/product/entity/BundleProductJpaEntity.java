package com.avantiq.billing.infrastructure.persistence.product.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.UUID;

/** JPA entity for BundleProduct. */
@Entity
@Table(name = "bundle_products")
public class BundleProductJpaEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.AUTO)
  private UUID id;

  @Column(name = "bundle_id")
  private UUID bundleId;

  @Column(name = "product_id")
  private UUID productId;

  private int quantity;

  @Column(name = "optional_flag")
  private boolean optionalFlag;

  private Long tenantId;

  // Getters and setters
  public UUID getId() {
    return id;
  }

  public void setId(UUID id) {
    this.id = id;
  }

  public UUID getBundleId() {
    return bundleId;
  }

  public void setBundleId(UUID bundleId) {
    this.bundleId = bundleId;
  }

  public UUID getProductId() {
    return productId;
  }

  public void setProductId(UUID productId) {
    this.productId = productId;
  }

  public int getQuantity() {
    return quantity;
  }

  public void setQuantity(int quantity) {
    this.quantity = quantity;
  }

  public boolean isOptionalFlag() {
    return optionalFlag;
  }

  public void setOptionalFlag(boolean optionalFlag) {
    this.optionalFlag = optionalFlag;
  }

  public Long getTenantId() {
    return tenantId;
  }

  public void setTenantId(Long tenantId) {
    this.tenantId = tenantId;
  }
}
