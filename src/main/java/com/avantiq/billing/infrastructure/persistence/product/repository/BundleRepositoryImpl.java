package com.avantiq.billing.infrastructure.persistence.product.repository;

import com.avantiq.billing.domain.product.model.Bundle;
import com.avantiq.billing.domain.product.repository.BundleRepository;
import com.avantiq.billing.infrastructure.persistence.product.entity.BundleJpaEntity;
import com.avantiq.billing.infrastructure.persistence.product.mapper.BundleMapper;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

/**
 * Implementation of BundleRepository using JPA. Bridges the domain repository interface with JPA
 * repository.
 */
@Repository
public class BundleRepositoryImpl implements BundleRepository {

  private final BundleJpaRepository bundleJpaRepository;

  @Autowired
  public BundleRepositoryImpl(BundleJpaRepository bundleJpaRepository) {
    this.bundleJpaRepository = bundleJpaRepository;
  }

  @Override
  public Optional<Bundle> findById(UUID bundleId) {
    return bundleJpaRepository.findById(bundleId).map(BundleMapper::toDomain);
  }

  @Override
  public Optional<Bundle> findByIdAndTenantId(UUID bundleId, Long tenantId) {
    return bundleJpaRepository
        .findByBundleIdAndTenantId(bundleId, tenantId)
        .map(BundleMapper::toDomain);
  }

  @Override
  public List<Bundle> findByName(String name) {
    return bundleJpaRepository.findByName(name).stream().map(BundleMapper::toDomain).toList();
  }

  @Override
  public List<Bundle> findActiveByTenantId(Long tenantId) {
    return bundleJpaRepository.findByTenantIdAndStatus(tenantId, "ACTIVE").stream()
        .map(BundleMapper::toDomain)
        .toList();
  }

  @Override
  public Page<Bundle> findAllByTenantId(Long tenantId, Pageable pageable) {
    return bundleJpaRepository.findAllByTenantId(tenantId, pageable).map(BundleMapper::toDomain);
  }

  @Override
  public Bundle save(Bundle bundle) {
    BundleJpaEntity entity = BundleMapper.toJpa(bundle);
    entity = bundleJpaRepository.save(entity);
    return BundleMapper.toDomain(entity);
  }

  @Override
  public void delete(Bundle bundle) {
    if (bundle != null && bundle.getBundleId() != null) {
      bundleJpaRepository.deleteById(bundle.getBundleId());
    }
  }

  @Override
  public boolean existsByIdAndTenantId(UUID bundleId, Long tenantId) {
    return bundleJpaRepository.existsByBundleIdAndTenantId(bundleId, tenantId);
  }
}
