package com.avantiq.billing.infrastructure.persistence.customer.repository;

import com.avantiq.billing.domain.customer.model.Contact;
import com.avantiq.billing.domain.customer.repository.ContactRepository;
import com.avantiq.billing.infrastructure.persistence.customer.entity.ContactJpaEntity;
import com.avantiq.billing.infrastructure.persistence.customer.mapper.ContactMapper;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class ContactRepositoryImpl implements ContactRepository {

  private final ContactJpaRepository contactJpaRepository;
  private final ContactMapper contactMapper;

  @Autowired
  public ContactRepositoryImpl(
      ContactJpaRepository contactJpaRepository, ContactMapper contactMapper) {
    this.contactJpaRepository = contactJpaRepository;
    this.contactMapper = contactMapper;
  }

  @Override
  public List<Contact> findByCustomerId(UUID customerId) {
    List<ContactJpaEntity> entities = contactJpaRepository.findByCustomerId(customerId);
    return entities.stream().map(contactMapper::toDomain).toList();
  }

  public List<Contact> findByCustomerIdAndTenantId(UUID customerId, Long tenantId) {
    List<ContactJpaEntity> entities =
        contactJpaRepository.findByCustomerIdAndTenantId(customerId, tenantId);
    return entities.stream().map(contactMapper::toDomain).toList();
  }

  @Override
  public Optional<Contact> findById(UUID id) {
    return contactJpaRepository.findById(id).map(contactMapper::toDomain);
  }

  public Optional<Contact> findByIdAndTenantId(UUID id, Long tenantId) {
    // Since ContactJpaEntity doesn't have a findByIdAndTenantId method,
    // we need to implement this logic differently
    return contactJpaRepository
        .findById(id)
        .filter(entity -> entity.getTenantId().equals(tenantId))
        .map(contactMapper::toDomain);
  }

  @Override
  public Contact save(Contact contact) {
    ContactJpaEntity entity = contactMapper.toEntity(contact);
    ContactJpaEntity savedEntity = contactJpaRepository.save(entity);
    return contactMapper.toDomain(savedEntity);
  }

  public void deleteById(UUID id) {
    contactJpaRepository.deleteById(id);
  }

  public void deleteByIdAndTenantId(UUID id, Long tenantId) {
    contactJpaRepository
        .findById(id)
        .filter(entity -> entity.getTenantId().equals(tenantId))
        .ifPresent(entity -> contactJpaRepository.deleteById(id));
  }

  public List<Contact> findByEmailAndTenantId(String email, Long tenantId) {
    List<ContactJpaEntity> entities = contactJpaRepository.findByEmailAndTenantId(email, tenantId);
    return entities.stream().map(contactMapper::toDomain).toList();
  }

  public List<Contact> findByContactTypeAndTenantId(String contactType, Long tenantId) {
    List<ContactJpaEntity> entities =
        contactJpaRepository.findByContactTypeAndTenantId(contactType, tenantId);
    return entities.stream().map(contactMapper::toDomain).toList();
  }

  public List<Contact> findByIsDefaultAndTenantId(boolean isDefault, Long tenantId) {
    List<ContactJpaEntity> entities =
        contactJpaRepository.findByIsDefaultAndTenantId(isDefault, tenantId);
    return entities.stream().map(contactMapper::toDomain).toList();
  }
}
