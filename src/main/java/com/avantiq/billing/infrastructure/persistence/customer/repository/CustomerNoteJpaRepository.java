package com.avantiq.billing.infrastructure.persistence.customer.repository;

import com.avantiq.billing.infrastructure.persistence.customer.entity.CustomerNoteJpaEntity;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CustomerNoteJpaRepository extends JpaRepository<CustomerNoteJpaEntity, UUID> {
  List<CustomerNoteJpaEntity> findByCustomerId(UUID customerId);
}
