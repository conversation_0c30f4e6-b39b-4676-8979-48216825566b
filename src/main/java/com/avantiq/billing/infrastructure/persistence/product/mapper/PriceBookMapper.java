package com.avantiq.billing.infrastructure.persistence.product.mapper;

import com.avantiq.billing.domain.product.model.PriceBook;
import com.avantiq.billing.infrastructure.persistence.product.entity.PriceBookJpaEntity;

/** Mapper for converting between PriceBook domain model and PriceBookJpaEntity. */
public class PriceBookMapper {

  public static PriceBook toDomain(PriceBookJpaEntity entity) {
    return new PriceBook(
        entity.getId(),
        entity.getName(),
        entity.getSegment(),
        entity.getCurrency(),
        entity.getStartDate(),
        entity.getEndDate(),
        entity.getStatus());
  }

  public static PriceBookJpaEntity toEntity(PriceBook priceBook) {
    PriceBookJpaEntity entity = new PriceBookJpaEntity();
    entity.setId(priceBook.getId());
    entity.setName(priceBook.getName());
    entity.setSegment(priceBook.getSegment());
    entity.setCurrency(priceBook.getCurrency());
    entity.setStartDate(priceBook.getStartDate());
    entity.setEndDate(priceBook.getEndDate());
    entity.setStatus(priceBook.getStatus());
    return entity;
  }

  public static PriceBookJpaEntity toJpa(PriceBook priceBook) {
    return toEntity(priceBook);
  }
}
