package com.avantiq.billing.infrastructure.persistence.customer.entity;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

/**
 * JPA entity for Customer. This is the persistence model for customers, separate from the domain
 * model.
 */
@Setter
@Getter
@Entity
@Table(
    name = "customers",
    indexes = {
      @Index(name = "idx_customer_email_tenant", columnList = "email, tenant_id"),
      @Index(name = "idx_customer_tenant", columnList = "tenant_id"),
      @Index(name = "idx_customer_status_tenant", columnList = "status, tenant_id"),
      @Index(name = "idx_customer_segment_tenant", columnList = "segment_id, tenant_id"),
      @Index(name = "idx_customer_name_tenant", columnList = "first_name, last_name, tenant_id"),
      @Index(name = "idx_customer_created", columnList = "created_at")
    })
public class CustomerJpaEntity {
  @Id
  @GeneratedValue
  @Column(name = "customer_id")
  private UUID id;

  @Column(name = "first_name", nullable = false)
  private String firstName;

  @Column(name = "last_name", nullable = false)
  private String lastName;

  @Column(nullable = false)
  private String email;

  @Column(name = "company_name")
  private String companyName;

  @Column(name = "vat_number")
  private String vatNumber;

  @Column(nullable = false)
  private String country;

  @Column(name = "tenant_id", nullable = false)
  private Long tenantId;

  @Column(name = "segment_id", nullable = false)
  private UUID segmentId;

  @CreatedDate
  @Column(name = "created_at")
  private LocalDateTime createdDate;

  @LastModifiedDate
  @Column(name = "last_modified_at")
  private LocalDateTime updatedDate;

  @Enumerated(EnumType.STRING)
  @Column(nullable = false)
  private Status status;

  @OneToMany(
      mappedBy = "customer",
      cascade = CascadeType.ALL,
      orphanRemoval = true,
      fetch = FetchType.LAZY)
  private List<AddressJpaEntity> addresses = new ArrayList<>();

  public enum Status {
    ACTIVE,
    INACTIVE
  }

  /**
   * Returns an unmodifiable list of addresses associated with the customer.
   *
   * @return List of addresses.
   */
  public List<AddressJpaEntity> getAddresses() {
    return Collections.unmodifiableList(addresses);
  }

  /**
   * Adds an address to the customer.
   *
   * @param address The address to add.
   */
  public void addAddress(AddressJpaEntity address) {
    if (address != null) {
      addresses.add(address);
      address.setCustomer(this);
    }
  }

  /**
   * Removes an address from the customer.
   *
   * @param address The address to remove.
   */
  public void removeAddress(AddressJpaEntity address) {
    if (address != null && addresses.contains(address)) {
      addresses.remove(address);
      address.setCustomer(null);
    }
  }
}
