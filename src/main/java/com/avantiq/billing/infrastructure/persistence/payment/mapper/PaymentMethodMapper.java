package com.avantiq.billing.infrastructure.persistence.payment.mapper;

import com.avantiq.billing.domain.payment.model.PaymentMethod;
import com.avantiq.billing.infrastructure.persistence.payment.entity.PaymentMethodJpaEntity;
import java.time.LocalDateTime;
import org.springframework.stereotype.Component;

/** Mapper for converting between PaymentMethod domain model and PaymentMethodJpaEntity. */
@Component
public class PaymentMethodMapper {

  public PaymentMethod toDomain(PaymentMethodJpaEntity entity) {
    if (entity == null) {
      return null;
    }

    return PaymentMethod.builder()
        .id(entity.getId())
        .customerId(entity.getCustomerId())
        .tenantId(entity.getTenantId())
        .type(parsePaymentMethodType(entity.getType()))
        .details(entity.getDetails())
        .isPrimary(entity.isPrimary())
        .isValidated(entity.isValidated())
        .createdAt(entity.getCreatedAt())
        .lastModifiedAt(entity.getCreatedAt()) // Entity doesn't have lastModified
        .build();
  }

  public PaymentMethodJpaEntity toEntity(PaymentMethod domain) {
    if (domain == null) {
      return null;
    }

    PaymentMethodJpaEntity entity = new PaymentMethodJpaEntity();
    entity.setId(domain.getId());
    entity.setCustomerId(domain.getCustomerId());
    entity.setTenantId(domain.getTenantId());
    entity.setType(domain.getType().name());
    entity.setDetails(domain.getDetails());
    entity.setPrimary(domain.isPrimary());
    entity.setValidated(domain.isValidated());
    entity.setCreatedAt(
        domain.getCreatedAt() != null ? domain.getCreatedAt() : LocalDateTime.now());

    return entity;
  }

  public void updateEntity(PaymentMethodJpaEntity entity, PaymentMethod domain) {
    if (entity == null || domain == null) {
      return;
    }

    entity.setType(domain.getType().name());
    entity.setDetails(domain.getDetails());
    entity.setPrimary(domain.isPrimary());
    entity.setValidated(domain.isValidated());
  }

  private PaymentMethod.PaymentMethodType parsePaymentMethodType(String type) {
    if (type == null) {
      return PaymentMethod.PaymentMethodType.CREDIT_CARD; // Default
    }

    try {
      return PaymentMethod.PaymentMethodType.valueOf(type.toUpperCase());
    } catch (IllegalArgumentException e) {
      // If type is not recognized, default to CREDIT_CARD
      return PaymentMethod.PaymentMethodType.CREDIT_CARD;
    }
  }
}
