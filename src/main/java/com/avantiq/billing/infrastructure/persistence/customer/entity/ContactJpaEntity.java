package com.avantiq.billing.infrastructure.persistence.customer.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@Table(name = "contacts")
public class ContactJpaEntity {
  // Getters and setters
  @Id private UUID id;

  @Column(nullable = false)
  private UUID customerId;

  @Column(name = "tenant_id", nullable = false)
  private Long tenantId;

  @Column(nullable = false)
  private String firstName;

  @Column(nullable = false)
  private String lastName;

  @Column(nullable = false)
  private String email;

  @Column(nullable = false)
  private String contactType;

  @Column(nullable = false)
  private boolean isDefault;

  private LocalDateTime createdAt;
}
