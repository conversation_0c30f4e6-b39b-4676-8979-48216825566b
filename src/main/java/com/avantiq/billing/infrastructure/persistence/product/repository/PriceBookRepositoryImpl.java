package com.avantiq.billing.infrastructure.persistence.product.repository;

import com.avantiq.billing.domain.product.model.PriceBook;
import com.avantiq.billing.domain.product.repository.PriceBookRepository;
import com.avantiq.billing.infrastructure.persistence.product.entity.PriceBookJpaEntity;
import com.avantiq.billing.infrastructure.persistence.product.mapper.PriceBookMapper;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

/**
 * Implementation of PriceBookRepository using JPA. Bridges the domain repository interface with JPA
 * repository.
 */
@Repository
public class PriceBookRepositoryImpl implements PriceBookRepository {

  private final PriceBookJpaRepository priceBookJpaRepository;

  @Autowired
  public PriceBookRepositoryImpl(PriceBookJpaRepository priceBookJpaRepository) {
    this.priceBookJpaRepository = priceBookJpaRepository;
  }

  @Override
  public Optional<PriceBook> findById(UUID priceBookId) {
    return priceBookJpaRepository.findById(priceBookId).map(PriceBookMapper::toDomain);
  }

  @Override
  public Optional<PriceBook> findByIdAndTenantId(UUID priceBookId, Long tenantId) {
    return priceBookJpaRepository
        .findByIdAndTenantId(priceBookId, tenantId)
        .map(PriceBookMapper::toDomain);
  }

  @Override
  public List<PriceBook> findByName(String name) {
    return priceBookJpaRepository.findByName(name).stream().map(PriceBookMapper::toDomain).toList();
  }

  @Override
  public List<PriceBook> findActiveByTenantId(Long tenantId) {
    return priceBookJpaRepository.findByTenantIdAndStatus(tenantId, "ACTIVE").stream()
        .map(PriceBookMapper::toDomain)
        .toList();
  }

  @Override
  public List<PriceBook> findByCurrencyAndTenantId(String currency, Long tenantId) {
    return priceBookJpaRepository.findByCurrencyAndTenantId(currency, tenantId).stream()
        .map(PriceBookMapper::toDomain)
        .toList();
  }

  @Override
  public Page<PriceBook> findAllByTenantId(Long tenantId, Pageable pageable) {
    return priceBookJpaRepository
        .findAllByTenantId(tenantId, pageable)
        .map(PriceBookMapper::toDomain);
  }

  @Override
  public PriceBook save(PriceBook priceBook) {
    PriceBookJpaEntity entity = PriceBookMapper.toJpa(priceBook);
    entity = priceBookJpaRepository.save(entity);
    return PriceBookMapper.toDomain(entity);
  }

  @Override
  public void delete(PriceBook priceBook) {
    if (priceBook != null && priceBook.getId() != null) {
      priceBookJpaRepository.deleteById(priceBook.getId());
    }
  }

  @Override
  public boolean existsByIdAndTenantId(UUID priceBookId, Long tenantId) {
    return priceBookJpaRepository.existsByIdAndTenantId(priceBookId, tenantId);
  }
}
