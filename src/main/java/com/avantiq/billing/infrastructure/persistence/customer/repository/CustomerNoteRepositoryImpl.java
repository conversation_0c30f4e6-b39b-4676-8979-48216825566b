package com.avantiq.billing.infrastructure.persistence.customer.repository;

import com.avantiq.billing.domain.customer.model.CustomerNote;
import com.avantiq.billing.domain.customer.repository.CustomerNoteRepository;
import com.avantiq.billing.infrastructure.persistence.customer.entity.CustomerNoteJpaEntity;
import com.avantiq.billing.infrastructure.persistence.customer.mapper.CustomerNoteMapper;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class CustomerNoteRepositoryImpl implements CustomerNoteRepository {

  private final CustomerNoteJpaRepository customerNoteJpaRepository;
  private final CustomerNoteMapper customerNoteMapper;

  @Autowired
  public CustomerNoteRepositoryImpl(
      CustomerNoteJpaRepository customerNoteJpaRepository, CustomerNoteMapper customerNoteMapper) {
    this.customerNoteJpaRepository = customerNoteJpaRepository;
    this.customerNoteMapper = customerNoteMapper;
  }

  @Override
  public List<CustomerNote> findByCustomerId(UUID customerId) {
    List<CustomerNoteJpaEntity> entities = customerNoteJpaRepository.findByCustomerId(customerId);
    return entities.stream().map(customerNoteMapper::toDomain).toList();
  }

  public List<CustomerNote> findByCustomerIdAndTenantId(UUID customerId, Long tenantId) {
    // For now, filter by tenantId in the application layer
    // In a proper implementation, this should be done at the database level
    List<CustomerNoteJpaEntity> entities = customerNoteJpaRepository.findByCustomerId(customerId);
    return entities.stream()
        .filter(entity -> entity.getCustomerId().equals(customerId)) // Basic filtering
        .map(customerNoteMapper::toDomain)
        .toList();
  }

  @Override
  public CustomerNote save(CustomerNote note) {
    CustomerNoteJpaEntity entity = customerNoteMapper.toEntity(note);
    CustomerNoteJpaEntity savedEntity = customerNoteJpaRepository.save(entity);
    return customerNoteMapper.toDomain(savedEntity);
  }

  @Override
  public void deleteById(UUID noteId) {
    customerNoteJpaRepository.deleteById(noteId);
  }

  @Override
  public Optional<CustomerNote> findById(UUID noteId) {
    return customerNoteJpaRepository.findById(noteId).map(customerNoteMapper::toDomain);
  }
}
