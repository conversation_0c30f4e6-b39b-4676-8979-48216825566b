package com.avantiq.billing.infrastructure.persistence.common.mapper;

import com.avantiq.billing.domain.common.Metadata;
import com.avantiq.billing.infrastructure.persistence.common.entity.MetadataJpaEntity;
import org.springframework.stereotype.Component;

/** Mapper for converting between Metadata domain model and MetadataJpaEntity. */
@Component
public class MetadataMapper {

  public Metadata toDomain(MetadataJpaEntity entity) {
    if (entity == null) {
      return null;
    }
    return new Metadata(entity.getId(), entity.getEntityId(), entity.getKey(), entity.getValue());
  }

  public MetadataJpaEntity toEntity(Metadata metadata, String entityType) {
    if (metadata == null) {
      return null;
    }
    MetadataJpaEntity entity = new MetadataJpaEntity();
    entity.setId(metadata.getId());
    entity.setEntityId(metadata.getEntityId());
    entity.setKey(metadata.getKey());
    entity.setValue(metadata.getValue());
    entity.setEntityType(entityType);
    return entity;
  }
}
