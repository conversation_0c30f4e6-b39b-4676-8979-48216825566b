package com.avantiq.billing.infrastructure.persistence.product.repository;

import com.avantiq.billing.domain.product.model.Product;
import com.avantiq.billing.domain.product.repository.ProductRepository;
import com.avantiq.billing.infrastructure.persistence.product.entity.ProductJpaEntity;
import com.avantiq.billing.infrastructure.persistence.product.mapper.ProductMapper;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * Infrastructure implementation of ProductRepository. Handles persistence using JPA and maps
 * between domain and persistence models.
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class ProductRepositoryImpl implements ProductRepository {

  private final ProductJpaRepository productJpaRepository;

  @Override
  public Optional<Product> findById(UUID id) {
    log.debug("Finding product by ID: {}", id);

    return productJpaRepository.findById(id).map(ProductMapper::toDomain);
  }

  @Override
  public Product save(Product product) {
    log.debug("Saving product with ID: {}", product.getId());

    // Validate domain model before saving
    product.validate();

    ProductJpaEntity entity = ProductMapper.toEntity(product);
    ProductJpaEntity savedEntity = productJpaRepository.save(entity);

    log.info("Successfully saved product with ID: {}", savedEntity.getId());
    return ProductMapper.toDomain(savedEntity);
  }

  @Override
  public void deleteById(UUID id) {
    log.info("Deleting product with ID: {}", id);

    if (productJpaRepository.existsById(id)) {
      productJpaRepository.deleteById(id);
      log.info("Successfully deleted product with ID: {}", id);
    } else {
      log.warn("Attempted to delete non-existent product: {}", id);
    }
  }
}
