package com.avantiq.billing.infrastructure.persistence.payment.repository;

import com.avantiq.billing.infrastructure.persistence.payment.entity.PaymentMethodJpaEntity;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface PaymentMethodJpaRepository extends JpaRepository<PaymentMethodJpaEntity, UUID> {

  // Tenant-aware query methods
  List<PaymentMethodJpaEntity> findByCustomerIdAndTenantId(UUID customerId, Long tenantId);

  List<PaymentMethodJpaEntity> findByTypeAndTenantId(String type, Long tenantId);

  List<PaymentMethodJpaEntity> findByIsPrimaryAndTenantId(boolean isPrimary, Long tenantId);

  Optional<PaymentMethodJpaEntity> findByIdAndTenantId(UUID id, Long tenantId);

  boolean existsByIdAndTenantId(UUID id, Long tenantId);
}
