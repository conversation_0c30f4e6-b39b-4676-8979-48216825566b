package com.avantiq.billing.infrastructure.persistence.customer.mapper;

import com.avantiq.billing.domain.customer.model.Address;
import com.avantiq.billing.domain.customer.model.Customer;
import com.avantiq.billing.infrastructure.persistence.customer.entity.AddressJpaEntity;
import com.avantiq.billing.infrastructure.persistence.customer.entity.CustomerJpaEntity;
import java.util.ArrayList;
import java.util.List;

/** Mapper for converting between Customer domain entity and CustomerJpaEntity. */
public class CustomerMapper {

  private CustomerMapper() {}

  /**
   * Convert a CustomerJpaEntity to a Customer domain entity.
   *
   * @param jpaEntity the JPA entity
   * @return the domain entity
   */
  public static Customer toDomain(CustomerJpaEntity jpaEntity) {
    if (jpaEntity == null) {
      return null;
    }

    Customer customer = new Customer();
    customer.setId(jpaEntity.getId());
    customer.setFirstName(jpaEntity.getFirstName());
    customer.setLastName(jpaEntity.getLastName());
    customer.setEmail(jpaEntity.getEmail());
    customer.setCompanyName(jpaEntity.getCompanyName());
    customer.setVatNumber(jpaEntity.getVatNumber());
    customer.setCountry(jpaEntity.getCountry());
    customer.setSegmentId(jpaEntity.getSegmentId());
    customer.setCreatedAt(jpaEntity.getCreatedDate());
    customer.setUpdatedAt(jpaEntity.getUpdatedDate());
    customer.setStatus(mapStatus(jpaEntity.getStatus()));
    customer.setTenantId(jpaEntity.getTenantId());

    // Map addresses if they exist
    if (jpaEntity.getAddresses() != null && !jpaEntity.getAddresses().isEmpty()) {
      List<Address> addresses =
          jpaEntity.getAddresses().stream().map(AddressMapper::toDomain).toList();
      customer.setAddresses(addresses);
    }

    return customer;
  }

  /**
   * Convert a Customer domain entity to a CustomerJpaEntity.
   *
   * @param domain the domain entity
   * @return the JPA entity
   */
  public static CustomerJpaEntity toJpa(Customer domain) {
    if (domain == null) {
      return null;
    }

    CustomerJpaEntity jpaEntity = new CustomerJpaEntity();
    jpaEntity.setId(domain.getId());
    jpaEntity.setFirstName(domain.getFirstName());
    jpaEntity.setLastName(domain.getLastName());
    jpaEntity.setEmail(domain.getEmail());
    jpaEntity.setCompanyName(domain.getCompanyName());
    jpaEntity.setVatNumber(domain.getVatNumber());
    jpaEntity.setCountry(domain.getCountry());
    jpaEntity.setSegmentId(domain.getSegmentId());
    jpaEntity.setCreatedDate(domain.getCreatedAt());
    jpaEntity.setUpdatedDate(domain.getUpdatedAt());
    jpaEntity.setStatus(mapStatus(domain.getStatus()));
    jpaEntity.setTenantId(domain.getTenantId());

    // Map addresses if they exist
    if (domain.getAddresses() != null && !domain.getAddresses().isEmpty()) {
      List<AddressJpaEntity> addresses = new ArrayList<>();
      for (Address address : domain.getAddresses()) {
        if (address != null) {
          AddressJpaEntity addressJpaEntity = AddressMapper.toEntity(address);
          if (addressJpaEntity != null) {
            addressJpaEntity.setCustomer(jpaEntity);
            addresses.add(addressJpaEntity);
          }
        }
      }
      jpaEntity.setAddresses(addresses);
    }

    return jpaEntity;
  }

  /**
   * Map CustomerJpaEntity.Status to Customer.Status.
   *
   * @param status the JPA entity status
   * @return the domain entity status
   */
  private static Customer.Status mapStatus(CustomerJpaEntity.Status status) {
    if (status == null) {
      return null;
    }

    return switch (status) {
      case ACTIVE -> Customer.Status.ACTIVE;
      case INACTIVE -> Customer.Status.INACTIVE;
      default -> throw new IllegalArgumentException("Unknown status: " + status);
    };
  }

  /**
   * Map Customer.Status to CustomerJpaEntity.Status.
   *
   * @param status the domain entity status
   * @return the JPA entity status
   */
  private static CustomerJpaEntity.Status mapStatus(Customer.Status status) {
    if (status == null) {
      return null;
    }

    return switch (status) {
      case ACTIVE -> CustomerJpaEntity.Status.ACTIVE;
      case INACTIVE -> CustomerJpaEntity.Status.INACTIVE;
      default -> throw new IllegalArgumentException("Unknown status: " + status);
    };
  }
}
