package com.avantiq.billing.infrastructure.persistence.product.mapper;

import com.avantiq.billing.domain.product.model.BundleProduct;
import com.avantiq.billing.infrastructure.persistence.product.entity.BundleProductJpaEntity;

/** Mapper for converting between BundleProduct domain model and BundleProductJpaEntity. */
public class BundleProductMapper {

  public static BundleProduct toDomain(BundleProductJpaEntity entity) {
    return new BundleProduct(
        entity.getId(),
        entity.getBundleId(),
        entity.getProductId(),
        entity.getQuantity(),
        entity.isOptionalFlag());
  }

  public static BundleProductJpaEntity toEntity(BundleProduct bundleProduct) {
    BundleProductJpaEntity entity = new BundleProductJpaEntity();
    entity.setId(bundleProduct.getId());
    entity.setBundleId(bundleProduct.getBundleId());
    entity.setProductId(bundleProduct.getProductId());
    entity.setQuantity(bundleProduct.getQuantity());
    entity.setOptionalFlag(bundleProduct.isOptionalFlag());
    return entity;
  }
}
