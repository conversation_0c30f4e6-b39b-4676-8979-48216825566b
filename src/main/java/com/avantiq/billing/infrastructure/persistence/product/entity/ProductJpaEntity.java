package com.avantiq.billing.infrastructure.persistence.product.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/** JPA entity for Product. */
@Entity
@Table(
    name = "products",
    indexes = {
      @Index(name = "idx_product_sku_tenant", columnList = "sku, tenantId"),
      @Index(name = "idx_product_tenant", columnList = "tenantId"),
      @Index(name = "idx_product_status_tenant", columnList = "status, tenantId"),
      @Index(name = "idx_product_family_tenant", columnList = "productFamilyId, tenantId"),
      @Index(name = "idx_product_name_tenant", columnList = "name, tenantId")
    })
public class ProductJpaEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.AUTO)
  private UUID id;

  private String name;

  private String description;

  private BigDecimal price;

  private UUID productFamilyId;
  private String sku;
  private String taxCode;
  private String glCode;
  private String status;
  private String visibility;
  private LocalDateTime createdAt;
  private LocalDateTime updatedAt;
  private Long tenantId;

  // Getters and setters
  public UUID getId() {
    return id;
  }

  public void setId(UUID id) {
    this.id = id;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public BigDecimal getPrice() {
    return price;
  }

  public void setPrice(BigDecimal price) {
    this.price = price;
  }

  public UUID getProductFamilyId() {
    return productFamilyId;
  }

  public void setProductFamilyId(UUID productFamilyId) {
    this.productFamilyId = productFamilyId;
  }

  public String getSku() {
    return sku;
  }

  public void setSku(String sku) {
    this.sku = sku;
  }

  public String getTaxCode() {
    return taxCode;
  }

  public void setTaxCode(String taxCode) {
    this.taxCode = taxCode;
  }

  public String getGlCode() {
    return glCode;
  }

  public void setGlCode(String glCode) {
    this.glCode = glCode;
  }

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public String getVisibility() {
    return visibility;
  }

  public void setVisibility(String visibility) {
    this.visibility = visibility;
  }

  public LocalDateTime getCreatedAt() {
    return createdAt;
  }

  public void setCreatedAt(LocalDateTime createdAt) {
    this.createdAt = createdAt;
  }

  public LocalDateTime getUpdatedAt() {
    return updatedAt;
  }

  public void setUpdatedAt(LocalDateTime updatedAt) {
    this.updatedAt = updatedAt;
  }

  public Long getTenantId() {
    return tenantId;
  }

  public void setTenantId(Long tenantId) {
    this.tenantId = tenantId;
  }
}
