package com.avantiq.billing.infrastructure.persistence.customer.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;

/**
 * JPA entity for CustomerSegment. This is the persistence model for customer segments, separate
 * from the domain model.
 */
@Setter
@Getter
@Entity
@Table(name = "segment")
public class CustomerSegmentJpaEntity {
  @Id private UUID id;
  private String name;
  private String description;
  private Long tenantId;

  public Long getTenantId() {
    return tenantId;
  }

  public void setTenantId(Long tenantId) {
    this.tenantId = tenantId;
  }
}
