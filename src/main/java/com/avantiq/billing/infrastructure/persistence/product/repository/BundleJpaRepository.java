package com.avantiq.billing.infrastructure.persistence.product.repository;

import com.avantiq.billing.infrastructure.persistence.product.entity.BundleJpaEntity;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/** JPA repository for BundleJpaEntity. */
@Repository
public interface BundleJpaRepository extends JpaRepository<BundleJpaEntity, UUID> {

  Optional<BundleJpaEntity> findByBundleIdAndTenantId(UUID bundleId, Long tenantId);

  List<BundleJpaEntity> findByName(String name);

  List<BundleJpaEntity> findByTenantIdAndStatus(Long tenantId, String status);

  Page<BundleJpaEntity> findAllByTenantId(Long tenantId, Pageable pageable);

  boolean existsByBundleIdAndTenantId(UUID bundleId, Long tenantId);
}
