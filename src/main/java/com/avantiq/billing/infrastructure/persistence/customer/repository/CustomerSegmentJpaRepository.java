package com.avantiq.billing.infrastructure.persistence.customer.repository;

import com.avantiq.billing.infrastructure.persistence.customer.entity.CustomerSegmentJpaEntity;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/** Spring Data JPA repository for CustomerSegmentJpaEntity. */
@Repository
public interface CustomerSegmentJpaRepository
    extends JpaRepository<CustomerSegmentJpaEntity, UUID> {
  Optional<CustomerSegmentJpaEntity> findByName(String name);
}
