package com.avantiq.billing.infrastructure.persistence.security.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

/**
 * JPA entity for User-Segment many-to-many relationship. Represents segment assignments to users
 * for tenant-based access control.
 */
@Entity
@Table(
    name = "user_segments",
    uniqueConstraints = @UniqueConstraint(columnNames = {"user_id", "segment_id"}),
    indexes = {
      @Index(name = "idx_user_segments_user_id", columnList = "user_id"),
      @Index(name = "idx_user_segments_segment_id", columnList = "segment_id"),
      @Index(name = "idx_user_segments_tenant_id", columnList = "tenant_id")
    })
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class UserSegmentJpaEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "user_segment_id")
  private Long userSegmentId;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "user_id", nullable = false)
  private UserJpaEntity user;

  @Column(name = "segment_id", nullable = false)
  private UUID segmentId;

  @Column(name = "tenant_id", nullable = false)
  private Long tenantId;

  @Column(name = "assigned_by")
  private UUID assignedBy;

  @CreatedDate
  @Column(name = "assigned_at", nullable = false, updatable = false)
  private LocalDateTime assignedAt;
}
