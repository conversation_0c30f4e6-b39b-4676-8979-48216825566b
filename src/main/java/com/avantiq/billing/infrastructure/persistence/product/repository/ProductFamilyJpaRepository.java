package com.avantiq.billing.infrastructure.persistence.product.repository;

import com.avantiq.billing.infrastructure.persistence.product.entity.ProductFamilyJpaEntity;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/** JPA repository for ProductFamilyJpaEntity. */
@Repository
public interface ProductFamilyJpaRepository extends JpaRepository<ProductFamilyJpaEntity, UUID> {

  Optional<ProductFamilyJpaEntity> findByIdAndTenantId(UUID id, Long tenantId);

  List<ProductFamilyJpaEntity> findByName(String name);

  List<ProductFamilyJpaEntity> findByTenantId(Long tenantId);

  Page<ProductFamilyJpaEntity> findAllByTenantId(Long tenantId, Pageable pageable);

  boolean existsByIdAndTenantId(UUID id, Long tenantId);
}
