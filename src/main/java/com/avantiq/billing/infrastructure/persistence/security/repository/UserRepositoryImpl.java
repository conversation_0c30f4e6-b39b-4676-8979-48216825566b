package com.avantiq.billing.infrastructure.persistence.security.repository;

import com.avantiq.billing.domain.security.model.Role;
import com.avantiq.billing.domain.security.model.User;
import com.avantiq.billing.domain.security.repository.UserRepository;
import com.avantiq.billing.infrastructure.persistence.security.mapper.SecurityMapper;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * Implementation of UserRepository using JPA. Provides data access operations for User domain
 * objects with tenant isolation.
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class UserRepositoryImpl implements UserRepository {

  private final UserJpaRepository userJpaRepository;
  private final UserRoleJpaRepository userRoleJpaRepository;
  private final UserSegmentJpaRepository userSegmentJpaRepository;
  private final SecurityMapper securityMapper;

  @Override
  public Optional<User> findById(UUID userId) {
    log.debug("Finding user by ID: {}", userId);
    return userJpaRepository.findById(userId).map(securityMapper::toDomain);
  }

  @Override
  public Optional<User> findByUsername(String username) {
    log.debug("Finding user by username: {}", username);
    return userJpaRepository
        .findByUsername(username)
        .map(
            entity ->
                userJpaRepository
                    .findByIdWithRolesAndSegments(entity.getUserId(), entity.getTenantId())
                    .map(securityMapper::toDomain)
                    .orElse(securityMapper.toDomain(entity)));
  }

  @Override
  public Optional<User> findByEmail(String email) {
    log.debug("Finding user by email: {}", email);
    return userJpaRepository
        .findByEmail(email)
        .map(
            entity ->
                userJpaRepository
                    .findByIdWithRolesAndSegments(entity.getUserId(), entity.getTenantId())
                    .map(securityMapper::toDomain)
                    .orElse(securityMapper.toDomain(entity)));
  }

  @Override
  public Optional<User> findByUsernameAndTenantId(String username, Long tenantId) {
    log.debug("Finding user by username: {} and tenantId: {}", username, tenantId);
    return userJpaRepository
        .findByUsernameAndTenantId(username, tenantId)
        .map(
            entity ->
                userJpaRepository
                    .findByIdWithRolesAndSegments(entity.getUserId(), tenantId)
                    .map(securityMapper::toDomain)
                    .orElse(securityMapper.toDomain(entity)));
  }

  @Override
  public Optional<User> findByEmailAndTenantId(String email, Long tenantId) {
    log.debug("Finding user by email: {} and tenantId: {}", email, tenantId);
    return userJpaRepository
        .findByEmailAndTenantId(email, tenantId)
        .map(
            entity ->
                userJpaRepository
                    .findByIdWithRolesAndSegments(entity.getUserId(), tenantId)
                    .map(securityMapper::toDomain)
                    .orElse(securityMapper.toDomain(entity)));
  }

  @Override
  public User save(User user) {
    if (user.getUserId() == null) {
      log.debug("Creating new user: {}", user.getUsername());
      var entity = securityMapper.toEntity(user);
      var savedEntity = userJpaRepository.save(entity);

      // Handle role assignments
      if (user.getRoles() != null && !user.getRoles().isEmpty()) {
        var roleEntities = securityMapper.createUserRoleEntities(savedEntity, user.getRoles());
        userRoleJpaRepository.saveAll(roleEntities);
      }

      // Handle segment assignments
      if (user.getAssignedSegments() != null && !user.getAssignedSegments().isEmpty()) {
        var segmentEntities =
            securityMapper.createUserSegmentEntities(
                savedEntity, user.getAssignedSegments(), user.getTenantId());
        userSegmentJpaRepository.saveAll(segmentEntities);
      }

      log.info("Created user: {} with ID: {}", user.getUsername(), savedEntity.getUserId());

      // Reload with associations
      return userJpaRepository
          .findByIdWithRolesAndSegments(savedEntity.getUserId(), savedEntity.getTenantId())
          .map(securityMapper::toDomain)
          .orElse(securityMapper.toDomain(savedEntity));
    } else {
      log.debug("Updating existing user: {}", user.getUserId());
      return userJpaRepository
          .findById(user.getUserId())
          .map(
              existingEntity -> {
                securityMapper.updateEntity(existingEntity, user);
                var savedEntity = userJpaRepository.save(existingEntity);

                // Update role assignments
                userRoleJpaRepository.deleteByUserId(user.getUserId());
                if (user.getRoles() != null && !user.getRoles().isEmpty()) {
                  var roleEntities =
                      securityMapper.createUserRoleEntities(savedEntity, user.getRoles());
                  userRoleJpaRepository.saveAll(roleEntities);
                }

                // Update segment assignments
                userSegmentJpaRepository.deleteByUserId(user.getUserId());
                if (user.getAssignedSegments() != null && !user.getAssignedSegments().isEmpty()) {
                  var segmentEntities =
                      securityMapper.createUserSegmentEntities(
                          savedEntity, user.getAssignedSegments(), user.getTenantId());
                  userSegmentJpaRepository.saveAll(segmentEntities);
                }

                log.info("Updated user: {}", user.getUserId());

                // Reload with associations
                return userJpaRepository
                    .findByIdWithRolesAndSegments(
                        savedEntity.getUserId(), savedEntity.getTenantId())
                    .map(securityMapper::toDomain)
                    .orElse(securityMapper.toDomain(savedEntity));
              })
          .orElseThrow(() -> new IllegalArgumentException("User not found: " + user.getUserId()));
    }
  }

  @Override
  public void deleteById(UUID userId) {
    log.debug("Deleting user by ID: {}", userId);
    if (userJpaRepository.existsById(userId)) {
      userJpaRepository.deleteById(userId);
      log.info("Deleted user: {}", userId);
    } else {
      log.warn("Attempted to delete non-existent user: {}", userId);
    }
  }

  @Override
  public List<User> findAllByTenantId(Long tenantId) {
    log.debug("Finding all users for tenantId: {}", tenantId);
    return userJpaRepository.findAllByTenantId(tenantId).stream()
        .map(securityMapper::toDomain)
        .collect(Collectors.toList());
  }

  @Override
  public List<User> findActiveUsersByTenantId(Long tenantId) {
    log.debug("Finding active users for tenantId: {}", tenantId);
    return userJpaRepository.findActiveUsersByTenantId(tenantId).stream()
        .map(securityMapper::toDomain)
        .collect(Collectors.toList());
  }

  @Override
  public List<User> findBySegmentIdAndTenantId(UUID segmentId, Long tenantId) {
    log.debug("Finding users by segmentId: {} and tenantId: {}", segmentId, tenantId);
    return userJpaRepository.findBySegmentIdAndTenantId(segmentId, tenantId).stream()
        .map(securityMapper::toDomain)
        .collect(Collectors.toList());
  }

  @Override
  public List<User> findByRoleAndTenantId(Role role, Long tenantId) {
    log.debug("Finding users by role: {} and tenantId: {}", role, tenantId);
    return userJpaRepository.findByRoleAndTenantId(role.name(), tenantId).stream()
        .map(securityMapper::toDomain)
        .collect(Collectors.toList());
  }

  @Override
  public boolean existsByUsernameAndTenantId(String username, Long tenantId) {
    log.debug("Checking if username exists: {} in tenantId: {}", username, tenantId);
    return userJpaRepository.existsByUsernameAndTenantId(username, tenantId);
  }

  @Override
  public boolean existsByEmailAndTenantId(String email, Long tenantId) {
    log.debug("Checking if email exists: {} in tenantId: {}", email, tenantId);
    return userJpaRepository.existsByEmailAndTenantId(email, tenantId);
  }

  @Override
  public long countByTenantId(Long tenantId) {
    log.debug("Counting users for tenantId: {}", tenantId);
    return userJpaRepository.countByTenantId(tenantId);
  }

  @Override
  public long countActiveUsersByTenantId(Long tenantId) {
    log.debug("Counting active users for tenantId: {}", tenantId);
    return userJpaRepository.countActiveUsersByTenantId(tenantId);
  }
}
