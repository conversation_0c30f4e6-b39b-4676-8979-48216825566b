package com.avantiq.billing.infrastructure.persistence.customer.repository;

import com.avantiq.billing.infrastructure.persistence.customer.entity.CustomerJpaEntity;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/** Spring Data JPA repository for CustomerJpaEntity with tenant isolation. */
@Repository
public interface CustomerJpaRepository extends JpaRepository<CustomerJpaEntity, UUID> {

  // Tenant-aware query methods
  Optional<CustomerJpaEntity> findByEmailAndTenantId(String email, Long tenantId);

  // Legacy methods - deprecated, use tenant-aware versions instead
  @Deprecated
  Optional<CustomerJpaEntity> findByEmail(String email);

  @Deprecated
  List<CustomerJpaEntity> findByFirstNameAndLastName(String firstName, String lastName);

  @Deprecated
  List<CustomerJpaEntity> findBySegmentId(UUID segmentId);

  @Deprecated
  List<CustomerJpaEntity> findByCompanyName(String companyName);

  @Deprecated
  List<CustomerJpaEntity> findByCountry(String country);

  @Deprecated
  List<CustomerJpaEntity> findByStatus(CustomerJpaEntity.Status status);

  @Deprecated
  @Query("SELECT c FROM CustomerJpaEntity c LEFT JOIN FETCH c.addresses WHERE c.email = :email")
  Optional<CustomerJpaEntity> findByEmailWithAddresses(@Param("email") String email);

  @Deprecated
  @Query("SELECT c FROM CustomerJpaEntity c LEFT JOIN FETCH c.addresses WHERE c.id = :id")
  Optional<CustomerJpaEntity> findByIdWithAddresses(@Param("id") UUID id);

  List<CustomerJpaEntity> findByFirstNameAndLastNameAndTenantId(
      String firstName, String lastName, Long tenantId);

  List<CustomerJpaEntity> findBySegmentIdAndTenantId(UUID segmentId, Long tenantId);

  List<CustomerJpaEntity> findByCompanyNameAndTenantId(String companyName, Long tenantId);

  List<CustomerJpaEntity> findByCountryAndTenantId(String country, Long tenantId);

  List<CustomerJpaEntity> findByStatusAndTenantId(CustomerJpaEntity.Status status, Long tenantId);

  @Query(
      "SELECT c FROM CustomerJpaEntity c LEFT JOIN FETCH c.addresses WHERE c.email = :email AND c.tenantId = :tenantId")
  Optional<CustomerJpaEntity> findByEmailWithAddressesAndTenantId(
      @Param("email") String email, @Param("tenantId") Long tenantId);

  @Query(
      "SELECT c FROM CustomerJpaEntity c LEFT JOIN FETCH c.addresses WHERE c.id = :id AND c.tenantId = :tenantId")
  Optional<CustomerJpaEntity> findByIdWithAddressesAndTenantId(
      @Param("id") UUID id, @Param("tenantId") Long tenantId);

  // Override default JPA methods to include tenant isolation
  @Query("SELECT c FROM CustomerJpaEntity c WHERE c.id = :id AND c.tenantId = :tenantId")
  Optional<CustomerJpaEntity> findByIdAndTenantId(
      @Param("id") UUID id, @Param("tenantId") Long tenantId);

  @Query("SELECT c FROM CustomerJpaEntity c WHERE c.tenantId = :tenantId")
  List<CustomerJpaEntity> findAllByTenantId(@Param("tenantId") Long tenantId);

  // Paginated query methods
  @Query("SELECT c FROM CustomerJpaEntity c WHERE c.tenantId = :tenantId")
  Page<CustomerJpaEntity> findAllByTenantId(@Param("tenantId") Long tenantId, Pageable pageable);

  Page<CustomerJpaEntity> findByStatusAndTenantId(
      CustomerJpaEntity.Status status, Long tenantId, Pageable pageable);

  Page<CustomerJpaEntity> findBySegmentIdAndTenantId(
      UUID segmentId, Long tenantId, Pageable pageable);

  boolean existsByEmailAndTenantId(String email, Long tenantId);
}
