package com.avantiq.billing.infrastructure.persistence.customer.repository;

import com.avantiq.billing.infrastructure.persistence.customer.entity.CustomerRelationshipJpaEntity;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CustomerRelationshipJpaRepository
    extends JpaRepository<CustomerRelationshipJpaEntity, UUID> {

  List<CustomerRelationshipJpaEntity> findByParentId(UUID parentId);

  List<CustomerRelationshipJpaEntity> findByChildId(UUID childId);

  List<CustomerRelationshipJpaEntity> findByType(
      CustomerRelationshipJpaEntity.RelationshipType type);
}
