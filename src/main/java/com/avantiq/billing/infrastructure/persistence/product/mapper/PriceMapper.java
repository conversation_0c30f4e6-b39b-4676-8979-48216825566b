package com.avantiq.billing.infrastructure.persistence.product.mapper;

import com.avantiq.billing.domain.product.model.Price;
import com.avantiq.billing.domain.product.valueobject.Pricing;
import com.avantiq.billing.infrastructure.persistence.product.entity.PriceJpaEntity;
import com.avantiq.billing.infrastructure.persistence.product.serializer.PricingSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/** Mapper for converting between Price domain model and PriceJpaEntity. */
@Component
public class PriceMapper {

  private final PricingSerializer pricingSerializer;

  @Autowired
  public PriceMapper(PricingSerializer pricingSerializer) {
    this.pricingSerializer = pricingSerializer;
  }

  public Price toDomain(PriceJpaEntity entity) {
    // Deserialize pricing configuration first
    Pricing pricing = null;
    if (entity.getPricing() != null && entity.getPriceStrategy() != null) {
      pricing =
          pricingSerializer.deserialize(
              entity.getPricing(), Price.PriceStrategy.valueOf(entity.getPriceStrategy().name()));
    }

    Price price =
        new Price(
            entity.getId(),
            entity.getProductId(),
            entity.getPriceBookId(),
            Price.BillingFrequency.valueOf(entity.getBillingFrequency().name()),
            entity.getCurrency(),
            entity.getUnitOfMeasure(),
            Price.PriceType.valueOf(entity.getPriceType().name()),
            Price.PriceStrategy.valueOf(entity.getPriceStrategy().name()),
            Price.BillingStrategy.valueOf(entity.getBillingStrategy().name()),
            pricing,
            Price.ProrationPolicy.valueOf(entity.getProrationPolicy().name()),
            entity.isDefault(),
            entity.isGrandfathered(),
            entity.getVersion(),
            entity.getCreatedAt(),
            entity.getUpdatedAt());

    return price;
  }

  public PriceJpaEntity toEntity(Price price) {
    PriceJpaEntity entity = new PriceJpaEntity();
    entity.setId(price.getId());
    entity.setProductId(price.getProductId());
    entity.setPriceBookId(price.getPriceBookId());
    entity.setBillingFrequency(
        PriceJpaEntity.BillingFrequency.valueOf(price.getBillingFrequency().name()));
    entity.setCurrency(price.getCurrency());
    entity.setUnitOfMeasure(price.getUnitOfMeasure());
    entity.setPriceType(PriceJpaEntity.PriceType.valueOf(price.getPriceType().name()));
    entity.setPriceStrategy(PriceJpaEntity.PriceStrategy.valueOf(price.getPriceStrategy().name()));
    if (price.getPricing() != null) {
      entity.setPricing(pricingSerializer.serialize(price.getPricing()));
    }
    entity.setBillingStrategy(
        PriceJpaEntity.BillingStrategy.valueOf(price.getBillingStrategy().name()));
    entity.setProrationPolicy(
        PriceJpaEntity.ProrationPolicy.valueOf(price.getProrationPolicy().name()));
    entity.setDefault(price.isDefault());
    entity.setGrandfathered(price.isGrandfathered());
    entity.setVersion(price.getVersion());
    entity.setCreatedAt(price.getCreatedAt());
    entity.setUpdatedAt(price.getUpdatedAt());
    return entity;
  }

  public PriceJpaEntity toJpa(Price price) {
    return toEntity(price);
  }
}
