package com.avantiq.billing.infrastructure.persistence.customer.mapper;

import com.avantiq.billing.domain.customer.model.CustomerSegment;
import com.avantiq.billing.infrastructure.persistence.customer.entity.CustomerSegmentJpaEntity;
import org.springframework.stereotype.Component;

/** Mapper for converting between CustomerSegment domain model and CustomerSegmentJpaEntity. */
@Component
public class CustomerSegmentMapper {

  public CustomerSegment toDomain(CustomerSegmentJpaEntity entity) {
    if (entity == null) {
      return null;
    }

    CustomerSegment segment = new CustomerSegment();
    segment.setId(entity.getId());
    segment.setName(entity.getName());
    segment.setDescription(entity.getDescription());

    return segment;
  }

  public CustomerSegmentJpaEntity toEntity(CustomerSegment domain) {
    if (domain == null) {
      return null;
    }

    CustomerSegmentJpaEntity entity = new CustomerSegmentJpaEntity();
    entity.setId(domain.getId());
    entity.setName(domain.getName());
    entity.setDescription(domain.getDescription());
    // Note: tenantId should be set from context

    return entity;
  }

  public void updateEntity(CustomerSegmentJpaEntity entity, CustomerSegment domain) {
    if (entity == null || domain == null) {
      return;
    }

    entity.setName(domain.getName());
    entity.setDescription(domain.getDescription());
  }
}
