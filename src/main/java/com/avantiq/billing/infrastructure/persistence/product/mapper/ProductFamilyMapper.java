package com.avantiq.billing.infrastructure.persistence.product.mapper;

import com.avantiq.billing.domain.product.model.ProductFamily;
import com.avantiq.billing.infrastructure.persistence.product.entity.ProductFamilyJpaEntity;

/** Mapper for converting between ProductFamily domain model and ProductFamilyJpaEntity. */
public class ProductFamilyMapper {

  public static ProductFamily toDomain(ProductFamilyJpaEntity entity) {
    return new ProductFamily(
        entity.getId(),
        entity.getName(),
        entity.getDescription(),
        entity.getCreatedAt(),
        entity.getUpdatedAt());
  }

  public static ProductFamilyJpaEntity toEntity(ProductFamily productFamily) {
    ProductFamilyJpaEntity entity = new ProductFamilyJpaEntity();
    entity.setId(productFamily.getId());
    entity.setName(productFamily.getName());
    entity.setDescription(productFamily.getDescription());
    entity.setCreatedAt(productFamily.getCreatedAt());
    entity.setUpdatedAt(productFamily.getUpdatedAt());
    return entity;
  }

  public static ProductFamilyJpaEntity toJpa(ProductFamily productFamily) {
    return toEntity(productFamily);
  }
}
