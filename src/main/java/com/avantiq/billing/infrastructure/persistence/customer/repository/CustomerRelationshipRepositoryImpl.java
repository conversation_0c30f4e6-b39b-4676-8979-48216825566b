package com.avantiq.billing.infrastructure.persistence.customer.repository;

import com.avantiq.billing.domain.customer.model.CustomerRelationship;
import com.avantiq.billing.domain.customer.repository.CustomerRelationshipRepository;
import com.avantiq.billing.infrastructure.persistence.customer.entity.CustomerRelationshipJpaEntity;
import com.avantiq.billing.infrastructure.persistence.customer.mapper.CustomerRelationshipMapper;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * Infrastructure implementation of CustomerRelationshipRepository. Handles persistence using JPA
 * and maps between domain and persistence models.
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class CustomerRelationshipRepositoryImpl implements CustomerRelationshipRepository {

  private final CustomerRelationshipJpaRepository customerRelationshipJpaRepository;
  private final CustomerRelationshipMapper customerRelationshipMapper;

  @Override
  public Optional<CustomerRelationship> findByIdAndTenantId(UUID id, Long tenantId) {
    log.debug("Finding customer relationship by ID {} and tenant {}", id, tenantId);

    return customerRelationshipJpaRepository
        .findById(id)
        .filter(entity -> tenantId.equals(getTenantIdFromEntity(entity)))
        .map(customerRelationshipMapper::toDomain);
  }

  @Override
  public List<CustomerRelationship> findByParentCustomerIdAndTenantId(
      UUID parentCustomerId, Long tenantId) {
    log.debug(
        "Finding relationships for parent customer {} in tenant {}", parentCustomerId, tenantId);

    return customerRelationshipJpaRepository.findByParentId(parentCustomerId).stream()
        .filter(entity -> tenantId.equals(getTenantIdFromEntity(entity)))
        .map(customerRelationshipMapper::toDomain)
        .toList();
  }

  @Override
  public List<CustomerRelationship> findByChildCustomerIdAndTenantId(
      UUID childCustomerId, Long tenantId) {
    log.debug(
        "Finding relationships for child customer {} in tenant {}", childCustomerId, tenantId);

    return customerRelationshipJpaRepository.findByChildId(childCustomerId).stream()
        .filter(entity -> tenantId.equals(getTenantIdFromEntity(entity)))
        .map(customerRelationshipMapper::toDomain)
        .toList();
  }

  @Override
  public List<CustomerRelationship> findByCustomerIdAndTenantId(UUID customerId, Long tenantId) {
    log.debug("Finding all relationships for customer {} in tenant {}", customerId, tenantId);

    List<CustomerRelationshipJpaEntity> allRelationships =
        customerRelationshipJpaRepository.findAll();

    return allRelationships.stream()
        .filter(entity -> tenantId.equals(getTenantIdFromEntity(entity)))
        .filter(
            entity ->
                customerId.equals(entity.getParentId()) || customerId.equals(entity.getChildId()))
        .map(customerRelationshipMapper::toDomain)
        .toList();
  }

  @Override
  public List<CustomerRelationship> findByTypeAndTenantId(
      CustomerRelationship.RelationshipType type, Long tenantId) {
    log.debug("Finding relationships by type {} in tenant {}", type, tenantId);

    CustomerRelationshipJpaEntity.RelationshipType jpaType =
        switch (type) {
          case PAYING -> CustomerRelationshipJpaEntity.RelationshipType.PAYING;
          case REPORTING -> CustomerRelationshipJpaEntity.RelationshipType.REPORTING;
        };

    return customerRelationshipJpaRepository.findByType(jpaType).stream()
        .filter(entity -> tenantId.equals(getTenantIdFromEntity(entity)))
        .map(customerRelationshipMapper::toDomain)
        .toList();
  }

  @Override
  public List<CustomerRelationship> findDirectRelationshipsByCustomerIdAndTenantId(
      UUID customerId, Long tenantId) {
    log.debug("Finding direct relationships for customer {} in tenant {}", customerId, tenantId);

    return findByCustomerIdAndTenantId(customerId, tenantId).stream()
        .filter(CustomerRelationship::isDirectRelationship)
        .toList();
  }

  @Override
  public CustomerRelationship save(CustomerRelationship relationship) {
    log.debug("Saving customer relationship with ID: {}", relationship.getId());

    // Validate domain model before saving
    CustomerRelationship.validateCustomerRelationship(relationship);

    CustomerRelationshipJpaEntity entity = customerRelationshipMapper.toEntity(relationship);
    CustomerRelationshipJpaEntity savedEntity = customerRelationshipJpaRepository.save(entity);

    log.info("Successfully saved customer relationship with ID: {}", savedEntity.getId());
    return customerRelationshipMapper.toDomain(savedEntity);
  }

  @Override
  public void deleteByIdAndTenantId(UUID id, Long tenantId) {
    log.info("Deleting customer relationship with ID {} in tenant {}", id, tenantId);

    Optional<CustomerRelationshipJpaEntity> entity = customerRelationshipJpaRepository.findById(id);

    if (entity.isPresent() && tenantId.equals(getTenantIdFromEntity(entity.get()))) {
      customerRelationshipJpaRepository.deleteById(id);
      log.info("Successfully deleted customer relationship with ID: {}", id);
    } else {
      log.warn(
          "Attempted to delete non-existent or unauthorized relationship: {} in tenant {}",
          id,
          tenantId);
    }
  }

  @Override
  public boolean existsRelationshipBetweenCustomers(
      UUID parentCustomerId, UUID childCustomerId, Long tenantId) {
    return customerRelationshipJpaRepository.findAll().stream()
        .anyMatch(
            entity ->
                tenantId.equals(getTenantIdFromEntity(entity))
                    && parentCustomerId.equals(entity.getParentId())
                    && childCustomerId.equals(entity.getChildId()));
  }

  @Override
  public boolean wouldCreateCircularDependency(
      UUID parentCustomerId, UUID childCustomerId, Long tenantId) {
    // Check if child is already a parent of the proposed parent (creating a circle)
    List<CustomerRelationship> existingRelationships =
        findByParentCustomerIdAndTenantId(childCustomerId, tenantId);

    return existingRelationships.stream()
        .anyMatch(rel -> rel.getChildCustomerId().equals(parentCustomerId));
  }

  @Override
  public long countByCustomerIdAndTenantId(UUID customerId, Long tenantId) {
    return findByCustomerIdAndTenantId(customerId, tenantId).size();
  }

  /** Helper method to get tenant ID from entity. */
  private Long getTenantIdFromEntity(CustomerRelationshipJpaEntity entity) {
    return entity.getTenantId();
  }
}
