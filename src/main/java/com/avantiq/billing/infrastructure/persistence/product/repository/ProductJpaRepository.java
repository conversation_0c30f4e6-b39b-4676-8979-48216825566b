package com.avantiq.billing.infrastructure.persistence.product.repository;

import com.avantiq.billing.infrastructure.persistence.product.entity.ProductJpaEntity;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/** JPA repository for ProductJpaEntity with tenant isolation. */
@Repository
public interface ProductJpaRepository extends JpaRepository<ProductJpaEntity, UUID> {

  // Tenant-aware query methods
  Optional<ProductJpaEntity> findByIdAndTenantId(UUID id, Long tenantId);

  List<ProductJpaEntity> findAllByTenantId(Long tenantId);

  Optional<ProductJpaEntity> findBySkuAndTenantId(String sku, Long tenantId);

  List<ProductJpaEntity> findByNameAndTenantId(String name, Long tenantId);

  List<ProductJpaEntity> findByProductFamilyIdAndTenantId(UUID productFamilyId, Long tenantId);

  List<ProductJpaEntity> findByStatusAndTenantId(String status, Long tenantId);

  boolean existsBySkuAndTenantId(String sku, Long tenantId);
}
