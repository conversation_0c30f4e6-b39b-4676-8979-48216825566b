package com.avantiq.billing.infrastructure.persistence.customer.repository;

import com.avantiq.billing.infrastructure.persistence.customer.entity.ContactJpaEntity;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ContactJpaRepository extends JpaRepository<ContactJpaEntity, UUID> {

  // Legacy methods - deprecated, use tenant-aware versions instead
  @Deprecated
  List<ContactJpaEntity> findByCustomerId(UUID customerId);

  // Tenant-aware query methods
  List<ContactJpaEntity> findByCustomerIdAndTenantId(UUID customerId, Long tenantId);

  List<ContactJpaEntity> findByEmailAndTenantId(String email, Long tenantId);

  List<ContactJpaEntity> findByContactTypeAndTenantId(String contactType, Long tenantId);

  List<ContactJpaEntity> findByIsDefaultAndTenantId(boolean isDefault, Long tenantId);
}
