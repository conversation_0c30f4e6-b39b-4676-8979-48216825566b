package com.avantiq.billing.infrastructure.persistence.product.repository;

import com.avantiq.billing.infrastructure.persistence.product.entity.PriceJpaEntity;
import java.util.List;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/** JPA repository for PriceJpaEntity. */
@Repository
public interface PriceJpaRepository extends JpaRepository<PriceJpaEntity, UUID> {

  List<PriceJpaEntity> findByProductId(UUID productId);

  List<PriceJpaEntity> findByPriceBookId(UUID priceBookId);

  List<PriceJpaEntity> findByProductIdAndPriceBookId(UUID productId, UUID priceBookId);

  List<PriceJpaEntity> findByProductIdAndTenantId(UUID productId, Long tenantId);

  Page<PriceJpaEntity> findAllByTenantId(Long tenantId, Pageable pageable);

  boolean existsByProductIdAndPriceBookId(UUID productId, UUID priceBookId);
}
