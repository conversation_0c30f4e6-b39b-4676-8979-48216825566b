package com.avantiq.billing.infrastructure.persistence.product.repository;

import com.avantiq.billing.domain.product.model.Price;
import com.avantiq.billing.domain.product.repository.PriceRepository;
import com.avantiq.billing.infrastructure.persistence.product.entity.PriceJpaEntity;
import com.avantiq.billing.infrastructure.persistence.product.mapper.PriceMapper;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

/**
 * Implementation of PriceRepository using JPA. Bridges the domain repository interface with JPA
 * repository.
 */
@Repository
public class PriceRepositoryImpl implements PriceRepository {

  private final PriceJpaRepository priceJpaRepository;
  private final PriceMapper priceMapper;

  @Autowired
  public PriceRepositoryImpl(PriceJpaRepository priceJpaRepository, PriceMapper priceMapper) {
    this.priceJpaRepository = priceJpaRepository;
    this.priceMapper = priceMapper;
  }

  @Override
  public Optional<Price> findById(UUID priceId) {
    return priceJpaRepository.findById(priceId).map(priceMapper::toDomain);
  }

  @Override
  public List<Price> findByProductId(UUID productId) {
    return priceJpaRepository.findByProductId(productId).stream()
        .map(priceMapper::toDomain)
        .toList();
  }

  @Override
  public List<Price> findByPriceBookId(UUID priceBookId) {
    return priceJpaRepository.findByPriceBookId(priceBookId).stream()
        .map(priceMapper::toDomain)
        .toList();
  }

  @Override
  public List<Price> findByProductIdAndPriceBookId(UUID productId, UUID priceBookId) {
    return priceJpaRepository.findByProductIdAndPriceBookId(productId, priceBookId).stream()
        .map(priceMapper::toDomain)
        .toList();
  }

  @Override
  public List<Price> findActiveByProductIdAndTenantId(UUID productId, Long tenantId) {
    return priceJpaRepository.findByProductIdAndTenantId(productId, tenantId).stream()
        .filter(entity -> !entity.isGrandfathered()) // Filter out grandfathered prices
        .map(priceMapper::toDomain)
        .toList();
  }

  @Override
  public Page<Price> findAllByTenantId(Long tenantId, Pageable pageable) {
    return priceJpaRepository.findAllByTenantId(tenantId, pageable).map(priceMapper::toDomain);
  }

  @Override
  public Price save(Price price) {
    PriceJpaEntity entity = priceMapper.toJpa(price);
    entity = priceJpaRepository.save(entity);
    return priceMapper.toDomain(entity);
  }

  @Override
  public void delete(Price price) {
    if (price != null && price.getId() != null) {
      priceJpaRepository.deleteById(price.getId());
    }
  }

  @Override
  public boolean existsByProductIdAndPriceBookId(UUID productId, UUID priceBookId) {
    return priceJpaRepository.existsByProductIdAndPriceBookId(productId, priceBookId);
  }
}
