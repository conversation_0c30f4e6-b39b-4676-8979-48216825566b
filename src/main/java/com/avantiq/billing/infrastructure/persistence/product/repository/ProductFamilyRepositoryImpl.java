package com.avantiq.billing.infrastructure.persistence.product.repository;

import com.avantiq.billing.domain.product.model.ProductFamily;
import com.avantiq.billing.domain.product.repository.ProductFamilyRepository;
import com.avantiq.billing.infrastructure.persistence.product.entity.ProductFamilyJpaEntity;
import com.avantiq.billing.infrastructure.persistence.product.mapper.ProductFamilyMapper;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

/**
 * Implementation of ProductFamilyRepository using JPA. Bridges the domain repository interface with
 * JPA repository.
 */
@Repository
public class ProductFamilyRepositoryImpl implements ProductFamilyRepository {

  private final ProductFamilyJpaRepository productFamilyJpaRepository;

  @Autowired
  public ProductFamilyRepositoryImpl(ProductFamilyJpaRepository productFamilyJpaRepository) {
    this.productFamilyJpaRepository = productFamilyJpaRepository;
  }

  @Override
  public Optional<ProductFamily> findById(UUID familyId) {
    return productFamilyJpaRepository.findById(familyId).map(ProductFamilyMapper::toDomain);
  }

  @Override
  public Optional<ProductFamily> findByIdAndTenantId(UUID familyId, Long tenantId) {
    return productFamilyJpaRepository
        .findByIdAndTenantId(familyId, tenantId)
        .map(ProductFamilyMapper::toDomain);
  }

  @Override
  public List<ProductFamily> findByName(String name) {
    return productFamilyJpaRepository.findByName(name).stream()
        .map(ProductFamilyMapper::toDomain)
        .toList();
  }

  @Override
  public List<ProductFamily> findActiveByTenantId(Long tenantId) {
    return productFamilyJpaRepository.findByTenantId(tenantId).stream()
        .map(ProductFamilyMapper::toDomain)
        .toList();
  }

  @Override
  public Page<ProductFamily> findAllByTenantId(Long tenantId, Pageable pageable) {
    return productFamilyJpaRepository
        .findAllByTenantId(tenantId, pageable)
        .map(ProductFamilyMapper::toDomain);
  }

  @Override
  public ProductFamily save(ProductFamily productFamily) {
    ProductFamilyJpaEntity entity = ProductFamilyMapper.toJpa(productFamily);
    entity = productFamilyJpaRepository.save(entity);
    return ProductFamilyMapper.toDomain(entity);
  }

  @Override
  public void delete(ProductFamily productFamily) {
    if (productFamily != null && productFamily.getId() != null) {
      productFamilyJpaRepository.deleteById(productFamily.getId());
    }
  }

  @Override
  public boolean existsByIdAndTenantId(UUID familyId, Long tenantId) {
    return productFamilyJpaRepository.existsByIdAndTenantId(familyId, tenantId);
  }
}
