package com.avantiq.billing.infrastructure.persistence.customer.mapper;

import com.avantiq.billing.domain.customer.model.Address;
import com.avantiq.billing.infrastructure.persistence.customer.entity.AddressJpaEntity;

public class AddressMapper {

  private AddressMapper() {}

  /**
   * Convert an AddressJpaEntity to an Address domain entity.
   *
   * @param entity the JPA entity
   * @return the domain entity
   */
  public static Address toDomain(AddressJpaEntity entity) {
    if (entity == null) {
      return null;
    }

    Address domain = new Address();
    domain.setId(entity.getId());
    domain.setStreet(entity.getStreet());
    domain.setCity(entity.getCity());
    domain.setState(entity.getState());
    domain.setPostalCode(entity.getPostalCode());
    domain.setCountry(entity.getCountry());
    domain.setAddressType(mapAddressType(entity.getAddressType()));

    return domain;
  }

  /**
   * Convert an Address domain entity to an AddressJpaEntity.
   *
   * @param domain the domain entity
   * @return the JPA entity
   */
  public static AddressJpaEntity toEntity(Address domain) {
    if (domain == null) {
      return null;
    }

    AddressJpaEntity entity = new AddressJpaEntity();
    entity.setId(domain.getId());
    entity.setStreet(domain.getStreet());
    entity.setCity(domain.getCity());
    entity.setState(domain.getState());
    entity.setPostalCode(domain.getPostalCode());
    entity.setCountry(domain.getCountry());
    entity.setAddressType(mapAddressType(domain.getAddressType()));

    return entity;
  }

  /**
   * Map AddressJpaEntity.AddressType to Address.AddressType.
   *
   * @param addressType the JPA entity addressType
   * @return the domain entity addressType
   */
  private static Address.AddressType mapAddressType(AddressJpaEntity.AddressType addressType) {
    if (addressType == null) {
      return null;
    }

    return switch (addressType) {
      case BILLING -> Address.AddressType.BILLING;
      case SHIPPING -> Address.AddressType.SHIPPING;
      default -> throw new IllegalArgumentException("Unknown addressType: " + addressType);
    };
  }

  /**
   * Map Address.AddressType to AddressJpaEntity.AddressType.
   *
   * @param addressType the domain entity addressType
   * @return the JPA entity addressType
   */
  private static AddressJpaEntity.AddressType mapAddressType(Address.AddressType addressType) {
    if (addressType == null) {
      return null;
    }

    return switch (addressType) {
      case BILLING -> AddressJpaEntity.AddressType.BILLING;
      case SHIPPING -> AddressJpaEntity.AddressType.SHIPPING;
      default -> throw new IllegalArgumentException("Unknown addressType: " + addressType);
    };
  }
}
