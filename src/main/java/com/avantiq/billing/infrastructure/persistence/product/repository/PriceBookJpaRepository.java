package com.avantiq.billing.infrastructure.persistence.product.repository;

import com.avantiq.billing.infrastructure.persistence.product.entity.PriceBookJpaEntity;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/** JPA repository for PriceBookJpaEntity. */
@Repository
public interface PriceBookJpaRepository extends JpaRepository<PriceBookJpaEntity, UUID> {

  Optional<PriceBookJpaEntity> findByIdAndTenantId(UUID id, Long tenantId);

  List<PriceBookJpaEntity> findByName(String name);

  List<PriceBookJpaEntity> findBySegment(String segment);

  List<PriceBookJpaEntity> findByTenantIdAndStatus(Long tenantId, String status);

  List<PriceBookJpaEntity> findByCurrencyAndTenantId(String currency, Long tenantId);

  Page<PriceBookJpaEntity> findAllByTenantId(Long tenantId, Pageable pageable);

  boolean existsByIdAndTenantId(UUID id, Long tenantId);
}
