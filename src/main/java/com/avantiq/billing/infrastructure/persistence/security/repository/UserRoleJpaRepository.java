package com.avantiq.billing.infrastructure.persistence.security.repository;

import com.avantiq.billing.infrastructure.persistence.security.entity.UserRoleJpaEntity;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/** JPA repository for UserRoleJpaEntity. Manages user-role associations. */
@Repository
public interface UserRoleJpaRepository extends JpaRepository<UserRoleJpaEntity, Long> {

  /** Find all role assignments for a user. */
  @Query("SELECT ur FROM UserRoleJpaEntity ur WHERE ur.user.userId = :userId")
  List<UserRoleJpaEntity> findByUserId(@Param("userId") UUID userId);

  /** Delete all role assignments for a user. */
  @Modifying
  @Query("DELETE FROM UserRoleJpaEntity ur WHERE ur.user.userId = :userId")
  void deleteByUserId(@Param("userId") UUID userId);

  /** Check if user has a specific role. */
  @Query(
      "SELECT COUNT(ur) > 0 FROM UserRoleJpaEntity ur WHERE ur.user.userId = :userId AND ur.role.roleId = :roleId")
  boolean existsByUserIdAndRoleId(@Param("userId") UUID userId, @Param("roleId") Long roleId);
}
