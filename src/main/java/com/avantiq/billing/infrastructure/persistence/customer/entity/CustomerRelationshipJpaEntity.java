package com.avantiq.billing.infrastructure.persistence.customer.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@Table(
    name = "customer_relationship",
    indexes = {
      @Index(name = "idx_relationship_parent_tenant", columnList = "parentId, tenant_id"),
      @Index(name = "idx_relationship_child_tenant", columnList = "childId, tenant_id"),
      @Index(name = "idx_relationship_type_tenant", columnList = "type, tenant_id"),
      @Index(name = "idx_relationship_tenant", columnList = "tenant_id"),
      @Index(name = "idx_relationship_segment", columnList = "segmentId")
    })
public class CustomerRelationshipJpaEntity {
  @Id @GeneratedValue private UUID id;

  @Column(nullable = false)
  private UUID parentId;

  @Column(nullable = false)
  private UUID childId;

  @Enumerated(EnumType.STRING)
  @Column(nullable = false)
  private RelationshipType type;

  @Column(nullable = false)
  private Integer level;

  @Column(nullable = false)
  private UUID segmentId;

  @Column(nullable = false)
  private Long tenantId;

  private LocalDateTime createdAt;

  public enum RelationshipType {
    PAYING,
    REPORTING
  }
}
