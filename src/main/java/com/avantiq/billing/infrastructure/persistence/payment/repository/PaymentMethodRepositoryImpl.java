package com.avantiq.billing.infrastructure.persistence.payment.repository;

import com.avantiq.billing.domain.payment.model.PaymentMethod;
import com.avantiq.billing.domain.payment.repository.PaymentMethodRepository;
import com.avantiq.billing.infrastructure.persistence.payment.entity.PaymentMethodJpaEntity;
import com.avantiq.billing.infrastructure.persistence.payment.mapper.PaymentMethodMapper;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * Infrastructure implementation of PaymentMethodRepository. Handles persistence using JPA and maps
 * between domain and persistence models.
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class PaymentMethodRepositoryImpl implements PaymentMethodRepository {

  private final PaymentMethodJpaRepository paymentMethodJpaRepository;
  private final PaymentMethodMapper paymentMethodMapper;

  @Override
  public Optional<PaymentMethod> findByIdAndTenantId(UUID id, Long tenantId) {
    log.debug("Finding payment method by ID {} and tenant {}", id, tenantId);

    return paymentMethodJpaRepository
        .findByIdAndTenantId(id, tenantId)
        .map(paymentMethodMapper::toDomain);
  }

  @Override
  public List<PaymentMethod> findByCustomerIdAndTenantId(UUID customerId, Long tenantId) {
    log.debug("Finding payment methods for customer {} in tenant {}", customerId, tenantId);

    return paymentMethodJpaRepository.findByCustomerIdAndTenantId(customerId, tenantId).stream()
        .map(paymentMethodMapper::toDomain)
        .toList();
  }

  @Override
  public Optional<PaymentMethod> findPrimaryByCustomerIdAndTenantId(
      UUID customerId, Long tenantId) {
    log.debug("Finding primary payment method for customer {} in tenant {}", customerId, tenantId);

    return paymentMethodJpaRepository.findByIsPrimaryAndTenantId(true, tenantId).stream()
        .filter(entity -> entity.getCustomerId().equals(customerId))
        .map(paymentMethodMapper::toDomain)
        .findFirst();
  }

  @Override
  public List<PaymentMethod> findByTypeAndTenantId(
      PaymentMethod.PaymentMethodType type, Long tenantId) {
    log.debug("Finding payment methods by type {} in tenant {}", type, tenantId);

    return paymentMethodJpaRepository.findByTypeAndTenantId(type.name(), tenantId).stream()
        .map(paymentMethodMapper::toDomain)
        .toList();
  }

  @Override
  public PaymentMethod save(PaymentMethod paymentMethod) {
    log.debug("Saving payment method with ID: {}", paymentMethod.getId());

    // Validate domain model before saving
    PaymentMethod.validatePaymentMethod(paymentMethod);

    PaymentMethodJpaEntity entity = paymentMethodMapper.toEntity(paymentMethod);
    PaymentMethodJpaEntity savedEntity = paymentMethodJpaRepository.save(entity);

    log.info("Successfully saved payment method with ID: {}", savedEntity.getId());
    return paymentMethodMapper.toDomain(savedEntity);
  }

  @Override
  public void deleteByIdAndTenantId(UUID id, Long tenantId) {
    log.info("Deleting payment method with ID {} in tenant {}", id, tenantId);

    // Verify exists before deleting
    if (!paymentMethodJpaRepository.existsByIdAndTenantId(id, tenantId)) {
      log.warn("Attempted to delete non-existent payment method: {} in tenant {}", id, tenantId);
      return;
    }

    // Find and delete
    paymentMethodJpaRepository
        .findByIdAndTenantId(id, tenantId)
        .ifPresent(entity -> paymentMethodJpaRepository.deleteById(entity.getId()));

    log.info("Successfully deleted payment method with ID: {}", id);
  }

  @Override
  public boolean existsByIdAndTenantId(UUID id, Long tenantId) {
    return paymentMethodJpaRepository.existsByIdAndTenantId(id, tenantId);
  }

  @Override
  public long countByCustomerIdAndTenantId(UUID customerId, Long tenantId) {
    return paymentMethodJpaRepository.findByCustomerIdAndTenantId(customerId, tenantId).size();
  }
}
