package com.avantiq.billing.infrastructure.persistence.product.mapper;

import com.avantiq.billing.domain.product.model.Product;
import com.avantiq.billing.infrastructure.persistence.product.entity.ProductJpaEntity;
import org.springframework.stereotype.Component;

/** Mapper for converting between Product domain model and ProductJpaEntity. */
@Component
public class ProductMapper {

  public static Product toDomain(ProductJpaEntity entity) {
    if (entity == null) {
      return null;
    }
    return new Product(
        entity.getId(),
        entity.getName(),
        entity.getDescription(),
        entity.getPrice(),
        entity.getProductFamilyId(),
        entity.getSku(),
        entity.getTaxCode(),
        entity.getGlCode(),
        entity.getStatus(),
        entity.getVisibility(),
        entity.getCreatedAt(),
        entity.getUpdatedAt(),
        entity.getTenantId());
  }

  public static ProductJpaEntity toEntity(Product product) {
    if (product == null) {
      return null;
    }

    ProductJpaEntity entity = new ProductJpaEntity();
    entity.setId(product.getId());
    entity.setName(product.getName());
    entity.setDescription(product.getDescription());
    entity.setPrice(product.getPrice());
    entity.setProductFamilyId(product.getProductFamilyId());
    entity.setSku(product.getSku());
    entity.setTaxCode(product.getTaxCode());
    entity.setGlCode(product.getGlCode());
    entity.setStatus(product.getStatus());
    entity.setVisibility(product.getVisibility());
    entity.setCreatedAt(product.getCreatedAt());
    entity.setUpdatedAt(product.getUpdatedAt());
    entity.setTenantId(product.getTenantId());
    return entity;
  }
}
