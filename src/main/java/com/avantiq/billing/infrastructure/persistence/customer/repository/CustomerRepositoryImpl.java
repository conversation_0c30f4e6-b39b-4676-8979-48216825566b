package com.avantiq.billing.infrastructure.persistence.customer.repository;

import com.avantiq.billing.domain.customer.model.Customer;
import com.avantiq.billing.domain.customer.repository.CustomerRepository;
import com.avantiq.billing.infrastructure.persistence.customer.entity.CustomerJpaEntity;
import com.avantiq.billing.infrastructure.persistence.customer.mapper.CustomerMapper;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

@Repository
public class CustomerRepositoryImpl implements CustomerRepository {
  private final CustomerJpaRepository customerJpaRepository;

  @Autowired
  public CustomerRepositoryImpl(CustomerJpaRepository customerJpaRepository) {
    this.customerJpaRepository = customerJpaRepository;
  }

  @Override
  public Optional<Customer> findById(UUID id) {
    // Note: This method should be updated to use tenant-aware queries
    // For now, using the original method but this needs tenant context
    CustomerJpaEntity entity = customerJpaRepository.findById(id).orElse(null);
    Customer customer = CustomerMapper.toDomain(entity);
    return Optional.ofNullable(customer);
  }

  @Override
  public Optional<Customer> findByIdAndTenantId(UUID id, Long tenantId) {
    return customerJpaRepository.findByIdAndTenantId(id, tenantId).map(CustomerMapper::toDomain);
  }

  @Override
  public Optional<Customer> findByEmail(String email) {
    // Note: This method should be updated to use tenant-aware queries
    CustomerJpaEntity entity = customerJpaRepository.findByEmail(email).orElse(null);
    Customer customer = CustomerMapper.toDomain(entity);
    return Optional.ofNullable(customer);
  }

  @Override
  public Optional<Customer> findByEmailAndTenantId(String email, Long tenantId) {
    return customerJpaRepository
        .findByEmailAndTenantId(email, tenantId)
        .map(CustomerMapper::toDomain);
  }

  @Override
  public List<Customer> findByFirstNameAndLastName(String firstName, String lastName) {
    return customerJpaRepository.findByFirstNameAndLastName(firstName, lastName).stream()
        .map(CustomerMapper::toDomain)
        .toList();
  }

  public List<Customer> findByFirstNameAndLastNameAndTenantId(
      String firstName, String lastName, Long tenantId) {
    return customerJpaRepository
        .findByFirstNameAndLastNameAndTenantId(firstName, lastName, tenantId)
        .stream()
        .map(CustomerMapper::toDomain)
        .toList();
  }

  @Override
  public List<Customer> findBySegmentId(UUID segmentId) {
    return customerJpaRepository.findBySegmentId(segmentId).stream()
        .map(CustomerMapper::toDomain)
        .toList();
  }

  public List<Customer> findBySegmentIdAndTenantId(UUID segmentId, Long tenantId) {
    return customerJpaRepository.findBySegmentIdAndTenantId(segmentId, tenantId).stream()
        .map(CustomerMapper::toDomain)
        .toList();
  }

  @Override
  public List<Customer> findByCompanyName(String companyName) {
    return customerJpaRepository.findByCompanyName(companyName).stream()
        .map(CustomerMapper::toDomain)
        .toList();
  }

  public List<Customer> findByCompanyNameAndTenantId(String companyName, Long tenantId) {
    return customerJpaRepository.findByCompanyNameAndTenantId(companyName, tenantId).stream()
        .map(CustomerMapper::toDomain)
        .toList();
  }

  @Override
  public List<Customer> findByCountry(String country) {
    return customerJpaRepository.findByCountry(country).stream()
        .map(CustomerMapper::toDomain)
        .toList();
  }

  public List<Customer> findByCountryAndTenantId(String country, Long tenantId) {
    return customerJpaRepository.findByCountryAndTenantId(country, tenantId).stream()
        .map(CustomerMapper::toDomain)
        .toList();
  }

  @Override
  public List<Customer> findByStatus(Customer.Status status) {
    CustomerJpaEntity.Status jpaStatus = mapStatusToJpa(status);
    return customerJpaRepository.findByStatus(jpaStatus).stream()
        .map(CustomerMapper::toDomain)
        .toList();
  }

  public List<Customer> findByStatusAndTenantId(Customer.Status status, Long tenantId) {
    CustomerJpaEntity.Status jpaStatus = mapStatusToJpa(status);
    return customerJpaRepository.findByStatusAndTenantId(jpaStatus, tenantId).stream()
        .map(CustomerMapper::toDomain)
        .toList();
  }

  @Override
  public Customer save(Customer customer) {
    CustomerJpaEntity entity = CustomerMapper.toJpa(customer);
    entity = customerJpaRepository.save(entity);
    return CustomerMapper.toDomain(entity);
  }

  @Override
  public void delete(Customer customer) {
    if (customer != null && customer.getId() != null) {
      customerJpaRepository.deleteById(customer.getId());
    }
  }

  @Override
  public Optional<Customer> findByIdWithAddresses(UUID id) {
    return customerJpaRepository.findByIdWithAddresses(id).map(CustomerMapper::toDomain);
  }

  @Override
  public Optional<Customer> findByIdWithAddressesAndTenantId(UUID id, Long tenantId) {
    return customerJpaRepository
        .findByIdWithAddressesAndTenantId(id, tenantId)
        .map(CustomerMapper::toDomain);
  }

  @Override
  public Optional<Customer> findByEmailWithAddresses(String email) {
    return customerJpaRepository.findByEmailWithAddresses(email).map(CustomerMapper::toDomain);
  }

  @Override
  public Optional<Customer> findByEmailWithAddressesAndTenantId(String email, Long tenantId) {
    return customerJpaRepository
        .findByEmailWithAddressesAndTenantId(email, tenantId)
        .map(CustomerMapper::toDomain);
  }

  public List<Customer> findAllByTenantId(Long tenantId) {
    return customerJpaRepository.findAllByTenantId(tenantId).stream()
        .map(CustomerMapper::toDomain)
        .toList();
  }

  @Override
  public boolean existsByEmailAndTenantId(String email, Long tenantId) {
    return customerJpaRepository.existsByEmailAndTenantId(email, tenantId);
  }

  @Override
  public Page<Customer> findAllByTenantId(Long tenantId, Pageable pageable) {
    return customerJpaRepository
        .findAllByTenantId(tenantId, pageable)
        .map(CustomerMapper::toDomain);
  }

  @Override
  public Page<Customer> findByStatusAndTenantId(
      Customer.Status status, Long tenantId, Pageable pageable) {
    CustomerJpaEntity.Status jpaStatus = mapStatusToJpa(status);
    return customerJpaRepository
        .findByStatusAndTenantId(jpaStatus, tenantId, pageable)
        .map(CustomerMapper::toDomain);
  }

  @Override
  public Page<Customer> findBySegmentIdAndTenantId(
      UUID segmentId, Long tenantId, Pageable pageable) {
    return customerJpaRepository
        .findBySegmentIdAndTenantId(segmentId, tenantId, pageable)
        .map(CustomerMapper::toDomain);
  }

  private CustomerJpaEntity.Status mapStatusToJpa(Customer.Status status) {
    if (status == null) {
      return null;
    }
    return switch (status) {
      case ACTIVE -> CustomerJpaEntity.Status.ACTIVE;
      case INACTIVE -> CustomerJpaEntity.Status.INACTIVE;
    };
  }
}
