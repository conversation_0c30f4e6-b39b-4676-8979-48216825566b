package com.avantiq.billing.infrastructure.persistence.customer.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
public class CustomerNoteJpaEntity {
  @Id @GeneratedValue private UUID id;

  @Column(nullable = false)
  private UUID customerId;

  @Column(nullable = false, length = 2048)
  private String notes;

  private LocalDateTime createdAt;
  private LocalDateTime updatedAt;
  private String createdBy;
}
