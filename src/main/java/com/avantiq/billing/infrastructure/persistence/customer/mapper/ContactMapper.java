package com.avantiq.billing.infrastructure.persistence.customer.mapper;

import com.avantiq.billing.domain.customer.model.Contact;
import com.avantiq.billing.infrastructure.persistence.customer.entity.ContactJpaEntity;
import org.springframework.stereotype.Component;

/** Mapper for converting between Contact domain model and ContactJpaEntity. */
@Component
public class ContactMapper {

  public Contact toDomain(ContactJpaEntity entity) {
    if (entity == null) {
      return null;
    }

    Contact contact = new Contact();
    contact.setId(entity.getId());
    contact.setFirstName(entity.getFirstName());
    contact.setLastName(entity.getLastName());
    contact.setEmail(entity.getEmail());
    contact.setContactType(entity.getContactType());
    contact.setDefault(entity.isDefault());
    contact.setTenantId(entity.getTenantId());

    return contact;
  }

  public ContactJpaEntity toEntity(Contact domain) {
    if (domain == null) {
      return null;
    }

    ContactJpaEntity entity = new ContactJpaEntity();
    entity.setId(domain.getId());
    entity.setTenantId(domain.getTenantId());
    entity.setFirstName(domain.getFirstName());
    entity.setLastName(domain.getLastName());
    entity.setEmail(domain.getEmail());
    entity.setContactType(domain.getContactType());
    entity.setDefault(domain.isDefault());
    entity.setCreatedAt(java.time.LocalDateTime.now());

    return entity;
  }

  public void updateEntity(ContactJpaEntity entity, Contact domain) {
    if (entity == null || domain == null) {
      return;
    }

    entity.setFirstName(domain.getFirstName());
    entity.setLastName(domain.getLastName());
    entity.setEmail(domain.getEmail());
    entity.setContactType(domain.getContactType());
    entity.setDefault(domain.isDefault());
  }
}
