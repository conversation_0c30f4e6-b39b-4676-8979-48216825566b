package com.avantiq.billing.infrastructure.persistence.customer.mapper;

import com.avantiq.billing.domain.customer.model.CustomerNote;
import com.avantiq.billing.infrastructure.persistence.customer.entity.CustomerNoteJpaEntity;
import org.springframework.stereotype.Component;

/** Mapper for converting between CustomerNote domain model and CustomerNoteJpaEntity. */
@Component
public class CustomerNoteMapper {

  public CustomerNote toDomain(CustomerNoteJpaEntity entity) {
    if (entity == null) {
      return null;
    }

    CustomerNote note = new CustomerNote();
    note.setId(entity.getId());
    note.setNote(entity.getNotes());
    note.setTenantId(null); // Entity doesn't have tenantId, would need to be added

    return note;
  }

  public CustomerNoteJpaEntity toEntity(CustomerNote domain) {
    if (domain == null) {
      return null;
    }

    CustomerNoteJpaEntity entity = new CustomerNoteJpaEntity();
    entity.setId(domain.getId());
    entity.setNotes(domain.getNote());
    // Note: customerId and other fields would need to be set from context
    entity.setCreatedAt(java.time.LocalDateTime.now());

    return entity;
  }

  public void updateEntity(CustomerNoteJpaEntity entity, CustomerNote domain) {
    if (entity == null || domain == null) {
      return;
    }

    entity.setNotes(domain.getNote());
    entity.setUpdatedAt(java.time.LocalDateTime.now());
  }
}
