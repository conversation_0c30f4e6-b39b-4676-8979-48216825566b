package com.avantiq.billing.infrastructure.persistence.customer.repository;

import com.avantiq.billing.domain.customer.model.CustomerSegment;
import com.avantiq.billing.domain.customer.repository.CustomerSegmentRepository;
import com.avantiq.billing.infrastructure.persistence.customer.entity.CustomerSegmentJpaEntity;
import com.avantiq.billing.infrastructure.persistence.customer.mapper.CustomerSegmentMapper;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

@Repository
public class CustomerSegmentRepositoryImpl implements CustomerSegmentRepository {

  private final CustomerSegmentJpaRepository customerSegmentJpaRepository;
  private final CustomerSegmentMapper customerSegmentMapper;

  @Autowired
  public CustomerSegmentRepositoryImpl(
      CustomerSegmentJpaRepository customerSegmentJpaRepository,
      CustomerSegmentMapper customerSegmentMapper) {
    this.customerSegmentJpaRepository = customerSegmentJpaRepository;
    this.customerSegmentMapper = customerSegmentMapper;
  }

  @Override
  public List<CustomerSegment> findAll() {
    List<CustomerSegmentJpaEntity> entities = customerSegmentJpaRepository.findAll();
    return entities.stream().map(customerSegmentMapper::toDomain).toList();
  }

  public List<CustomerSegment> findAllByTenantId(Long tenantId) {
    // This method would need to be added to CustomerSegmentJpaRepository
    // For now, filtering in application layer
    List<CustomerSegmentJpaEntity> entities = customerSegmentJpaRepository.findAll();
    return entities.stream()
        .filter(entity -> entity.getTenantId() != null && entity.getTenantId().equals(tenantId))
        .map(customerSegmentMapper::toDomain)
        .toList();
  }

  @Override
  public Page<CustomerSegment> findAll(Pageable pageable) {
    Page<CustomerSegmentJpaEntity> entityPage = customerSegmentJpaRepository.findAll(pageable);
    List<CustomerSegment> segments =
        entityPage.getContent().stream().map(customerSegmentMapper::toDomain).toList();

    return new PageImpl<>(segments, pageable, entityPage.getTotalElements());
  }

  @Override
  public Optional<CustomerSegment> findById(UUID id) {
    return customerSegmentJpaRepository.findById(id).map(customerSegmentMapper::toDomain);
  }

  public Optional<CustomerSegment> findByIdAndTenantId(UUID id, Long tenantId) {
    return customerSegmentJpaRepository
        .findById(id)
        .filter(entity -> entity.getTenantId() != null && entity.getTenantId().equals(tenantId))
        .map(customerSegmentMapper::toDomain);
  }

  @Override
  public Optional<CustomerSegment> findByName(String name) {
    // This method would need to be added to CustomerSegmentJpaRepository
    List<CustomerSegmentJpaEntity> entities = customerSegmentJpaRepository.findAll();
    return entities.stream()
        .filter(entity -> name.equals(entity.getName()))
        .findFirst()
        .map(customerSegmentMapper::toDomain);
  }

  public Optional<CustomerSegment> findByNameAndTenantId(String name, Long tenantId) {
    List<CustomerSegmentJpaEntity> entities = customerSegmentJpaRepository.findAll();
    return entities.stream()
        .filter(
            entity ->
                name.equals(entity.getName())
                    && entity.getTenantId() != null
                    && entity.getTenantId().equals(tenantId))
        .findFirst()
        .map(customerSegmentMapper::toDomain);
  }

  @Override
  public CustomerSegment save(CustomerSegment segment) {
    CustomerSegmentJpaEntity entity = customerSegmentMapper.toEntity(segment);
    CustomerSegmentJpaEntity savedEntity = customerSegmentJpaRepository.save(entity);
    return customerSegmentMapper.toDomain(savedEntity);
  }

  @Override
  public void delete(CustomerSegment segment) {
    if (segment != null && segment.getId() != null) {
      customerSegmentJpaRepository.deleteById(segment.getId());
    }
  }
}
