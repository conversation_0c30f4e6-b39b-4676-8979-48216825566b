package com.avantiq.billing.infrastructure.persistence.product.mapper;

import com.avantiq.billing.domain.product.model.Bundle;
import com.avantiq.billing.infrastructure.persistence.product.entity.BundleJpaEntity;

/** Mapper for converting between Bundle domain model and BundleJpaEntity. */
public class BundleMapper {

  public static Bundle toDomain(BundleJpaEntity entity) {
    return new Bundle(
        entity.getBundleId(),
        entity.getName(),
        entity.getDescription(),
        entity.getStatus(),
        entity.getPricingStrategy(),
        entity.getVersion(),
        entity.getCreatedAt(),
        entity.getUpdatedAt());
  }

  public static BundleJpaEntity toEntity(Bundle bundle) {
    BundleJpaEntity entity = new BundleJpaEntity();
    entity.setBundleId(bundle.getBundleId());
    entity.setName(bundle.getName());
    entity.setDescription(bundle.getDescription());
    entity.setStatus(bundle.getStatus());
    entity.setPricingStrategy(bundle.getPricingStrategy());
    entity.setVersion(bundle.getVersion());
    entity.setCreatedAt(bundle.getCreatedAt());
    entity.setUpdatedAt(bundle.getUpdatedAt());
    return entity;
  }

  public static BundleJpaEntity toJpa(Bundle bundle) {
    return toEntity(bundle);
  }
}
