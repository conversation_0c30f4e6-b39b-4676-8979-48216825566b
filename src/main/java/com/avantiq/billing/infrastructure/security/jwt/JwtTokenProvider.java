package com.avantiq.billing.infrastructure.security.jwt;

import com.avantiq.billing.domain.security.model.Role;
import com.avantiq.billing.domain.security.model.UserPrincipal;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import java.security.Key;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * JWT token provider for creating and validating JWT tokens. Handles tenant and segment claims for
 * multi-tenant access control.
 */
@Component
@Slf4j
public class JwtTokenProvider {

  private final Key jwtSecret;
  private final long jwtExpirationMs;

  public JwtTokenProvider(
      @Value("${jwt.secret}") String jwtSecret,
      @Value("${jwt.expiration:86400000}") long jwtExpirationMs) {

    // Validate JWT secret for security
    if (jwtSecret == null || jwtSecret.trim().isEmpty()) {
      throw new IllegalArgumentException("JWT secret cannot be null or empty");
    }

    if (jwtSecret.contains("dev-only") || jwtSecret.contains("development")) {
      log.warn("WARNING: Using development JWT secret. This should never be used in production!");
    }

    if (jwtSecret.length() < 32) {
      throw new IllegalArgumentException(
          "JWT secret must be at least 32 characters long for security");
    }

    this.jwtSecret = Keys.hmacShaKeyFor(jwtSecret.getBytes());
    this.jwtExpirationMs = jwtExpirationMs;

    log.info("JWT token provider initialized with {}ms expiration", jwtExpirationMs);
  }

  /** Generate JWT token from UserPrincipal. */
  public String generateToken(UserPrincipal userPrincipal) {
    Date expiryDate = new Date(System.currentTimeMillis() + jwtExpirationMs);
    Date issuedAt = new Date();

    // Limit segments to prevent token bloat (max 10 segments)
    List<String> limitedSegments =
        userPrincipal.getAssignedSegments().stream()
            .limit(10)
            .map(UUID::toString)
            .collect(Collectors.toList());

    return Jwts.builder()
        .setSubject(userPrincipal.getUserId().toString())
        .setIssuer("avantiq-billing-service")
        .setAudience("avantiq-billing-api")
        .claim("typ", "access_token")
        .claim("username", userPrincipal.getUsername())
        .claim("tenantId", userPrincipal.getTenantId())
        .claim(
            "roles",
            userPrincipal.getRoles().stream().map(Role::getAuthority).collect(Collectors.toList()))
        .claim("segments", limitedSegments)
        .claim("segmentCount", userPrincipal.getAssignedSegments().size())
        .setIssuedAt(issuedAt)
        .setExpiration(expiryDate)
        .signWith(jwtSecret, SignatureAlgorithm.HS256)
        .compact();
  }

  /** Get user ID from JWT token. */
  public UUID getUserIdFromToken(String token) {
    Claims claims = getClaimsFromToken(token);
    return UUID.fromString(claims.getSubject());
  }

  /** Get username from JWT token. */
  public String getUsernameFromToken(String token) {
    Claims claims = getClaimsFromToken(token);
    return claims.get("username", String.class);
  }

  /** Get tenant ID from JWT token. */
  public Long getTenantIdFromToken(String token) {
    Claims claims = getClaimsFromToken(token);
    return claims.get("tenantId", Long.class);
  }

  /** Get roles from JWT token. */
  @SuppressWarnings("unchecked")
  public Set<Role> getRolesFromToken(String token) {
    Claims claims = getClaimsFromToken(token);
    List<String> roleNames = claims.get("roles", List.class);
    return roleNames.stream().map(Role::valueOf).collect(Collectors.toSet());
  }

  /** Get assigned segments from JWT token. */
  @SuppressWarnings("unchecked")
  public Set<UUID> getSegmentsFromToken(String token) {
    Claims claims = getClaimsFromToken(token);
    List<String> segmentIds = claims.get("segments", List.class);
    return segmentIds.stream().map(UUID::fromString).collect(Collectors.toSet());
  }

  /** Get total segment count from JWT token (including segments not included due to limit). */
  public Integer getSegmentCountFromToken(String token) {
    Claims claims = getClaimsFromToken(token);
    return claims.get("segmentCount", Integer.class);
  }

  /** Validate JWT token. */
  public boolean validateToken(String token) {
    try {
      Claims claims = getClaimsFromToken(token);

      // Validate issuer
      if (!"avantiq-billing-service".equals(claims.getIssuer())) {
        log.warn("Invalid JWT issuer: {}", claims.getIssuer());
        return false;
      }

      // Validate audience
      if (!"avantiq-billing-api".equals(claims.getAudience())) {
        log.warn("Invalid JWT audience: {}", claims.getAudience());
        return false;
      }

      // Validate token type
      if (!"access_token".equals(claims.get("typ", String.class))) {
        log.warn("Invalid JWT token type: {}", claims.get("typ", String.class));
        return false;
      }

      // Validate required claims
      if (claims.getSubject() == null || claims.get("tenantId") == null) {
        log.warn("Missing required claims in JWT token");
        return false;
      }

      return true;
    } catch (JwtException | IllegalArgumentException ex) {
      log.error("Invalid JWT token: {}", ex.getMessage());
      return false;
    }
  }

  /** Check if JWT token is expired. */
  public boolean isTokenExpired(String token) {
    try {
      Claims claims = getClaimsFromToken(token);
      return claims.getExpiration().before(new Date());
    } catch (JwtException | IllegalArgumentException ex) {
      return true;
    }
  }

  /** Get expiration date from JWT token. */
  public Date getExpirationDateFromToken(String token) {
    Claims claims = getClaimsFromToken(token);
    return claims.getExpiration();
  }

  /** Extract all claims from JWT token. */
  private Claims getClaimsFromToken(String token) {
    return Jwts.parserBuilder().setSigningKey(jwtSecret).build().parseClaimsJws(token).getBody();
  }
}
