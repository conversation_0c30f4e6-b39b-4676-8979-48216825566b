package com.avantiq.billing.infrastructure.security.ratelimit;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

/**
 * Rate limiting filter that applies to all HTTP requests. Blocks requests that exceed the
 * configured rate limit per IP address.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class RateLimitFilter extends OncePerRequestFilter {

  private final RateLimitService rateLimitService;

  @Override
  protected void doFilterInternal(
      HttpServletRequest request, HttpServletResponse response, <PERSON><PERSON><PERSON><PERSON><PERSON> filterChain)
      throws ServletException, IOException {

    String clientIp = getClientIpAddress(request);

    if (!rateLimitService.isAllowed(clientIp)) {
      handleRateLimitExceeded(request, response, clientIp);
      return;
    }

    filterChain.doFilter(request, response);
  }

  /** Handle rate limit exceeded by returning HTTP 429 Too Many Requests. */
  private void handleRateLimitExceeded(
      HttpServletRequest request, HttpServletResponse response, String clientIp)
      throws IOException {

    response.setStatus(HttpStatus.TOO_MANY_REQUESTS.value());
    response.setContentType("application/json");
    response.setHeader("Retry-After", "60"); // Suggest retry after 60 seconds

    String errorResponse =
        String.format(
            "{\"error\":\"Rate limit exceeded\",\"message\":\"Too many requests from IP %s\"}",
            clientIp);

    response.getWriter().write(errorResponse);

    log.warn("Rate limit exceeded for IP {} accessing {}", clientIp, request.getRequestURI());
  }

  /** Extract client IP address from request, considering proxies and load balancers. */
  private String getClientIpAddress(HttpServletRequest request) {
    // Check for X-Forwarded-For header (common with load balancers)
    String xForwardedFor = request.getHeader("X-Forwarded-For");
    if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
      // Take the first IP in the chain
      return xForwardedFor.split(",")[0].trim();
    }

    // Check for X-Real-IP header (nginx)
    String xRealIp = request.getHeader("X-Real-IP");
    if (xRealIp != null && !xRealIp.isEmpty()) {
      return xRealIp;
    }

    // Check for X-Client-IP header
    String xClientIp = request.getHeader("X-Client-IP");
    if (xClientIp != null && !xClientIp.isEmpty()) {
      return xClientIp;
    }

    // Fall back to remote address
    return request.getRemoteAddr();
  }

  /** Skip rate limiting for health check endpoints and internal requests. */
  @Override
  protected boolean shouldNotFilter(HttpServletRequest request) {
    String path = request.getRequestURI();

    // Skip rate limiting for health checks and management endpoints
    return path.startsWith("/actuator/health")
        || path.startsWith("/actuator/info")
        || path.startsWith("/management/");
  }
}
