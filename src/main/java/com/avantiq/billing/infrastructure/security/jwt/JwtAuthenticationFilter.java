package com.avantiq.billing.infrastructure.security.jwt;

import com.avantiq.billing.domain.security.model.Role;
import com.avantiq.billing.domain.security.model.UserPrincipal;
import com.avantiq.billing.domain.security.model.UserStatus;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Set;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

/**
 * JWT Authentication Filter for HTTP requests. Extracts and validates JWT tokens from HTTP
 * Authorization header.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class JwtAuthenticationFilter extends OncePerRequestFilter {

  private static final String AUTHORIZATION_HEADER = "Authorization";
  private static final String BEARER_PREFIX = "Bearer ";

  private final JwtTokenProvider jwtTokenProvider;

  @Override
  protected void doFilterInternal(
      HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
      throws ServletException, IOException {

    try {
      String jwt = getJwtFromRequest(request);

      if (StringUtils.hasText(jwt) && jwtTokenProvider.validateToken(jwt)) {
        authenticateUser(jwt, request);
      }
    } catch (Exception ex) {
      log.error("Could not set user authentication in security context", ex);
    }

    filterChain.doFilter(request, response);
  }

  /** Extract JWT token from HTTP request Authorization header. */
  private String getJwtFromRequest(HttpServletRequest request) {
    String bearerToken = request.getHeader(AUTHORIZATION_HEADER);
    if (StringUtils.hasText(bearerToken) && bearerToken.startsWith(BEARER_PREFIX)) {
      return bearerToken.substring(BEARER_PREFIX.length());
    }
    return null;
  }

  /** Authenticate user and set security context. */
  private void authenticateUser(String jwt, HttpServletRequest request) {
    try {
      UUID userId = jwtTokenProvider.getUserIdFromToken(jwt);
      String username = jwtTokenProvider.getUsernameFromToken(jwt);
      Long tenantId = jwtTokenProvider.getTenantIdFromToken(jwt);
      Set<Role> roles = jwtTokenProvider.getRolesFromToken(jwt);
      Set<UUID> segments = jwtTokenProvider.getSegmentsFromToken(jwt);

      // Email is not included in JWT for security - it should be fetched from database if needed
      UserPrincipal userPrincipal =
          UserPrincipal.builder()
              .userId(userId)
              .username(username)
              .email(null) // Email removed from JWT for security
              .tenantId(tenantId)
              .roles(roles)
              .assignedSegments(segments)
              .status(UserStatus.ACTIVE) // JWT presence implies active status
              .build();

      UsernamePasswordAuthenticationToken authentication =
          new UsernamePasswordAuthenticationToken(
              userPrincipal, null, userPrincipal.getAuthorities());

      authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
      SecurityContextHolder.getContext().setAuthentication(authentication);

      log.debug(
          "Set Authentication in SecurityContext for user: {}, tenant: {}", username, tenantId);
    } catch (Exception ex) {
      log.error("Failed to authenticate user from JWT token", ex);
    }
  }
}
