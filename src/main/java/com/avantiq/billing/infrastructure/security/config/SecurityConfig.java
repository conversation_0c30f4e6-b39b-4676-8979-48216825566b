package com.avantiq.billing.infrastructure.security.config;

import com.avantiq.billing.infrastructure.security.jwt.JwtAuthenticationEntryPoint;
import com.avantiq.billing.infrastructure.security.jwt.JwtAuthenticationFilter;
import com.avantiq.billing.infrastructure.security.ratelimit.RateLimitFilter;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfigurationSource;

/**
 * Spring Security configuration for the multi-tenant billing system. Configures JWT-based
 * authentication with method-level security.
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
@RequiredArgsConstructor
public class SecurityConfig {

  private final JwtAuthenticationFilter jwtAuthenticationFilter;
  private final JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;
  private final RateLimitFilter rateLimitFilter;

  @Autowired private CorsConfigurationSource corsConfigurationSource;

  /** Configure the security filter chain for HTTP requests. */
  @Bean
  public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
    http
        // Enable CORS with custom configuration
        .cors(cors -> cors.configurationSource(corsConfigurationSource))

        // Disable CSRF for stateless JWT authentication
        .csrf(AbstractHttpConfigurer::disable)

        // Configure session management to be stateless
        .sessionManagement(
            session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))

        // Configure authorization rules
        .authorizeHttpRequests(
            auth ->
                auth
                    // Allow health check endpoints without authentication
                    .requestMatchers("/actuator/health", "/actuator/info")
                    .permitAll()

                    // Allow authentication endpoints without authentication
                    .requestMatchers("/api/auth/**")
                    .permitAll()

                    // Allow test endpoints without authentication (development only)
                    .requestMatchers("/test/**")
                    .permitAll()

                    // All other requests require authentication
                    .anyRequest()
                    .authenticated())

        // Configure JWT authentication entry point for handling auth errors
        .exceptionHandling(ex -> ex.authenticationEntryPoint(jwtAuthenticationEntryPoint))

        // Configure security headers
        .headers(
            headers ->
                headers
                    .frameOptions(frameOptions -> frameOptions.deny()) // Prevent clickjacking
                    .contentTypeOptions(contentTypeOptions -> {}) // Prevent MIME type sniffing
                    .httpStrictTransportSecurity(
                        hstsConfig ->
                            hstsConfig
                                .maxAgeInSeconds(31536000) // 1 year
                                .includeSubDomains(true)))

        // Add rate limiting filter first, before any authentication
        .addFilterBefore(rateLimitFilter, UsernamePasswordAuthenticationFilter.class)

        // Add JWT filter before username/password authentication filter
        .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

    return http.build();
  }

  /** Configure password encoder for secure password hashing. */
  @Bean
  public PasswordEncoder passwordEncoder() {
    return new BCryptPasswordEncoder(12); // Use strength 12 for good security/performance balance
  }
}
