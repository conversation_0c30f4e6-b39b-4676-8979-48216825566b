package com.avantiq.billing.infrastructure.security.jwt;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

/**
 * JWT Authentication Entry Point that handles authentication failures. Returns proper error
 * responses for unauthenticated requests.
 */
@Component
@Slf4j
public class JwtAuthenticationEntryPoint implements AuthenticationEntryPoint {

  private final ObjectMapper objectMapper = new ObjectMapper();

  @Override
  public void commence(
      HttpServletRequest request,
      HttpServletResponse response,
      AuthenticationException authException)
      throws IOException {

    log.warn("Unauthorized request to {}: {}", request.getRequestURI(), authException.getMessage());

    // Set response status and content type
    response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
    response.setContentType(MediaType.APPLICATION_JSON_VALUE);

    // Create error response body
    Map<String, Object> errorDetails = new HashMap<>();
    errorDetails.put("timestamp", LocalDateTime.now().toString());
    errorDetails.put("status", HttpServletResponse.SC_UNAUTHORIZED);
    errorDetails.put("error", "Unauthorized");
    errorDetails.put("message", "Authentication required");
    errorDetails.put("path", request.getRequestURI());

    // Add specific error details based on the exception type
    if (authException.getMessage() != null) {
      if (authException.getMessage().contains("JWT")) {
        errorDetails.put("details", "Invalid or expired JWT token");
      } else if (authException.getMessage().contains("Bearer")) {
        errorDetails.put("details", "Missing or invalid Authorization header");
      } else {
        errorDetails.put("details", "Authentication credentials are required");
      }
    }

    // Write error response
    response.getWriter().write(objectMapper.writeValueAsString(errorDetails));
  }
}
