package com.avantiq.billing.infrastructure.security.ratelimit;

import java.time.Instant;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * Simple in-memory rate limiting service to prevent API abuse. Implements a sliding window rate
 * limiter per IP address.
 */
@Service
@Slf4j
public class RateLimitService {

  private final int maxRequestsPerWindow;
  private final int windowSizeSeconds;
  private final ConcurrentHashMap<String, RateLimitWindow> rateLimitMap;

  public RateLimitService(
      @Value("${security.rate-limit.max-requests:100}") int maxRequestsPerWindow,
      @Value("${security.rate-limit.window-seconds:60}") int windowSizeSeconds) {
    this.maxRequestsPerWindow = maxRequestsPerWindow;
    this.windowSizeSeconds = windowSizeSeconds;
    this.rateLimitMap = new ConcurrentHashMap<>();

    log.info(
        "Rate limiting initialized: {} requests per {} seconds",
        maxRequestsPerWindow,
        windowSizeSeconds);
  }

  /**
   * Check if a request from the given IP address should be allowed.
   *
   * @param ipAddress the client IP address
   * @return true if request is allowed, false if rate limited
   */
  public boolean isAllowed(String ipAddress) {
    if (ipAddress == null || ipAddress.trim().isEmpty()) {
      log.warn("Rate limit check called with empty IP address");
      return true; // Allow if IP cannot be determined
    }

    long now = Instant.now().getEpochSecond();
    RateLimitWindow window =
        rateLimitMap.computeIfAbsent(
            ipAddress, k -> new RateLimitWindow(now, new AtomicInteger(0)));

    synchronized (window) {
      // Reset window if enough time has passed
      if (now - window.getWindowStart() >= windowSizeSeconds) {
        window.setWindowStart(now);
        window.getRequestCount().set(0);
      }

      int currentCount = window.getRequestCount().incrementAndGet();

      if (currentCount > maxRequestsPerWindow) {
        log.warn("Rate limit exceeded for IP {}: {} requests in window", ipAddress, currentCount);
        return false;
      }

      if (currentCount % 10 == 0) { // Log every 10th request
        log.debug(
            "Rate limit status for IP {}: {}/{} requests",
            ipAddress,
            currentCount,
            maxRequestsPerWindow);
      }

      return true;
    }
  }

  /**
   * Get current request count for an IP address.
   *
   * @param ipAddress the client IP address
   * @return current request count in the window
   */
  public int getCurrentCount(String ipAddress) {
    RateLimitWindow window = rateLimitMap.get(ipAddress);
    return window != null ? window.getRequestCount().get() : 0;
  }

  /**
   * Clear rate limit data for an IP address (for testing or manual reset).
   *
   * @param ipAddress the client IP address
   */
  public void reset(String ipAddress) {
    rateLimitMap.remove(ipAddress);
    log.info("Rate limit reset for IP: {}", ipAddress);
  }

  /**
   * Clear old entries from the rate limit map to prevent memory leaks. Should be called
   * periodically by a scheduled task.
   */
  public void cleanup() {
    long now = Instant.now().getEpochSecond();
    int removed = 0;

    rateLimitMap
        .entrySet()
        .removeIf(
            entry -> {
              long windowAge = now - entry.getValue().getWindowStart();
              return windowAge > (windowSizeSeconds * 2); // Keep for 2x window size
            });

    if (removed > 0) {
      log.debug("Cleaned up {} old rate limit entries", removed);
    }
  }

  /** Get rate limit configuration. */
  public RateLimitConfig getConfig() {
    return new RateLimitConfig(maxRequestsPerWindow, windowSizeSeconds);
  }

  /** Rate limit window tracking. */
  private static class RateLimitWindow {
    private long windowStart;
    private final AtomicInteger requestCount;

    public RateLimitWindow(long windowStart, AtomicInteger requestCount) {
      this.windowStart = windowStart;
      this.requestCount = requestCount;
    }

    public long getWindowStart() {
      return windowStart;
    }

    public void setWindowStart(long windowStart) {
      this.windowStart = windowStart;
    }

    public AtomicInteger getRequestCount() {
      return requestCount;
    }
  }

  /** Rate limit configuration. */
  public static class RateLimitConfig {
    private final int maxRequests;
    private final int windowSeconds;

    public RateLimitConfig(int maxRequests, int windowSeconds) {
      this.maxRequests = maxRequests;
      this.windowSeconds = windowSeconds;
    }

    public int getMaxRequests() {
      return maxRequests;
    }

    public int getWindowSeconds() {
      return windowSeconds;
    }
  }
}
