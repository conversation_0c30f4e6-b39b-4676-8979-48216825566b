package com.avantiq.billing.infrastructure.security.context;

import com.avantiq.billing.domain.security.model.UserPrincipal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

/**
 * Service for extracting tenant context from security context. Ensures proper tenant isolation by
 * providing the current user's tenant ID.
 */
@Service
@Slf4j
public class TenantContextService {

  /**
   * Get the current tenant ID from the security context.
   *
   * @return the tenant ID of the currently authenticated user
   * @throws SecurityException if no authentication is found or tenant ID is not available
   */
  public Long getCurrentTenantId() {
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

    if (authentication == null || !authentication.isAuthenticated()) {
      throw new SecurityException("No authenticated user found in security context");
    }

    Object principal = authentication.getPrincipal();
    if (!(principal instanceof UserPrincipal userPrincipal)) {
      throw new SecurityException("Invalid principal type in security context");
    }

    Long tenantId = userPrincipal.getTenantId();
    if (tenantId == null) {
      throw new SecurityException("Tenant ID not found in user principal");
    }

    log.debug(
        "Retrieved tenant ID {} from security context for user {}",
        tenantId,
        userPrincipal.getUsername());

    return tenantId;
  }

  /**
   * Get the current user principal from the security context.
   *
   * @return the UserPrincipal of the currently authenticated user
   * @throws SecurityException if no authentication is found or principal is invalid
   */
  public UserPrincipal getCurrentUserPrincipal() {
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

    if (authentication == null || !authentication.isAuthenticated()) {
      throw new SecurityException("No authenticated user found in security context");
    }

    Object principal = authentication.getPrincipal();
    if (!(principal instanceof UserPrincipal userPrincipal)) {
      throw new SecurityException("Invalid principal type in security context");
    }

    return userPrincipal;
  }

  /**
   * Check if the current user belongs to the specified tenant.
   *
   * @param tenantId the tenant ID to verify against
   * @throws SecurityException if the user does not belong to the specified tenant
   */
  public void validateTenantAccess(Long tenantId) {
    Long currentTenantId = getCurrentTenantId();

    if (!currentTenantId.equals(tenantId)) {
      throw new SecurityException(
          String.format(
              "Access denied: User belongs to tenant %d but tried to access tenant %d",
              currentTenantId, tenantId));
    }
  }
}
