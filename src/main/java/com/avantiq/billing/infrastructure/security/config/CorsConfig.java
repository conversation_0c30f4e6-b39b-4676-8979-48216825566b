package com.avantiq.billing.infrastructure.security.config;

import java.util.Arrays;
import java.util.List;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

/**
 * CORS configuration for cross-origin resource sharing. Allows controlled access from web browsers
 * to the API.
 */
@Configuration
public class CorsConfig {

  @Value("${cors.allowed.origins:http://localhost:3000}")
  private String allowedOrigins;

  @Value("${cors.allowed.methods:GET,POST,PUT,DELETE,OPTIONS}")
  private String allowedMethods;

  @Value("${cors.allowed.headers:*}")
  private String allowedHeaders;

  @Value("${cors.exposed.headers:Authorization,Content-Type}")
  private String exposedHeaders;

  @Value("${cors.max.age:3600}")
  private Long maxAge;

  @Bean
  public CorsConfigurationSource corsConfigurationSource() {
    CorsConfiguration configuration = new CorsConfiguration();

    // Parse and set allowed origins
    List<String> origins = Arrays.asList(allowedOrigins.split(","));
    configuration.setAllowedOrigins(origins);

    // Parse and set allowed methods
    List<String> methods = Arrays.asList(allowedMethods.split(","));
    configuration.setAllowedMethods(methods);

    // Parse and set allowed headers
    if ("*".equals(allowedHeaders)) {
      configuration.addAllowedHeader("*");
    } else {
      List<String> headers = Arrays.asList(allowedHeaders.split(","));
      configuration.setAllowedHeaders(headers);
    }

    // Parse and set exposed headers
    List<String> exposed = Arrays.asList(exposedHeaders.split(","));
    configuration.setExposedHeaders(exposed);

    // Set max age for preflight requests
    configuration.setMaxAge(maxAge);

    // Allow credentials
    configuration.setAllowCredentials(true);

    UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
    source.registerCorsConfiguration("/**", configuration);

    return source;
  }
}
