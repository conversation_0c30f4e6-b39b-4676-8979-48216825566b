package com.avantiq.billing.infrastructure.rest.common;

import com.avantiq.billing.domain.common.exception.DomainException;
import com.avantiq.billing.domain.customer.exception.CustomerEmailAlreadyExistsException;
import com.avantiq.billing.domain.customer.exception.CustomerNotFoundException;
import com.avantiq.billing.domain.payment.exception.PaymentMethodNotFoundException;
import com.avantiq.billing.domain.product.exception.ProductNotFoundException;
import jakarta.validation.ConstraintViolationException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * Global exception handler for REST controllers. Converts exceptions to standardized API responses.
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

  @ExceptionHandler(CustomerNotFoundException.class)
  public ResponseEntity<ErrorResponse> handleCustomerNotFoundException(
      CustomerNotFoundException ex) {
    return buildResponse(HttpStatus.NOT_FOUND, ex.getMessage(), ex.getErrorCode().getCode());
  }

  @ExceptionHandler(ProductNotFoundException.class)
  public ResponseEntity<ErrorResponse> handleProductNotFoundException(ProductNotFoundException ex) {
    return buildResponse(HttpStatus.NOT_FOUND, ex.getMessage(), ex.getErrorCode().getCode());
  }

  @ExceptionHandler(PaymentMethodNotFoundException.class)
  public ResponseEntity<ErrorResponse> handlePaymentMethodNotFoundException(
      PaymentMethodNotFoundException ex) {
    return buildResponse(HttpStatus.NOT_FOUND, ex.getMessage(), ex.getErrorCode().getCode());
  }

  @ExceptionHandler(CustomerEmailAlreadyExistsException.class)
  public ResponseEntity<ErrorResponse> handleCustomerEmailAlreadyExistsException(
      CustomerEmailAlreadyExistsException ex) {
    return buildResponse(HttpStatus.CONFLICT, ex.getMessage(), ex.getErrorCode().getCode());
  }

  @ExceptionHandler(DomainException.class)
  public ResponseEntity<ErrorResponse> handleDomainException(DomainException ex) {
    return buildResponse(HttpStatus.BAD_REQUEST, ex.getMessage(), ex.getErrorCode().getCode());
  }

  @ExceptionHandler(IllegalArgumentException.class)
  public ResponseEntity<ErrorResponse> handleIllegalArgumentException(IllegalArgumentException ex) {
    return buildResponse(HttpStatus.BAD_REQUEST, ex.getMessage(), 1000); // General validation error
  }

  @ExceptionHandler(MethodArgumentNotValidException.class)
  public ResponseEntity<ValidationErrorResponse> handleValidationExceptions(
      MethodArgumentNotValidException ex) {
    Map<String, String> errors = new HashMap<>();
    ex.getBindingResult()
        .getAllErrors()
        .forEach(
            error -> {
              String fieldName = ((FieldError) error).getField();
              String errorMessage = error.getDefaultMessage();
              errors.put(fieldName, errorMessage);
            });

    ValidationErrorResponse response =
        new ValidationErrorResponse(
            HttpStatus.BAD_REQUEST.value(), "Validation failed", 1000, LocalDateTime.now(), errors);

    return ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .contentType(MediaType.APPLICATION_JSON)
        .body(response);
  }

  @ExceptionHandler(ConstraintViolationException.class)
  public ResponseEntity<ValidationErrorResponse> handleConstraintViolationException(
      ConstraintViolationException ex) {
    Map<String, String> errors = new HashMap<>();
    ex.getConstraintViolations()
        .forEach(
            violation -> {
              String propertyPath = violation.getPropertyPath().toString();
              String message = violation.getMessage();
              errors.put(propertyPath, message);
            });

    ValidationErrorResponse response =
        new ValidationErrorResponse(
            HttpStatus.BAD_REQUEST.value(), "Validation failed", 1000, LocalDateTime.now(), errors);

    return ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .contentType(MediaType.APPLICATION_JSON)
        .body(response);
  }

  @ExceptionHandler(Exception.class)
  public ResponseEntity<ErrorResponse> handleGenericException(Exception ex) {
    // Log the full exception for debugging but don't expose details to client
    log.error("Unexpected error occurred", ex);
    return buildResponse(
        HttpStatus.INTERNAL_SERVER_ERROR,
        "An unexpected error occurred. Please contact support.",
        5000 // General server error
        );
  }

  private ResponseEntity<ErrorResponse> buildResponse(HttpStatus status, String message, int code) {
    ErrorResponse response = new ErrorResponse(status.value(), message, code, LocalDateTime.now());
    return ResponseEntity.status(status).contentType(MediaType.APPLICATION_JSON).body(response);
  }

  /** Standard error response structure. */
  public static class ErrorResponse {
    private final int status;
    private final String message;
    private final int errorCode;
    private final LocalDateTime timestamp;

    public ErrorResponse(int status, String message, int errorCode, LocalDateTime timestamp) {
      this.status = status;
      this.message = message;
      this.errorCode = errorCode;
      this.timestamp = timestamp;
    }

    public int getStatus() {
      return status;
    }

    public String getMessage() {
      return message;
    }

    public int getErrorCode() {
      return errorCode;
    }

    public LocalDateTime getTimestamp() {
      return timestamp;
    }
  }

  /** Validation error response with field-level details. */
  public static class ValidationErrorResponse extends ErrorResponse {
    private final Map<String, String> errors;

    public ValidationErrorResponse(
        int status,
        String message,
        int errorCode,
        LocalDateTime timestamp,
        Map<String, String> errors) {
      super(status, message, errorCode, timestamp);
      this.errors = errors;
    }

    public Map<String, String> getErrors() {
      return errors;
    }
  }
}
