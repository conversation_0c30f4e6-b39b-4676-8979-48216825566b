package com.avantiq.billing.infrastructure.rest;

import com.avantiq.billing.domain.customer.model.CustomerSegment;
import com.avantiq.billing.domain.customer.service.interfaces.CustomerSegmentService;
import com.avantiq.billing.domain.security.model.Role;
import com.avantiq.billing.domain.security.model.UserPrincipal;
import com.avantiq.billing.infrastructure.security.jwt.JwtTokenProvider;
import java.util.Set;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/** Test controller for development purposes. This should be removed in production. */
@RestController
@RequestMapping("/test")
public class TestController {

  @Autowired private JwtTokenProvider jwtTokenProvider;

  @Autowired private CustomerSegmentService customerSegmentService;

  @GetMapping("/token")
  public String generateTestToken() {
    UserPrincipal testUser =
        UserPrincipal.builder()
            .userId(UUID.randomUUID())
            .username("test-user")
            .tenantId(1L)
            .roles(Set.of(Role.USER))
            .assignedSegments(Set.of(UUID.randomUUID()))
            .build();

    return jwtTokenProvider.generateToken(testUser);
  }

  @PostMapping("/segment")
  public String createTestSegment(@RequestBody CreateSegmentRequest request) {
    try {
      CustomerSegment segment = new CustomerSegment();
      segment.setId(UUID.randomUUID()); // Generate UUID for the segment
      segment.setName(request.name);
      segment.setDescription(request.description);
      segment.setTenantId(1L); // Use tenant ID 1 for testing

      CustomerSegment savedSegment = customerSegmentService.save(segment);

      return "Segment created successfully with ID: " + savedSegment.getId();
    } catch (Exception e) {
      return "Error creating segment: " + e.getMessage();
    }
  }

  public static class CreateSegmentRequest {
    public String name;
    public String description;
  }
}
