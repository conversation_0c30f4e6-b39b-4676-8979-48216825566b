package com.avantiq.billing.infrastructure.grpc.config;

import com.avantiq.billing.infrastructure.grpc.common.GrpcExceptionInterceptor;
import net.devh.boot.grpc.server.interceptor.GrpcGlobalServerInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration for gRPC services. Sets up global interceptors and other gRPC-related
 * configurations including security.
 */
@Configuration
public class GrpcConfig {

  /**
   * Registers the global exception interceptor for all gRPC services. This interceptor will handle
   * domain exceptions and convert them to appropriate gRPC status codes.
   *
   * @return the GrpcExceptionInterceptor
   */
  @Bean
  @GrpcGlobalServerInterceptor
  public GrpcExceptionInterceptor exceptionInterceptor() {
    return new GrpcExceptionInterceptor();
  }
}
