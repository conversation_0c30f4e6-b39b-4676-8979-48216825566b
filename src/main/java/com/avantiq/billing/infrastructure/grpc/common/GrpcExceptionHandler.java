package com.avantiq.billing.infrastructure.grpc.common;

import com.avantiq.billing.domain.common.exception.DomainException;
import io.grpc.Metadata;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import lombok.extern.slf4j.Slf4j;

/**
 * Utility class to handle exceptions in gRPC services. Converts domain exceptions to appropriate
 * gRPC status codes.
 */
@Slf4j
public class GrpcExceptionHandler {

  private static final Metadata.Key<String> ERROR_CODE_KEY =
      Metadata.Key.of("error-code", Metadata.ASCII_STRING_MARSHALLER);

  private static final Metadata.Key<String> ERROR_TYPE_KEY =
      Metadata.Key.of("error-type", Metadata.ASCII_STRING_MARSHALLER);

  private GrpcExceptionHandler() {
    // Utility class, no instantiation
  }

  /**
   * Maps a domain exception to a gRPC Status.
   *
   * @param ex The domain exception
   * @return A gRPC Status object with appropriate code and description
   */
  public static Status handleException(DomainException ex) {
    log.error("Domain exception: {}", ex.getMessage(), ex);

    return switch (ex.getErrorCode()) {
      case NOT_FOUND, CUSTOMER_NOT_FOUND, PRODUCT_NOT_FOUND, PAYMENT_METHOD_NOT_FOUND ->
          Status.NOT_FOUND.withDescription(ex.getMessage());

      case ALREADY_EXISTS, CUSTOMER_EMAIL_ALREADY_EXISTS, PRODUCT_SKU_ALREADY_EXISTS ->
          Status.ALREADY_EXISTS.withDescription(ex.getMessage());

      case VALIDATION_ERROR, INVALID_CUSTOMER_DATA, INVALID_PRODUCT_DATA ->
          Status.INVALID_ARGUMENT.withDescription(ex.getMessage());

      case OPERATION_NOT_ALLOWED, CUSTOMER_RELATIONSHIP_VIOLATION ->
          Status.FAILED_PRECONDITION.withDescription(ex.getMessage());

      case PAYMENT_METHOD_INVALID, PAYMENT_PROCESSING_ERROR ->
          Status.FAILED_PRECONDITION.withDescription(ex.getMessage());

      default ->
          Status.INTERNAL.withDescription("An unexpected error occurred: " + ex.getMessage());
    };
  }

  /**
   * Maps a domain exception to a gRPC StatusRuntimeException with metadata.
   *
   * @param ex The domain exception
   * @return A gRPC StatusRuntimeException with appropriate code, description, and metadata
   */
  public static StatusRuntimeException handleExceptionWithMetadata(DomainException ex) {
    Status status = handleException(ex);

    Metadata metadata = new Metadata();
    metadata.put(ERROR_CODE_KEY, String.valueOf(ex.getErrorCode().getCode()));
    metadata.put(ERROR_TYPE_KEY, ex.getErrorCode().name());

    return status.asRuntimeException(metadata);
  }

  /**
   * Maps a general exception to a gRPC Status.
   *
   * @param ex The exception
   * @return A gRPC Status object with appropriate code and description
   */
  public static Status handleException(Exception ex) {
    log.error("Unexpected exception: {}", ex.getMessage(), ex);
    return Status.INTERNAL.withDescription("An unexpected error occurred: " + ex.getMessage());
  }

  /**
   * Maps a general exception to a gRPC StatusRuntimeException with metadata.
   *
   * @param ex The exception
   * @return A gRPC StatusRuntimeException with INTERNAL status code and metadata
   */
  public static StatusRuntimeException handleExceptionWithMetadata(Exception ex) {
    Status status = handleException(ex);

    Metadata metadata = new Metadata();
    metadata.put(ERROR_CODE_KEY, "5000");
    metadata.put(ERROR_TYPE_KEY, "INTERNAL_ERROR");

    return status.asRuntimeException(metadata);
  }
}
