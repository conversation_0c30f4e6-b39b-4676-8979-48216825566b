package com.avantiq.billing.infrastructure.grpc.security;

import com.avantiq.billing.application.security.AuthenticationApplicationService;
import com.avantiq.billing.infrastructure.grpc.proto.AuthenticationData;
import com.avantiq.billing.infrastructure.grpc.proto.AuthenticationServiceGrpc;
import com.avantiq.billing.infrastructure.grpc.proto.LoginRequest;
import com.avantiq.billing.infrastructure.grpc.proto.LoginResponse;
import com.avantiq.billing.infrastructure.grpc.proto.LogoutRequest;
import com.avantiq.billing.infrastructure.grpc.proto.LogoutResponse;
import com.avantiq.billing.infrastructure.grpc.proto.RefreshTokenRequest;
import com.avantiq.billing.infrastructure.grpc.proto.ValidateTokenRequest;
import com.avantiq.billing.infrastructure.grpc.proto.ValidateTokenResponse;
import com.avantiq.billing.infrastructure.security.jwt.JwtTokenProvider;
import io.grpc.Status;
import io.grpc.stub.StreamObserver;
import java.time.ZoneId;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.devh.boot.grpc.server.service.GrpcService;
import org.springframework.security.authentication.BadCredentialsException;

/**
 * gRPC endpoint for authentication operations. Handles login, logout, token refresh, and token
 * validation.
 */
@GrpcService
@RequiredArgsConstructor
@Slf4j
public class AuthenticationGrpcEndpoint
    extends AuthenticationServiceGrpc.AuthenticationServiceImplBase {

  private final AuthenticationApplicationService authenticationService;
  private final JwtTokenProvider jwtTokenProvider;

  @Override
  public void login(LoginRequest request, StreamObserver<LoginResponse> responseObserver) {
    try {
      log.info("Login request for username: {}", request.getUsername());

      // Create login request
      var loginRequest =
          AuthenticationApplicationService.LoginRequest.builder()
              .username(request.getUsername())
              .password(request.getPassword())
              .tenantDomain(request.hasTenantDomain() ? request.getTenantDomain() : null)
              .build();

      // Authenticate user
      var authResult = authenticationService.login(loginRequest);

      // Build successful response
      var authData =
          AuthenticationData.newBuilder()
              .setToken(authResult.getToken())
              .setUserId(authResult.getUserId().toString())
              .setUsername(authResult.getUsername())
              .setTenantId(authResult.getTenantId())
              .setExpiresAt(
                  authResult.getExpiresAt().atZone(ZoneId.systemDefault()).toEpochSecond())
              .build();

      var response =
          LoginResponse.newBuilder()
              .setSuccess(true)
              .setMessage("Login successful")
              .setAuthData(authData)
              .build();

      responseObserver.onNext(response);
      responseObserver.onCompleted();

    } catch (BadCredentialsException ex) {
      log.warn("Invalid login attempt for username: {}", request.getUsername());

      var response =
          LoginResponse.newBuilder()
              .setSuccess(false)
              .setMessage("Invalid username or password")
              .build();

      responseObserver.onNext(response);
      responseObserver.onCompleted();

    } catch (UnsupportedOperationException ex) {
      log.warn("Authentication service not fully implemented: {}", ex.getMessage());

      responseObserver.onError(
          Status.UNIMPLEMENTED
              .withDescription("User authentication requires database implementation")
              .asRuntimeException());

    } catch (Exception ex) {
      log.error("Error during login for username: {}", request.getUsername(), ex);

      responseObserver.onError(
          Status.INTERNAL.withDescription("Internal authentication error").asRuntimeException());
    }
  }

  @Override
  public void refreshToken(
      RefreshTokenRequest request, StreamObserver<LoginResponse> responseObserver) {
    try {
      log.debug("Token refresh request");

      var authResult = authenticationService.refreshToken();

      var authData =
          AuthenticationData.newBuilder()
              .setToken(authResult.getToken())
              .setUserId(authResult.getUserId().toString())
              .setUsername(authResult.getUsername())
              .setTenantId(authResult.getTenantId())
              .setExpiresAt(
                  authResult.getExpiresAt().atZone(ZoneId.systemDefault()).toEpochSecond())
              .build();

      var response =
          LoginResponse.newBuilder()
              .setSuccess(true)
              .setMessage("Token refreshed successfully")
              .setAuthData(authData)
              .build();

      responseObserver.onNext(response);
      responseObserver.onCompleted();

    } catch (BadCredentialsException ex) {
      responseObserver.onError(
          Status.UNAUTHENTICATED.withDescription("Invalid or expired token").asRuntimeException());

    } catch (Exception ex) {
      log.error("Error during token refresh", ex);

      responseObserver.onError(
          Status.INTERNAL.withDescription("Internal token refresh error").asRuntimeException());
    }
  }

  @Override
  public void logout(LogoutRequest request, StreamObserver<LogoutResponse> responseObserver) {
    try {
      log.debug("Logout request");

      authenticationService.logout();

      var response =
          LogoutResponse.newBuilder().setSuccess(true).setMessage("Logout successful").build();

      responseObserver.onNext(response);
      responseObserver.onCompleted();

    } catch (Exception ex) {
      log.error("Error during logout", ex);

      responseObserver.onError(
          Status.INTERNAL.withDescription("Internal logout error").asRuntimeException());
    }
  }

  @Override
  public void validateToken(
      ValidateTokenRequest request, StreamObserver<ValidateTokenResponse> responseObserver) {
    try {
      String token = request.getToken();
      boolean isValid = jwtTokenProvider.validateToken(token);

      if (isValid && !jwtTokenProvider.isTokenExpired(token)) {
        // Extract token information
        var authData =
            AuthenticationData.newBuilder()
                .setUserId(jwtTokenProvider.getUserIdFromToken(token).toString())
                .setUsername(jwtTokenProvider.getUsernameFromToken(token))
                .setTenantId(jwtTokenProvider.getTenantIdFromToken(token))
                .addAllRoles(
                    jwtTokenProvider.getRolesFromToken(token).stream()
                        .map(role -> role.getAuthority())
                        .collect(Collectors.toList()))
                .addAllAssignedSegments(
                    jwtTokenProvider.getSegmentsFromToken(token).stream()
                        .map(uuid -> uuid.toString())
                        .collect(Collectors.toList()))
                .setExpiresAt(
                    jwtTokenProvider.getExpirationDateFromToken(token).toInstant().getEpochSecond())
                .build();

        var response =
            ValidateTokenResponse.newBuilder()
                .setValid(true)
                .setMessage("Token is valid")
                .setAuthData(authData)
                .build();

        responseObserver.onNext(response);
        responseObserver.onCompleted();
      } else {
        var response =
            ValidateTokenResponse.newBuilder()
                .setValid(false)
                .setMessage("Token is invalid or expired")
                .build();

        responseObserver.onNext(response);
        responseObserver.onCompleted();
      }

    } catch (Exception ex) {
      log.error("Error validating token", ex);

      var response =
          ValidateTokenResponse.newBuilder()
              .setValid(false)
              .setMessage("Token validation failed")
              .build();

      responseObserver.onNext(response);
      responseObserver.onCompleted();
    }
  }
}
