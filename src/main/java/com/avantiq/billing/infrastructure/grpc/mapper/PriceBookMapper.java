package com.avantiq.billing.infrastructure.grpc.mapper;

import com.avantiq.billing.domain.product.model.PriceBook;
import com.avantiq.billing.product.grpc.CreatePriceBookRequest;
import com.avantiq.billing.product.grpc.ListPriceBooksResponse;
import com.avantiq.billing.product.grpc.PriceBookResponse;
import com.avantiq.billing.product.grpc.UpdatePriceBookRequest;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/** Mapper for converting between domain PriceBook and gRPC PriceBook messages. */
public class PriceBookMapper {

  private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ISO_DATE;

  private PriceBookMapper() {
    // Utility class, no instantiation
  }

  /**
   * Converts a domain PriceBook to a gRPC PriceBookResponse.
   *
   * @param priceBook the domain PriceBook
   * @return the gRPC PriceBookResponse
   */
  public static PriceBookResponse toProto(PriceBook priceBook) {
    if (priceBook == null) {
      return PriceBookResponse.getDefaultInstance();
    }

    PriceBookResponse.Builder builder = PriceBookResponse.newBuilder();

    if (priceBook.getId() != null) {
      builder.setId(priceBook.getId().toString());
    }

    if (priceBook.getName() != null) {
      builder.setName(priceBook.getName());
    }

    if (priceBook.getSegment() != null) {
      builder.setSegment(priceBook.getSegment());
    }

    if (priceBook.getCurrency() != null) {
      builder.setCurrency(priceBook.getCurrency());
    }

    if (priceBook.getStartDate() != null) {
      builder.setStartDate(priceBook.getStartDate().format(DATE_FORMATTER));
    }

    if (priceBook.getEndDate() != null) {
      builder.setEndDate(priceBook.getEndDate().format(DATE_FORMATTER));
    }

    if (priceBook.getStatus() != null) {
      builder.setStatus(priceBook.getStatus());
    }

    return builder.build();
  }

  /**
   * Converts a gRPC CreatePriceBookRequest to a domain PriceBook.
   *
   * @param request the gRPC CreatePriceBookRequest
   * @return the domain PriceBook
   */
  public static PriceBook toDomain(CreatePriceBookRequest request) {
    if (request == null) {
      return null;
    }

    LocalDate startDate = null;
    if (!request.getStartDate().isEmpty()) {
      startDate = LocalDate.parse(request.getStartDate(), DATE_FORMATTER);
    }

    LocalDate endDate = null;
    if (!request.getEndDate().isEmpty()) {
      endDate = LocalDate.parse(request.getEndDate(), DATE_FORMATTER);
    }

    return new PriceBook(
        null, // ID will be set by the service
        request.getName(),
        request.getSegment(),
        request.getCurrency(),
        startDate,
        endDate,
        request.getStatus());
  }

  /**
   * Converts a gRPC UpdatePriceBookRequest to a domain PriceBook.
   *
   * @param request the gRPC UpdatePriceBookRequest
   * @return the domain PriceBook
   */
  public static PriceBook toDomain(UpdatePriceBookRequest request) {
    if (request == null || request.getId().isEmpty()) {
      return null;
    }

    UUID id = UUID.fromString(request.getId());

    LocalDate startDate = null;
    if (!request.getStartDate().isEmpty()) {
      startDate = LocalDate.parse(request.getStartDate(), DATE_FORMATTER);
    }

    LocalDate endDate = null;
    if (!request.getEndDate().isEmpty()) {
      endDate = LocalDate.parse(request.getEndDate(), DATE_FORMATTER);
    }

    return new PriceBook(
        id,
        request.getName(),
        request.getSegment(),
        request.getCurrency(),
        startDate,
        endDate,
        request.getStatus());
  }

  /**
   * Converts a list of domain PriceBook to a gRPC ListPriceBooksResponse.
   *
   * @param priceBooks the list of domain PriceBook
   * @return the gRPC ListPriceBooksResponse
   */
  public static ListPriceBooksResponse toGrpcListResponse(List<PriceBook> priceBooks) {
    List<PriceBookResponse> responses =
        priceBooks.stream().map(PriceBookMapper::toProto).collect(Collectors.toList());

    return ListPriceBooksResponse.newBuilder().addAllPriceBooks(responses).build();
  }
}
