package com.avantiq.billing.infrastructure.grpc.mapper;

import com.avantiq.billing.domain.product.model.Bundle;
import com.avantiq.billing.domain.product.model.BundleProduct;
import com.avantiq.billing.product.grpc.AddProductToBundleRequest;
import com.avantiq.billing.product.grpc.BundleProductResponse;
import com.avantiq.billing.product.grpc.BundleResponse;
import com.avantiq.billing.product.grpc.CreateBundleRequest;
import com.avantiq.billing.product.grpc.ListBundlesResponse;
import com.avantiq.billing.product.grpc.UpdateBundleRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/** Mapper for converting between domain Bundle and gRPC Bundle messages. */
public class BundleMapper {

  private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ISO_DATE_TIME;

  private BundleMapper() {
    // Utility class, no instantiation
  }

  /**
   * Converts a domain Bundle to a gRPC BundleResponse.
   *
   * @param bundle the domain Bundle
   * @param bundleProducts the list of BundleProducts associated with the bundle
   * @return the gRPC BundleResponse
   */
  public static BundleResponse toProto(Bundle bundle, List<BundleProduct> bundleProducts) {
    if (bundle == null) {
      return BundleResponse.getDefaultInstance();
    }

    BundleResponse.Builder builder = BundleResponse.newBuilder();

    if (bundle.getBundleId() != null) {
      builder.setId(bundle.getBundleId().toString());
    }

    if (bundle.getName() != null) {
      builder.setName(bundle.getName());
    }

    if (bundle.getDescription() != null) {
      builder.setDescription(bundle.getDescription());
    }

    if (bundle.getStatus() != null) {
      builder.setStatus(bundle.getStatus());
    }

    if (bundle.getPricingStrategy() != null) {
      builder.setPricingStrategy(bundle.getPricingStrategy());
    }

    builder.setVersion(bundle.getVersion());

    if (bundle.getCreatedAt() != null) {
      builder.setCreatedAt(bundle.getCreatedAt().format(DATE_TIME_FORMATTER));
    }

    if (bundle.getUpdatedAt() != null) {
      builder.setUpdatedAt(bundle.getUpdatedAt().format(DATE_TIME_FORMATTER));
    }

    // Add bundle products
    if (bundleProducts != null) {
      List<BundleProductResponse> bundleProductResponses =
          bundleProducts.stream().map(BundleMapper::toProto).collect(Collectors.toList());
      builder.addAllBundleProducts(bundleProductResponses);
    }

    return builder.build();
  }

  /**
   * Converts a domain Bundle to a gRPC BundleResponse without bundle products.
   *
   * @param bundle the domain Bundle
   * @return the gRPC BundleResponse
   */
  public static BundleResponse toProto(Bundle bundle) {
    return toProto(bundle, new ArrayList<>());
  }

  /**
   * Converts a domain BundleProduct to a gRPC BundleProductResponse.
   *
   * @param bundleProduct the domain BundleProduct
   * @return the gRPC BundleProductResponse
   */
  public static BundleProductResponse toProto(BundleProduct bundleProduct) {
    if (bundleProduct == null) {
      return BundleProductResponse.getDefaultInstance();
    }

    BundleProductResponse.Builder builder = BundleProductResponse.newBuilder();

    if (bundleProduct.getId() != null) {
      builder.setId(bundleProduct.getId().toString());
    }

    if (bundleProduct.getBundleId() != null) {
      builder.setBundleId(bundleProduct.getBundleId().toString());
    }

    if (bundleProduct.getProductId() != null) {
      builder.setProductId(bundleProduct.getProductId().toString());
    }

    builder.setQuantity(bundleProduct.getQuantity());
    builder.setOptionalFlag(bundleProduct.isOptionalFlag());

    return builder.build();
  }

  /**
   * Converts a gRPC CreateBundleRequest to a domain Bundle.
   *
   * @param request the gRPC CreateBundleRequest
   * @return the domain Bundle
   */
  public static Bundle toDomain(CreateBundleRequest request) {
    if (request == null) {
      return null;
    }

    LocalDateTime now = LocalDateTime.now();

    return new Bundle(
        null, // ID will be set by the service
        request.getName(),
        request.getDescription(),
        request.getStatus(),
        request.getPricingStrategy(),
        1L, // Initial version
        now,
        now);
  }

  /**
   * Converts a gRPC UpdateBundleRequest to a domain Bundle.
   *
   * @param request the gRPC UpdateBundleRequest
   * @param currentVersion the current version of the bundle
   * @return the domain Bundle
   */
  public static Bundle toDomain(UpdateBundleRequest request, long currentVersion) {
    if (request == null || request.getId().isEmpty()) {
      return null;
    }

    UUID id = UUID.fromString(request.getId());
    LocalDateTime now = LocalDateTime.now();

    return new Bundle(
        id,
        request.getName(),
        request.getDescription(),
        request.getStatus(),
        request.getPricingStrategy(),
        currentVersion, // Keep the current version, will be incremented by service
        null, // Keep createdAt as is
        now);
  }

  /**
   * Converts a gRPC AddProductToBundleRequest to a domain BundleProduct.
   *
   * @param request the gRPC AddProductToBundleRequest
   * @return the domain BundleProduct
   */
  public static BundleProduct toDomain(AddProductToBundleRequest request) {
    if (request == null) {
      return null;
    }

    return new BundleProduct(
        null, // ID will be set by the service
        UUID.fromString(request.getBundleId()),
        UUID.fromString(request.getProductId()),
        request.getQuantity(),
        request.getOptionalFlag());
  }

  /**
   * Converts a list of domain Bundle to a gRPC ListBundlesResponse.
   *
   * @param bundles the list of domain Bundle
   * @param bundleProducts the list of all BundleProducts
   * @return the gRPC ListBundlesResponse
   */
  public static ListBundlesResponse toGrpcListResponse(
      List<Bundle> bundles, List<BundleProduct> bundleProducts) {
    List<BundleResponse> responses = new ArrayList<>();

    for (Bundle bundle : bundles) {
      List<BundleProduct> bundleProductsForBundle =
          bundleProducts.stream()
              .filter(
                  bp ->
                      bp.getBundleId() != null
                          && bundle.getBundleId() != null
                          && bp.getBundleId().equals(bundle.getBundleId()))
              .collect(Collectors.toList());

      responses.add(toProto(bundle, bundleProductsForBundle));
    }

    return ListBundlesResponse.newBuilder().addAllBundles(responses).build();
  }
}
