package com.avantiq.billing.infrastructure.grpc.customer;

import com.avantiq.billing.application.customer.ContactApplicationService;
import com.avantiq.billing.application.customer.CustomerApplicationService;
import com.avantiq.billing.application.customer.CustomerRelationshipApplicationService;
import com.avantiq.billing.application.customer.CustomerSegmentApplicationService;
import com.avantiq.billing.application.payment.PaymentMethodApplicationService;
import com.avantiq.billing.customer.grpc.AddContactRequest;
import com.avantiq.billing.customer.grpc.AddPaymentMethodRequest;
import com.avantiq.billing.customer.grpc.ContactResponse;
import com.avantiq.billing.customer.grpc.CreateCustomerRequest;
import com.avantiq.billing.customer.grpc.CreateSegmentRequest;
import com.avantiq.billing.customer.grpc.CustomerRelationshipResponse;
import com.avantiq.billing.customer.grpc.CustomerResponse;
import com.avantiq.billing.customer.grpc.CustomerServiceGrpc;
import com.avantiq.billing.customer.grpc.GetCustomerByEmailRequest;
import com.avantiq.billing.customer.grpc.GetCustomerRequest;
import com.avantiq.billing.customer.grpc.LinkCustomerRelationshipRequest;
import com.avantiq.billing.customer.grpc.ListContactsRequest;
import com.avantiq.billing.customer.grpc.ListContactsResponse;
import com.avantiq.billing.customer.grpc.ListSegmentsRequest;
import com.avantiq.billing.customer.grpc.ListSegmentsResponse;
import com.avantiq.billing.customer.grpc.PaymentMethodResponse;
import com.avantiq.billing.customer.grpc.Segment;
import com.avantiq.billing.customer.grpc.SegmentResponse;
import com.avantiq.billing.customer.grpc.UpdateContactRequest;
import com.avantiq.billing.customer.grpc.UpdateCustomerRequest;
import com.avantiq.billing.domain.customer.exception.CustomerEmailAlreadyExistsException;
import com.avantiq.billing.domain.customer.exception.CustomerNotFoundException;
import com.avantiq.billing.domain.customer.exception.InvalidCustomerDataException;
import com.avantiq.billing.domain.customer.model.CustomerSegment;
import com.avantiq.billing.domain.payment.model.PaymentMethod;
import com.avantiq.billing.infrastructure.grpc.common.GrpcExceptionHandler;
import com.avantiq.billing.infrastructure.grpc.mapper.CustomerMapper;
import com.avantiq.billing.infrastructure.security.context.TenantContextService;
import io.grpc.stub.StreamObserver;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import net.devh.boot.grpc.server.service.GrpcService;
import org.springframework.beans.factory.annotation.Autowired;

@GrpcService
@Slf4j
public class CustomerGrpcEndpoint extends CustomerServiceGrpc.CustomerServiceImplBase {
  private final CustomerApplicationService customerApplicationService;
  private final ContactApplicationService contactApplicationService;
  private final CustomerSegmentApplicationService customerSegmentApplicationService;
  private final PaymentMethodApplicationService paymentMethodApplicationService;
  private final CustomerRelationshipApplicationService customerRelationshipApplicationService;
  private final TenantContextService tenantContextService;

  @Autowired
  public CustomerGrpcEndpoint(
      CustomerApplicationService customerApplicationService,
      ContactApplicationService contactApplicationService,
      CustomerSegmentApplicationService customerSegmentApplicationService,
      PaymentMethodApplicationService paymentMethodApplicationService,
      CustomerRelationshipApplicationService customerRelationshipApplicationService,
      TenantContextService tenantContextService) {
    this.customerApplicationService = customerApplicationService;
    this.contactApplicationService = contactApplicationService;
    this.customerSegmentApplicationService = customerSegmentApplicationService;
    this.paymentMethodApplicationService = paymentMethodApplicationService;
    this.customerRelationshipApplicationService = customerRelationshipApplicationService;
    this.tenantContextService = tenantContextService;
  }

  @Override
  public void createCustomer(
      CreateCustomerRequest request, StreamObserver<CustomerResponse> responseObserver) {
    try {
      // Check for duplicate email
      if (customerApplicationService.existsByEmail(request.getEmail())) {
        throw new CustomerEmailAlreadyExistsException(request.getEmail());
      }

      // Validate request data
      if (request.getFirstName().isEmpty()
          || request.getLastName().isEmpty()
          || request.getEmail().isEmpty()) {
        throw new InvalidCustomerDataException("First name, last name, and email are required");
      }

      // Map proto to domain
      var customer = CustomerMapper.toDomain(request);

      // Save
      var saved = customerApplicationService.createCustomer(customer);

      // Map domain to proto
      var protoCustomer = CustomerMapper.toProto(saved);
      CustomerResponse response = CustomerResponse.newBuilder().setCustomer(protoCustomer).build();

      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (CustomerEmailAlreadyExistsException | InvalidCustomerDataException e) {
      // Let the global interceptor handle the exception
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    } catch (Exception e) {
      log.error("Error in createCustomer", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  @Override
  public void updateCustomer(
      UpdateCustomerRequest request, StreamObserver<CustomerResponse> responseObserver) {
    try {
      UUID id;
      try {
        id = UUID.fromString(request.getId());
      } catch (IllegalArgumentException e) {
        throw new InvalidCustomerDataException("Invalid customer ID format", e);
      }

      Optional<com.avantiq.billing.domain.customer.model.Customer> opt =
          customerApplicationService.findByIdWithAddresses(id);

      if (opt.isEmpty()) {
        throw new CustomerNotFoundException(request.getId());
      }

      var customer = opt.get();

      // Update fields
      CustomerMapper.updateDomainFromProto(request, customer);

      // Validate customer data after update
      if (customer.getFirstName() == null
          || customer.getLastName() == null
          || customer.getEmail() == null) {
        throw new InvalidCustomerDataException("First name, last name, and email cannot be null");
      }

      var saved = customerApplicationService.updateCustomer(customer.getId(), customer);
      var protoCustomer = CustomerMapper.toProto(saved);
      CustomerResponse response = CustomerResponse.newBuilder().setCustomer(protoCustomer).build();

      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (CustomerNotFoundException | InvalidCustomerDataException e) {
      // Let the global interceptor handle domain exceptions
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    } catch (Exception e) {
      log.error("Error in updateCustomer", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  @Override
  public void getCustomer(
      GetCustomerRequest request, StreamObserver<CustomerResponse> responseObserver) {
    try {
      UUID id;
      try {
        id = UUID.fromString(request.getId());
      } catch (IllegalArgumentException e) {
        throw new InvalidCustomerDataException("Invalid customer ID format", e);
      }

      Optional<com.avantiq.billing.domain.customer.model.Customer> opt =
          customerApplicationService.findByIdWithAddresses(id);

      if (opt.isEmpty()) {
        throw new CustomerNotFoundException(request.getId());
      }

      var customer = opt.get();
      var protoCustomer = CustomerMapper.toProto(customer);
      CustomerResponse response = CustomerResponse.newBuilder().setCustomer(protoCustomer).build();

      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (CustomerNotFoundException | InvalidCustomerDataException e) {
      // Let the global interceptor handle domain exceptions
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    } catch (Exception e) {
      log.error("Error in getCustomer", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  @Override
  public void linkCustomerRelationship(
      LinkCustomerRelationshipRequest request,
      StreamObserver<CustomerRelationshipResponse> responseObserver) {
    try {
      UUID parentId;
      UUID childId;

      try {
        parentId = UUID.fromString(request.getParentId());
        childId = UUID.fromString(request.getChildId());
      } catch (IllegalArgumentException e) {
        throw new InvalidCustomerDataException("Invalid customer ID format", e);
      }

      // Check if customers exist
      if (customerApplicationService.findById(parentId).isEmpty()) {
        throw new CustomerNotFoundException(request.getParentId());
      }

      if (customerApplicationService.findById(childId).isEmpty()) {
        throw new CustomerNotFoundException(request.getChildId());
      }

      // Logic to link customer relationships
      customerRelationshipApplicationService.linkRelationship(parentId, childId, request.getType());

      CustomerRelationshipResponse response =
          CustomerRelationshipResponse.newBuilder().setSuccess(true).build();
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (CustomerNotFoundException | InvalidCustomerDataException e) {
      log.error("Error in linkCustomerRelationship", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    } catch (Exception e) {
      log.error("Unexpected error in linkCustomerRelationship", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  @Override
  public void addPaymentMethod(
      AddPaymentMethodRequest request, StreamObserver<PaymentMethodResponse> responseObserver) {
    try {
      UUID customerId;
      try {
        customerId = UUID.fromString(request.getCustomerId());
      } catch (IllegalArgumentException e) {
        throw new InvalidCustomerDataException("Invalid customer ID format", e);
      }

      // Check if customer exists
      if (customerApplicationService.findById(customerId).isEmpty()) {
        throw new CustomerNotFoundException(request.getCustomerId());
      }

      // Validate payment details
      if (request.getDetails() == null || request.getDetails().isEmpty()) {
        throw new InvalidCustomerDataException("Payment details cannot be empty");
      }

      // Logic to add a payment method
      Long tenantId = tenantContextService.getCurrentTenantId();

      PaymentMethodApplicationService.AddPaymentMethodRequest paymentRequest =
          PaymentMethodApplicationService.AddPaymentMethodRequest.builder()
              .customerId(customerId)
              .tenantId(tenantId)
              .type(parsePaymentMethodType(request.getType()))
              .details(request.getDetails())
              .isPrimary(request.getIsPrimary())
              .build();

      paymentMethodApplicationService.addPaymentMethod(paymentRequest);

      PaymentMethodResponse response = PaymentMethodResponse.newBuilder().setSuccess(true).build();
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (CustomerNotFoundException | InvalidCustomerDataException e) {
      log.error("Error in addPaymentMethod", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    } catch (Exception e) {
      log.error("Unexpected error in addPaymentMethod", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  @Override
  public void listSegments(
      ListSegmentsRequest request, StreamObserver<ListSegmentsResponse> responseObserver) {
    try {
      // Logic to list customer segments
      List<CustomerSegment> customerSegments = customerSegmentApplicationService.listSegments();
      List<Segment> segments = customerSegments.stream().map(CustomerMapper::toProto).toList();

      ListSegmentsResponse.Builder responseBuilder = ListSegmentsResponse.newBuilder();
      segments.forEach(responseBuilder::addSegments);

      responseObserver.onNext(responseBuilder.build());
      responseObserver.onCompleted();
    } catch (Exception e) {
      log.error("Unexpected error in listSegments", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  @Override
  public void createSegment(
      CreateSegmentRequest request, StreamObserver<SegmentResponse> responseObserver) {
    try {
      // Validate request data
      if (request.getName().isEmpty()) {
        throw new InvalidCustomerDataException("Segment name is required");
      }

      // Create segment domain object
      CustomerSegment segment = new CustomerSegment();
      segment.setId(UUID.randomUUID()); // Generate UUID for the segment
      segment.setName(request.getName());
      segment.setDescription(request.getDescription());
      segment.setTenantId(tenantContextService.getCurrentTenantId());

      // Save segment
      CustomerSegment savedSegment = customerSegmentApplicationService.save(segment);

      // Map to proto
      Segment protoSegment = CustomerMapper.toProto(savedSegment);
      SegmentResponse response =
          SegmentResponse.newBuilder().setSegment(protoSegment).setSuccess(true).build();

      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (Exception e) {
      log.error("Unexpected error in createSegment", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  @Override
  public void addContact(
      AddContactRequest request, StreamObserver<ContactResponse> responseObserver) {
    try {
      UUID customerId;
      try {
        customerId = UUID.fromString(request.getCustomerId());
      } catch (IllegalArgumentException e) {
        throw new InvalidCustomerDataException("Invalid customer ID format", e);
      }

      // Check if customer exists
      if (customerApplicationService.findById(customerId).isEmpty()) {
        throw new CustomerNotFoundException(request.getCustomerId());
      }

      // Validate contact data
      if (request.getEmail() == null || request.getEmail().isEmpty()) {
        throw new InvalidCustomerDataException("Contact email is required");
      }

      // Logic to add a contact
      contactApplicationService.addContact(
          customerId,
          request.getFirstName(),
          request.getLastName(),
          request.getEmail(),
          request.getContactType(),
          request.getIsDefault());

      ContactResponse response = ContactResponse.newBuilder().setSuccess(true).build();
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (CustomerNotFoundException | InvalidCustomerDataException e) {
      log.error("Error in addContact", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    } catch (Exception e) {
      log.error("Unexpected error in addContact", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  @Override
  public void listContacts(
      ListContactsRequest request, StreamObserver<ListContactsResponse> responseObserver) {
    try {
      UUID customerId;
      try {
        customerId = UUID.fromString(request.getCustomerId());
      } catch (IllegalArgumentException e) {
        throw new InvalidCustomerDataException("Invalid customer ID format", e);
      }

      // Check if customer exists
      if (customerApplicationService.findById(customerId).isEmpty()) {
        throw new CustomerNotFoundException(request.getCustomerId());
      }

      // Logic to list contacts
      List<com.avantiq.billing.domain.customer.model.Contact> contacts =
          contactApplicationService.listContacts(customerId);

      ListContactsResponse.Builder responseBuilder = ListContactsResponse.newBuilder();
      contacts.stream().map(CustomerMapper::toProto).forEach(responseBuilder::addContacts);

      responseObserver.onNext(responseBuilder.build());
      responseObserver.onCompleted();
    } catch (CustomerNotFoundException | InvalidCustomerDataException e) {
      log.error("Error in listContacts", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    } catch (Exception e) {
      log.error("Unexpected error in listContacts", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  @Override
  public void updateContact(
      UpdateContactRequest request, StreamObserver<ContactResponse> responseObserver) {
    try {
      UUID contactId;
      try {
        contactId = UUID.fromString(request.getContactId());
      } catch (IllegalArgumentException e) {
        throw new InvalidCustomerDataException("Invalid contact ID format", e);
      }

      // Validate contact data
      if (request.getEmail() != null && request.getEmail().isEmpty()) {
        throw new InvalidCustomerDataException("Contact email cannot be empty if provided");
      }

      // Logic to update a contact
      contactApplicationService.updateContact(
          contactId,
          request.getFirstName(),
          request.getLastName(),
          request.getEmail(),
          request.getContactType(),
          request.getIsDefault());

      ContactResponse response = ContactResponse.newBuilder().setSuccess(true).build();
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (InvalidCustomerDataException e) {
      log.error("Error in updateContact", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    } catch (Exception e) {
      log.error("Unexpected error in updateContact", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  @Override
  public void getCustomerByEmail(
      GetCustomerByEmailRequest request, StreamObserver<CustomerResponse> responseObserver) {
    try {
      // Get deep flag from request if available
      boolean deep = request.getDeep();

      // Find customer by email
      Optional<com.avantiq.billing.domain.customer.model.Customer> opt;
      if (deep) {
        opt = customerApplicationService.findByEmailWithAddresses(request.getEmail());
      } else {
        opt = customerApplicationService.findByEmail(request.getEmail());
      }

      // Throw domain exception if not found
      if (opt.isEmpty()) {
        throw new CustomerNotFoundException("Customer not found with email: " + request.getEmail());
      }

      // Process result
      var customer = opt.get();
      var protoCustomer = CustomerMapper.toProto(customer);
      CustomerResponse response = CustomerResponse.newBuilder().setCustomer(protoCustomer).build();
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (CustomerNotFoundException e) {
      // Let the global interceptor handle domain exceptions
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    } catch (Exception e) {
      log.error("Error in getCustomerByEmail", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  /** Helper method to parse payment method type from string. */
  private PaymentMethod.PaymentMethodType parsePaymentMethodType(String type) {
    if (type == null || type.trim().isEmpty()) {
      return PaymentMethod.PaymentMethodType.CREDIT_CARD; // Default
    }

    try {
      return PaymentMethod.PaymentMethodType.valueOf(type.toUpperCase());
    } catch (IllegalArgumentException e) {
      log.warn("Unknown payment method type: {}, defaulting to CREDIT_CARD", type);
      return PaymentMethod.PaymentMethodType.CREDIT_CARD;
    }
  }
}
