package com.avantiq.billing.infrastructure.grpc.common;

import com.avantiq.billing.domain.common.exception.DomainException;
import com.avantiq.billing.domain.customer.exception.CustomerEmailAlreadyExistsException;
import com.avantiq.billing.domain.customer.exception.CustomerNotFoundException;
import com.avantiq.billing.domain.payment.exception.PaymentMethodNotFoundException;
import com.avantiq.billing.domain.product.exception.ProductNotFoundException;
import io.grpc.ForwardingServerCall;
import io.grpc.Metadata;
import io.grpc.ServerCall;
import io.grpc.ServerCallHandler;
import io.grpc.ServerInterceptor;
import io.grpc.Status;
import lombok.extern.slf4j.Slf4j;

/**
 * Global interceptor for handling exceptions in gRPC services. Maps domain and general exceptions
 * to appropriate gRPC status codes and error details.
 */
@Slf4j
public class GrpcExceptionInterceptor implements ServerInterceptor {

  private static final Metadata.Key<String> ERROR_CODE_KEY =
      Metadata.Key.of("error-code", Metadata.ASCII_STRING_MARSHALLER);

  private static final Metadata.Key<String> ERROR_TYPE_KEY =
      Metadata.Key.of("error-type", Metadata.ASCII_STRING_MARSHALLER);

  // No-op listener for returning after handling exceptions
  private static class NoOpServerCallListener<ReqT> extends ServerCall.Listener<ReqT> {
    // All methods are empty as we've already handled the exception
  }

  @Override
  public <ReqT, RespT> ServerCall.Listener<ReqT> interceptCall(
      ServerCall<ReqT, RespT> call, Metadata headers, ServerCallHandler<ReqT, RespT> next) {

    ServerCall<ReqT, RespT> wrappedCall =
        new ForwardingServerCall.SimpleForwardingServerCall<ReqT, RespT>(call) {
          @Override
          public void close(Status status, Metadata trailers) {
            // We don't modify successful responses
            super.close(status, trailers);
          }
        };

    try {
      return new ExceptionHandlingServerCallListener<>(
          next.startCall(wrappedCall, headers), wrappedCall);
    } catch (CustomerNotFoundException ex) {
      handleException(wrappedCall, ex, Status.NOT_FOUND);
      return new NoOpServerCallListener<>();
    } catch (ProductNotFoundException ex) {
      handleException(wrappedCall, ex, Status.NOT_FOUND);
      return new NoOpServerCallListener<>();
    } catch (PaymentMethodNotFoundException ex) {
      handleException(wrappedCall, ex, Status.NOT_FOUND);
      return new NoOpServerCallListener<>();
    } catch (CustomerEmailAlreadyExistsException ex) {
      handleException(wrappedCall, ex, Status.ALREADY_EXISTS);
      return new NoOpServerCallListener<>();
    } catch (DomainException ex) {
      handleDomainException(wrappedCall, ex);
      return new NoOpServerCallListener<>();
    } catch (IllegalArgumentException ex) {
      handleException(wrappedCall, ex, Status.INVALID_ARGUMENT, "VALIDATION_ERROR", "1000");
      return new NoOpServerCallListener<>();
    } catch (Exception ex) {
      handleException(wrappedCall, ex, Status.INTERNAL, "INTERNAL_ERROR", "5000");
      return new NoOpServerCallListener<>();
    }
  }

  // Handle domain exceptions with proper status mapping
  private <ReqT, RespT> void handleDomainException(
      ServerCall<ReqT, RespT> call, DomainException ex) {
    Status status;
    switch (ex.getErrorCode()) {
      case NOT_FOUND:
      case CUSTOMER_NOT_FOUND:
      case PRODUCT_NOT_FOUND:
      case PAYMENT_METHOD_NOT_FOUND:
        status = Status.NOT_FOUND;
        break;

      case ALREADY_EXISTS:
      case CUSTOMER_EMAIL_ALREADY_EXISTS:
      case PRODUCT_SKU_ALREADY_EXISTS:
        status = Status.ALREADY_EXISTS;
        break;

      case VALIDATION_ERROR:
      case INVALID_CUSTOMER_DATA:
      case INVALID_PRODUCT_DATA:
        status = Status.INVALID_ARGUMENT;
        break;

      case OPERATION_NOT_ALLOWED:
      case CUSTOMER_RELATIONSHIP_VIOLATION:
        status = Status.FAILED_PRECONDITION;
        break;

      case PAYMENT_METHOD_INVALID:
      case PAYMENT_PROCESSING_ERROR:
        status = Status.FAILED_PRECONDITION;
        break;

      default:
        status = Status.INTERNAL;
        break;
    }

    handleException(
        call, ex, status, ex.getErrorCode().name(), String.valueOf(ex.getErrorCode().getCode()));
  }

  // Handle exceptions with default error metadata
  private <ReqT, RespT> void handleException(
      ServerCall<ReqT, RespT> call, Exception ex, Status status) {
    handleException(call, ex, status, "UNKNOWN", "0");
  }

  // Handle exceptions with custom error metadata
  private <ReqT, RespT> void handleException(
      ServerCall<ReqT, RespT> call,
      Exception ex,
      Status status,
      String errorType,
      String errorCode) {
    log.error(
        "gRPC call failed with " + errorType + " (" + errorCode + "): " + ex.getMessage(), ex);

    Metadata trailers = new Metadata();
    trailers.put(ERROR_CODE_KEY, errorCode);
    trailers.put(ERROR_TYPE_KEY, errorType);

    call.close(status.withDescription(ex.getMessage()), trailers);
  }

  private class ExceptionHandlingServerCallListener<ReqT, RespT>
      extends io.grpc.ForwardingServerCallListener.SimpleForwardingServerCallListener<ReqT> {

    private final ServerCall<ReqT, RespT> serverCall;

    ExceptionHandlingServerCallListener(
        ServerCall.Listener<ReqT> listener, ServerCall<ReqT, RespT> serverCall) {
      super(listener);
      this.serverCall = serverCall;
    }

    @Override
    public void onHalfClose() {
      try {
        super.onHalfClose();
      } catch (CustomerNotFoundException ex) {
        handleException(ex, Status.NOT_FOUND);
      } catch (ProductNotFoundException ex) {
        handleException(ex, Status.NOT_FOUND);
      } catch (PaymentMethodNotFoundException ex) {
        handleException(ex, Status.NOT_FOUND);
      } catch (CustomerEmailAlreadyExistsException ex) {
        handleException(ex, Status.ALREADY_EXISTS);
      } catch (DomainException ex) {
        handleDomainException(ex);
      } catch (IllegalArgumentException ex) {
        handleException(ex, Status.INVALID_ARGUMENT, "VALIDATION_ERROR", "1000");
      } catch (Exception ex) {
        handleException(ex, Status.INTERNAL, "INTERNAL_ERROR", "5000");
      }
    }

    @Override
    public void onReady() {
      try {
        super.onReady();
      } catch (Exception ex) {
        handleException(ex, Status.INTERNAL, "INTERNAL_ERROR", "5000");
      }
    }

    private void handleDomainException(DomainException ex) {
      Status status;
      switch (ex.getErrorCode()) {
        case NOT_FOUND:
        case CUSTOMER_NOT_FOUND:
        case PRODUCT_NOT_FOUND:
        case PAYMENT_METHOD_NOT_FOUND:
          status = Status.NOT_FOUND;
          break;

        case ALREADY_EXISTS:
        case CUSTOMER_EMAIL_ALREADY_EXISTS:
        case PRODUCT_SKU_ALREADY_EXISTS:
          status = Status.ALREADY_EXISTS;
          break;

        case VALIDATION_ERROR:
        case INVALID_CUSTOMER_DATA:
        case INVALID_PRODUCT_DATA:
          status = Status.INVALID_ARGUMENT;
          break;

        case OPERATION_NOT_ALLOWED:
        case CUSTOMER_RELATIONSHIP_VIOLATION:
          status = Status.FAILED_PRECONDITION;
          break;

        case PAYMENT_METHOD_INVALID:
        case PAYMENT_PROCESSING_ERROR:
          status = Status.FAILED_PRECONDITION;
          break;

        default:
          status = Status.INTERNAL;
          break;
      }

      handleException(
          ex, status, ex.getErrorCode().name(), String.valueOf(ex.getErrorCode().getCode()));
    }

    private void handleException(Exception ex, Status status) {
      handleException(ex, status, "UNKNOWN", "0");
    }

    private void handleException(Exception ex, Status status, String errorType, String errorCode) {
      log.error(
          "gRPC call failed with " + errorType + " (" + errorCode + "): " + ex.getMessage(), ex);

      Metadata trailers = new Metadata();
      trailers.put(ERROR_CODE_KEY, errorCode);
      trailers.put(ERROR_TYPE_KEY, errorType);

      serverCall.close(status.withDescription(ex.getMessage()), trailers);
    }
  }
}
