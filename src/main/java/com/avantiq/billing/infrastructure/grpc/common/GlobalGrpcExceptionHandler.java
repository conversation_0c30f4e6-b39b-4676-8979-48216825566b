package com.avantiq.billing.infrastructure.grpc.common;

import com.avantiq.billing.application.exception.InvalidOperationException;
import com.avantiq.billing.application.exception.ResourceAlreadyExistsException;
import com.avantiq.billing.application.exception.ResourceNotFoundException;
import com.avantiq.billing.domain.common.exception.DomainException;
import com.avantiq.billing.domain.common.exception.ValidationException;
import io.grpc.Metadata;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import lombok.extern.slf4j.Slf4j;
import net.devh.boot.grpc.server.advice.GrpcAdvice;
import net.devh.boot.grpc.server.advice.GrpcExceptionHandler;

/**
 * Global exception handler for gRPC services. Maps application and domain exceptions to appropriate
 * gRPC status codes.
 */
@GrpcAdvice
@Slf4j
public class GlobalGrpcExceptionHandler {

  private static final Metadata.Key<String> ERROR_CODE_KEY =
      Metadata.Key.of("error-code", Metadata.ASCII_STRING_MARSHALLER);

  private static final Metadata.Key<String> ERROR_DETAILS_KEY =
      Metadata.Key.of("error-details", Metadata.ASCII_STRING_MARSHALLER);

  /** Handle ResourceNotFoundException - maps to NOT_FOUND status. */
  @GrpcExceptionHandler(ResourceNotFoundException.class)
  public StatusRuntimeException handleResourceNotFoundException(ResourceNotFoundException e) {
    log.warn("Resource not found: {}", e.getMessage());

    Metadata metadata = new Metadata();
    metadata.put(ERROR_CODE_KEY, e.getErrorCode());
    metadata.put(ERROR_DETAILS_KEY, e.getMessage());

    return Status.NOT_FOUND.withDescription(e.getMessage()).asRuntimeException(metadata);
  }

  /** Handle ResourceAlreadyExistsException - maps to ALREADY_EXISTS status. */
  @GrpcExceptionHandler(ResourceAlreadyExistsException.class)
  public StatusRuntimeException handleResourceAlreadyExistsException(
      ResourceAlreadyExistsException e) {
    log.warn("Resource already exists: {}", e.getMessage());

    Metadata metadata = new Metadata();
    metadata.put(ERROR_CODE_KEY, e.getErrorCode());
    metadata.put(ERROR_DETAILS_KEY, e.getMessage());

    return Status.ALREADY_EXISTS.withDescription(e.getMessage()).asRuntimeException(metadata);
  }

  /** Handle InvalidOperationException - maps to FAILED_PRECONDITION status. */
  @GrpcExceptionHandler(InvalidOperationException.class)
  public StatusRuntimeException handleInvalidOperationException(InvalidOperationException e) {
    log.warn("Invalid operation: {}", e.getMessage());

    Metadata metadata = new Metadata();
    metadata.put(ERROR_CODE_KEY, e.getErrorCode());
    metadata.put(ERROR_DETAILS_KEY, e.getMessage());

    return Status.FAILED_PRECONDITION.withDescription(e.getMessage()).asRuntimeException(metadata);
  }

  /** Handle IllegalArgumentException - maps to INVALID_ARGUMENT status. */
  @GrpcExceptionHandler(IllegalArgumentException.class)
  public StatusRuntimeException handleIllegalArgumentException(IllegalArgumentException e) {
    log.warn("Invalid argument: {}", e.getMessage());

    Metadata metadata = new Metadata();
    metadata.put(ERROR_CODE_KEY, "INVALID_ARGUMENT");
    metadata.put(ERROR_DETAILS_KEY, e.getMessage());

    return Status.INVALID_ARGUMENT.withDescription(e.getMessage()).asRuntimeException(metadata);
  }

  /** Handle ValidationException from domain layer - maps to INVALID_ARGUMENT status. */
  @GrpcExceptionHandler(ValidationException.class)
  public StatusRuntimeException handleValidationException(ValidationException e) {
    log.warn("Validation error: {} - Field: {}", e.getMessage(), e.getFieldName());

    Metadata metadata = new Metadata();
    metadata.put(ERROR_CODE_KEY, e.getErrorCode().name());
    metadata.put(
        ERROR_DETAILS_KEY, String.format("Field '%s': %s", e.getFieldName(), e.getMessage()));

    return Status.INVALID_ARGUMENT.withDescription(e.getMessage()).asRuntimeException(metadata);
  }

  /** Handle generic DomainException - maps to FAILED_PRECONDITION status. */
  @GrpcExceptionHandler(DomainException.class)
  public StatusRuntimeException handleDomainException(DomainException e) {
    log.warn("Domain error: {}", e.getMessage());

    Metadata metadata = new Metadata();
    metadata.put(ERROR_CODE_KEY, e.getErrorCode().name());
    metadata.put(ERROR_DETAILS_KEY, e.getMessage());

    return Status.FAILED_PRECONDITION.withDescription(e.getMessage()).asRuntimeException(metadata);
  }

  /** Handle SecurityException - maps to PERMISSION_DENIED status. */
  @GrpcExceptionHandler(SecurityException.class)
  public StatusRuntimeException handleSecurityException(SecurityException e) {
    log.error("Security violation: {}", e.getMessage());

    Metadata metadata = new Metadata();
    metadata.put(ERROR_CODE_KEY, "PERMISSION_DENIED");
    metadata.put(ERROR_DETAILS_KEY, "Access denied");

    return Status.PERMISSION_DENIED.withDescription("Access denied").asRuntimeException(metadata);
  }

  /** Handle generic Exception - maps to INTERNAL status. */
  @GrpcExceptionHandler(Exception.class)
  public StatusRuntimeException handleGenericException(Exception e) {
    log.error("Unexpected error", e);

    Metadata metadata = new Metadata();
    metadata.put(ERROR_CODE_KEY, "INTERNAL_ERROR");
    metadata.put(ERROR_DETAILS_KEY, "An internal error occurred");

    return Status.INTERNAL
        .withDescription("An internal error occurred")
        .asRuntimeException(metadata);
  }
}
