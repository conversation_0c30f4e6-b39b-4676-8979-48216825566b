package com.avantiq.billing.infrastructure.grpc.mapper;

import com.avantiq.billing.domain.product.model.ProductFamily;
import com.avantiq.billing.product.grpc.CreateProductFamilyRequest;
import com.avantiq.billing.product.grpc.ListProductFamiliesResponse;
import com.avantiq.billing.product.grpc.ProductFamilyResponse;
import com.avantiq.billing.product.grpc.UpdateProductFamilyRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/** Mapper for converting between domain ProductFamily and gRPC ProductFamily messages. */
public class ProductFamilyMapper {

  private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ISO_DATE_TIME;

  private ProductFamilyMapper() {
    // Utility class, no instantiation
  }

  /**
   * Converts a domain ProductFamily to a gRPC ProductFamilyResponse.
   *
   * @param productFamily the domain ProductFamily
   * @return the gRPC ProductFamilyResponse
   */
  public static ProductFamilyResponse toProto(ProductFamily productFamily) {
    if (productFamily == null) {
      return ProductFamilyResponse.getDefaultInstance();
    }

    ProductFamilyResponse.Builder builder = ProductFamilyResponse.newBuilder();

    if (productFamily.getId() != null) {
      builder.setId(productFamily.getId().toString());
    }

    if (productFamily.getName() != null) {
      builder.setName(productFamily.getName());
    }

    if (productFamily.getDescription() != null) {
      builder.setDescription(productFamily.getDescription());
    }

    if (productFamily.getCreatedAt() != null) {
      builder.setCreatedAt(productFamily.getCreatedAt().format(DATE_TIME_FORMATTER));
    }

    if (productFamily.getUpdatedAt() != null) {
      builder.setUpdatedAt(productFamily.getUpdatedAt().format(DATE_TIME_FORMATTER));
    }

    return builder.build();
  }

  /**
   * Converts a gRPC CreateProductFamilyRequest to a domain ProductFamily.
   *
   * @param request the gRPC CreateProductFamilyRequest
   * @return the domain ProductFamily
   */
  public static ProductFamily toDomain(CreateProductFamilyRequest request) {
    if (request == null) {
      return null;
    }

    LocalDateTime now = LocalDateTime.now();

    return new ProductFamily(
        null, // ID will be set by the service
        request.getName(),
        request.getDescription(),
        now,
        now);
  }

  /**
   * Converts a gRPC UpdateProductFamilyRequest to a domain ProductFamily.
   *
   * @param request the gRPC UpdateProductFamilyRequest
   * @return the domain ProductFamily
   */
  public static ProductFamily toDomain(UpdateProductFamilyRequest request) {
    if (request == null || request.getId().isEmpty()) {
      return null;
    }

    UUID id = UUID.fromString(request.getId());
    LocalDateTime now = LocalDateTime.now();

    return new ProductFamily(
        id,
        request.getName(),
        request.getDescription(),
        null, // Keep createdAt as is
        now);
  }

  /**
   * Converts a list of domain ProductFamily to a gRPC ListProductFamiliesResponse.
   *
   * @param productFamilies the list of domain ProductFamily
   * @return the gRPC ListProductFamiliesResponse
   */
  public static ListProductFamiliesResponse toGrpcListResponse(
      List<ProductFamily> productFamilies) {
    List<ProductFamilyResponse> responses =
        productFamilies.stream().map(ProductFamilyMapper::toProto).collect(Collectors.toList());

    return ListProductFamiliesResponse.newBuilder().addAllProductFamilies(responses).build();
  }
}
