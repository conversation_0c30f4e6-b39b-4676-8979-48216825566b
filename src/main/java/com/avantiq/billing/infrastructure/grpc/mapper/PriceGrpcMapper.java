package com.avantiq.billing.infrastructure.grpc.mapper;

import com.avantiq.billing.domain.product.model.Price;
import com.avantiq.billing.domain.product.model.Price.BillingFrequency;
import com.avantiq.billing.domain.product.model.Price.BillingStrategy;
import com.avantiq.billing.domain.product.model.Price.PriceStrategy;
import com.avantiq.billing.domain.product.model.Price.PriceType;
import com.avantiq.billing.domain.product.model.Price.ProrationPolicy;
import com.avantiq.billing.domain.product.valueobject.Pricing;
import com.avantiq.billing.infrastructure.persistence.product.serializer.PricingSerializer;
import com.avantiq.billing.product.grpc.CreatePriceRequest;
import com.avantiq.billing.product.grpc.ListPricesResponse;
import com.avantiq.billing.product.grpc.PriceResponse;
import com.avantiq.billing.product.grpc.UpdatePriceRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/** Mapper for converting between domain Price and gRPC Price messages. */
@Component
public class PriceGrpcMapper {

  private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ISO_DATE_TIME;

  private final PricingSerializer pricingSerializer;

  @Autowired
  public PriceGrpcMapper(PricingSerializer pricingSerializer) {
    this.pricingSerializer = pricingSerializer;
  }

  /**
   * Converts a domain Price to a gRPC PriceResponse.
   *
   * @param price the domain Price
   * @return the gRPC PriceResponse
   */
  public PriceResponse toProto(Price price) {
    if (price == null) {
      return PriceResponse.getDefaultInstance();
    }

    PriceResponse.Builder builder = PriceResponse.newBuilder();

    if (price.getId() != null) {
      builder.setId(price.getId().toString());
    }

    if (price.getProductId() != null) {
      builder.setProductId(price.getProductId().toString());
    }

    if (price.getPriceBookId() != null) {
      builder.setPriceBookId(price.getPriceBookId().toString());
    }

    if (price.getBillingFrequency() != null) {
      builder.setBillingFrequency(price.getBillingFrequency().name());
    }

    if (price.getCurrency() != null) {
      builder.setCurrency(price.getCurrency());
    }

    if (price.getUnitOfMeasure() != null) {
      builder.setUnitOfMeasure(price.getUnitOfMeasure());
    }

    if (price.getPriceType() != null) {
      builder.setPriceType(
          com.avantiq.billing.product.grpc.PriceType.valueOf(price.getPriceType().name()));
    }

    if (price.getPriceStrategy() != null) {
      builder.setPriceStrategy(
          com.avantiq.billing.product.grpc.PriceStrategy.valueOf(price.getPriceStrategy().name()));
    }

    if (price.getBillingStrategy() != null) {
      builder.setBillingStrategy(price.getBillingStrategy().name());
    }

    if (price.getPricing() != null) {
      builder.setPricing(pricingSerializer.serialize(price.getPricing()));
    }

    if (price.getProrationPolicy() != null) {
      builder.setProrationPolicy(price.getProrationPolicy().name());
    }

    builder.setIsDefault(price.isDefault());
    builder.setIsGrandfathered(price.isGrandfathered());
    builder.setVersion(price.getVersion());

    if (price.getCreatedAt() != null) {
      builder.setCreatedAt(price.getCreatedAt().format(DATE_TIME_FORMATTER));
    }

    if (price.getUpdatedAt() != null) {
      builder.setUpdatedAt(price.getUpdatedAt().format(DATE_TIME_FORMATTER));
    }

    return builder.build();
  }

  /**
   * Converts a gRPC CreatePriceRequest to a domain Price.
   *
   * @param request the gRPC CreatePriceRequest
   * @return the domain Price
   */
  public Price toDomain(CreatePriceRequest request) {
    if (request == null) {
      return null;
    }

    LocalDateTime now = LocalDateTime.now();

    // Deserialize pricing if provided
    Pricing pricing = null;
    if (request.getPricing() != null && !request.getPricing().isEmpty()) {
      pricing =
          pricingSerializer.deserialize(
              request.getPricing(), PriceStrategy.valueOf(request.getPriceStrategy().name()));
    }

    return new Price(
        null, // ID will be set by the service
        UUID.fromString(request.getProductId()),
        UUID.fromString(request.getPriceBookId()),
        BillingFrequency.valueOf(request.getBillingFrequency()),
        request.getCurrency(),
        request.getUnitOfMeasure(),
        PriceType.valueOf(request.getPriceType().name()),
        PriceStrategy.valueOf(request.getPriceStrategy().name()),
        BillingStrategy.valueOf(request.getBillingStrategy()),
        pricing,
        ProrationPolicy.valueOf(request.getProrationPolicy()),
        request.getIsDefault(),
        request.getIsGrandfathered(),
        1L, // Initial version
        now,
        now);
  }

  /**
   * Converts a gRPC UpdatePriceRequest to a domain Price.
   *
   * @param request the gRPC UpdatePriceRequest
   * @param currentVersion the current version of the price
   * @return the domain Price
   */
  public Price toDomain(UpdatePriceRequest request, long currentVersion) {
    if (request == null || request.getId().isEmpty()) {
      return null;
    }

    UUID id = UUID.fromString(request.getId());
    LocalDateTime now = LocalDateTime.now();

    // Deserialize pricing if provided
    Pricing pricing = null;
    if (request.getPricing() != null && !request.getPricing().isEmpty()) {
      pricing =
          pricingSerializer.deserialize(
              request.getPricing(), PriceStrategy.valueOf(request.getPriceStrategy().name()));
    }

    return new Price(
        id,
        UUID.fromString(request.getProductId()),
        UUID.fromString(request.getPriceBookId()),
        BillingFrequency.valueOf(request.getBillingFrequency()),
        request.getCurrency(),
        request.getUnitOfMeasure(),
        PriceType.valueOf(request.getPriceType().name()),
        PriceStrategy.valueOf(request.getPriceStrategy().name()),
        BillingStrategy.valueOf(request.getBillingStrategy()),
        pricing,
        ProrationPolicy.valueOf(request.getProrationPolicy()),
        request.getIsDefault(),
        request.getIsGrandfathered(),
        currentVersion, // Keep the current version, will be incremented by service
        null, // Keep createdAt as is
        now);
  }

  /**
   * Converts a list of domain Price to a gRPC ListPricesResponse.
   *
   * @param prices the list of domain Price
   * @return the gRPC ListPricesResponse
   */
  public ListPricesResponse toGrpcListResponse(List<Price> prices) {
    List<PriceResponse> responses = prices.stream().map(this::toProto).collect(Collectors.toList());

    return ListPricesResponse.newBuilder().addAllPrices(responses).build();
  }
}
