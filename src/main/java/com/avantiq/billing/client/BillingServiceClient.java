package com.avantiq.billing.client;

import com.avantiq.billing.customer.grpc.CreateCustomerRequest;
import com.avantiq.billing.customer.grpc.CustomerResponse;
import com.avantiq.billing.customer.grpc.CustomerServiceGrpc;
import com.avantiq.billing.customer.grpc.GetCustomerRequest;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import io.grpc.Metadata;
import io.grpc.StatusRuntimeException;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;

/** Example client for Avantiq Billing gRPC services that demonstrates proper error handling. */
public class BillingServiceClient {
  private static final Logger logger = Logger.getLogger(BillingServiceClient.class.getName());

  private static final Metadata.Key<String> ERROR_CODE_KEY =
      Metadata.Key.of("error-code", Metadata.ASCII_STRING_MARSHALLER);

  private static final Metadata.Key<String> ERROR_TYPE_KEY =
      Metadata.Key.of("error-type", Metadata.ASCII_STRING_MARSHALLER);

  private final ManagedChannel channel;
  private final CustomerServiceGrpc.CustomerServiceBlockingStub customerStub;

  /** Construct client connecting to Billing server at {@code host:port}. */
  public BillingServiceClient(String host, int port) {
    this.channel =
        ManagedChannelBuilder.forAddress(host, port)
            .usePlaintext() // For demonstration only, use TLS in production
            .build();
    this.customerStub = CustomerServiceGrpc.newBlockingStub(channel);
  }

  public void shutdown() throws InterruptedException {
    channel.shutdown().awaitTermination(5, TimeUnit.SECONDS);
  }

  /** Example of calling getCustomer with error handling. */
  public void getCustomer(String customerId) {
    logger.info("Getting customer " + customerId);
    GetCustomerRequest request = GetCustomerRequest.newBuilder().setId(customerId).build();

    try {
      CustomerResponse response = customerStub.getCustomer(request);
      logger.info(
          "Customer found: "
              + response.getCustomer().getFirstName()
              + " "
              + response.getCustomer().getLastName());
    } catch (StatusRuntimeException e) {
      // Extract metadata and handle error
      handleError(e, "getCustomer");
    }
  }

  /** Example of calling createCustomer with error handling. */
  public void createCustomer(String firstName, String lastName, String email) {
    logger.info("Creating customer: " + firstName + " " + lastName + " (" + email + ")");
    CreateCustomerRequest request =
        CreateCustomerRequest.newBuilder()
            .setFirstName(firstName)
            .setLastName(lastName)
            .setEmail(email)
            .build();

    try {
      CustomerResponse response = customerStub.createCustomer(request);
      logger.info("Customer created with ID: " + response.getCustomer().getId());
    } catch (StatusRuntimeException e) {
      // Extract metadata and handle error
      handleError(e, "createCustomer");
    }
  }

  /** Handles gRPC errors with consistent pattern and extracts metadata. */
  private void handleError(StatusRuntimeException e, String operation) {
    Metadata trailers = e.getTrailers();
    String errorCode = trailers != null ? trailers.get(ERROR_CODE_KEY) : "unknown";
    String errorType = trailers != null ? trailers.get(ERROR_TYPE_KEY) : "unknown";

    logger.log(
        Level.SEVERE,
        operation
            + " failed with status "
            + e.getStatus().getCode()
            + ", error type: "
            + errorType
            + ", error code: "
            + errorCode
            + ", message: "
            + e.getStatus().getDescription());

    // Handle specific error types
    switch (e.getStatus().getCode()) {
      case NOT_FOUND:
        // Handle resource not found
        logger.info("Resource not found: " + e.getStatus().getDescription());
        break;

      case ALREADY_EXISTS:
        // Handle duplicate resource
        logger.info("Resource already exists: " + e.getStatus().getDescription());
        break;

      case INVALID_ARGUMENT:
        // Handle validation errors
        logger.info("Invalid data: " + e.getStatus().getDescription());
        break;

      case FAILED_PRECONDITION:
        // Handle business rule violations
        logger.info("Business rule violation: " + e.getStatus().getDescription());
        break;

      default:
        // Handle unexpected errors
        logger.severe("Unexpected error: " + e.getStatus().getDescription());
        break;
    }
  }

  /** Example usage of the client with error handling. */
  public static void main(String[] args) throws Exception {
    BillingServiceClient client = new BillingServiceClient("localhost", 9090);
    try {
      // Example 1: Try to get non-existent customer (will cause NOT_FOUND error)
      client.getCustomer("00000000-0000-0000-0000-000000000000");

      // Example 2: Try to create customer with missing required fields (will cause INVALID_ARGUMENT
      // error)
      client.createCustomer("", "", "");

      // Example 3: Try to create duplicate customer (will cause ALREADY_EXISTS error if email
      // exists)
      client.createCustomer("John", "Doe", "<EMAIL>");
      client.createCustomer("John", "Doe", "<EMAIL>");

    } finally {
      client.shutdown();
    }
  }
}
