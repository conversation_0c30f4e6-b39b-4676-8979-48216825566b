package com.avantiq.billing.domain.common.exception;

/**
 * Base exception for all domain-related exceptions in the billing system. This exception and its
 * subclasses should be used to represent business rule violations and domain logic errors.
 */
public abstract class DomainException extends RuntimeException {

  private final ErrorCode errorCode;

  public DomainException(String message, ErrorCode errorCode) {
    super(message);
    this.errorCode = errorCode;
  }

  public DomainException(String message, Throwable cause, ErrorCode errorCode) {
    super(message, cause);
    this.errorCode = errorCode;
  }

  public ErrorCode getErrorCode() {
    return errorCode;
  }
}
