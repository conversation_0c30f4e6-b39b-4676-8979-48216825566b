package com.avantiq.billing.domain.common.exception;

/**
 * Exception thrown when a business rule is violated. This exception is used for domain-specific
 * business logic violations.
 */
public class BusinessRuleViolationException extends DomainException {

  private final String ruleName;

  public BusinessRuleViolationException(String message, String ruleName) {
    super(message, ErrorCode.OPERATION_NOT_ALLOWED);
    this.ruleName = ruleName;
  }

  public BusinessRuleViolationException(String message, String ruleName, Throwable cause) {
    super(message, cause, ErrorCode.OPERATION_NOT_ALLOWED);
    this.ruleName = ruleName;
  }

  public String getRuleName() {
    return ruleName;
  }

  @Override
  public String getMessage() {
    if (ruleName != null) {
      return String.format("Business rule '%s' violated: %s", ruleName, super.getMessage());
    }
    return super.getMessage();
  }
}
