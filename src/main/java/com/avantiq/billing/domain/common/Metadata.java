package com.avantiq.billing.domain.common;

import java.util.UUID;

/** Represents metadata for various entities like Product, Bundle, and Price. */
public class Metadata {

  private UUID id;
  private UUID entityId; // Foreign key to associated entity (e.g., Product, Bundle, Price)
  private String key; // Name/key of the metadata field
  private String value; // Value of the metadata field

  public Metadata(UUID id, UUID entityId, String key, String value) {
    this.id = id;
    this.entityId = entityId;
    this.key = key;
    this.value = value;
  }

  public UUID getId() {
    return id;
  }

  public void setId(UUID id) {
    this.id = id;
  }

  public UUID getEntityId() {
    return entityId;
  }

  public void setEntityId(UUID entityId) {
    this.entityId = entityId;
  }

  public String getKey() {
    return key;
  }

  public void setKey(String key) {
    this.key = key;
  }

  public String getValue() {
    return value;
  }

  public void setValue(String value) {
    this.value = value;
  }
}
