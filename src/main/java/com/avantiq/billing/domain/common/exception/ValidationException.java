package com.avantiq.billing.domain.common.exception;

/**
 * Exception thrown when domain model validation fails. This exception provides specific information
 * about validation failures.
 */
public class ValidationException extends DomainException {

  private final String fieldName;
  private final Object rejectedValue;

  public ValidationException(String message, String fieldName, Object rejectedValue) {
    super(message, ErrorCode.VALIDATION_ERROR);
    this.fieldName = fieldName;
    this.rejectedValue = rejectedValue;
  }

  public ValidationException(
      String message, String fieldName, Object rejectedValue, Throwable cause) {
    super(message, cause, ErrorCode.VALIDATION_ERROR);
    this.fieldName = fieldName;
    this.rejectedValue = rejectedValue;
  }

  public String getFieldName() {
    return fieldName;
  }

  public Object getRejectedValue() {
    return rejectedValue;
  }

  @Override
  public String getMessage() {
    if (fieldName != null) {
      return String.format(
          "Validation failed for field '%s' with value '%s': %s",
          fieldName, rejectedValue, super.getMessage());
    }
    return super.getMessage();
  }
}
