package com.avantiq.billing.domain.common.exception;

/**
 * Standardized error codes for domain exceptions. These error codes can be used for client-side
 * error handling and internationalization.
 */
public enum ErrorCode {
  // General errors
  VALIDATION_ERROR(1000),
  NOT_FOUND(1001),
  ALREADY_EXISTS(1002),
  OPERATION_NOT_ALLOWED(1003),
  REPOSITORY_ERROR(1004),

  // Customer-specific errors
  CUSTOMER_NOT_FOUND(2000),
  CUSTOMER_EMAIL_ALREADY_EXISTS(2001),
  INVALID_CUSTOMER_DATA(2002),
  CUSTOMER_RELATIONSHIP_VIOLATION(2003),

  // Product-specific errors
  PRODUCT_NOT_FOUND(3000),
  PRODUCT_SKU_ALREADY_EXISTS(3001),
  INVALID_PRODUCT_DATA(3002),

  // Payment-specific errors
  PAYMENT_METHOD_INVALID(4000),
  PAYMENT_PROCESSING_ERROR(4001),
  PAYMENT_METHOD_NOT_FOUND(4002);

  private final int code;

  ErrorCode(int code) {
    this.code = code;
  }

  public int getCode() {
    return code;
  }
}
