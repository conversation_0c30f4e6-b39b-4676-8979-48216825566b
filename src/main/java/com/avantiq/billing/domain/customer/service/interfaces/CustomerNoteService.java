package com.avantiq.billing.domain.customer.service.interfaces;

import com.avantiq.billing.domain.customer.model.CustomerNote;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface CustomerNoteService {
  CustomerNote createNote(CustomerNote note);

  CustomerNote updateNote(CustomerNote note);

  boolean deleteNote(UUID noteId);

  Optional<CustomerNote> getNoteById(UUID noteId);

  List<CustomerNote> getNotesByCustomerId(UUID customerId);
}
