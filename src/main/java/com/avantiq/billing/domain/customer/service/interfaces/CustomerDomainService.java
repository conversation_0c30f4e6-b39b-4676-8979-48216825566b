package com.avantiq.billing.domain.customer.service.interfaces;

import com.avantiq.billing.domain.customer.model.Address;
import com.avantiq.billing.domain.customer.model.Customer;
import java.util.UUID;

/**
 * Domain service for customer-related operations. This contains domain logic that doesn't naturally
 * fit within a single entity.
 */
public interface CustomerDomainService {
  /**
   * Validate a customer.
   *
   * @param customer the customer to validate
   * @return true if the customer is valid, false otherwise
   */
  boolean validateCustomer(Customer customer);

  /**
   * Add an address to a customer.
   *
   * @param customer the customer
   * @param address the address to add
   * @return the updated customer
   */
  Customer addAddress(Customer customer, Address address);

  /**
   * Remove an address from a customer.
   *
   * @param customer the customer
   * @param addressId the ID of the address to remove
   * @return the updated customer
   */
  Customer removeAddress(Customer customer, UUID addressId);

  /**
   * Change a customer's segment.
   *
   * @param customer the customer
   * @param segmentId the new segment ID
   * @return the updated customer
   */
  Customer changeSegment(Customer customer, UUID segmentId);

  /**
   * Activate a customer.
   *
   * @param customer the customer to activate
   * @return the activated customer
   */
  Customer activateCustomer(Customer customer);

  /**
   * Deactivate a customer.
   *
   * @param customer the customer to deactivate
   * @return the deactivated customer
   */
  Customer deactivateCustomer(Customer customer);
}
