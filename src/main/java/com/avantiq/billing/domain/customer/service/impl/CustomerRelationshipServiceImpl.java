package com.avantiq.billing.domain.customer.service.impl;

import com.avantiq.billing.domain.customer.exception.CustomerRelationshipViolationException;
import com.avantiq.billing.domain.customer.model.CustomerRelationship;
import com.avantiq.billing.domain.customer.repository.CustomerRelationshipRepository;
import com.avantiq.billing.domain.customer.service.interfaces.CustomerRelationshipService;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

@Slf4j
public class CustomerRelationshipServiceImpl implements CustomerRelationshipService {

  private final CustomerRelationshipRepository customerRelationshipRepository;

  public CustomerRelationshipServiceImpl(
      CustomerRelationshipRepository customerRelationshipRepository) {
    this.customerRelationshipRepository = customerRelationshipRepository;
  }

  @Override
  public void linkRelationship(
      UUID parentId, UUID childId, String relationshipType, Long tenantId) {
    log.info(
        "Creating customer relationship: parent={}, child={}, type={}, tenant={}",
        parentId,
        childId,
        relationshipType,
        tenantId);

    validateRelationshipData(parentId, childId, relationshipType, tenantId);

    CustomerRelationship relationship =
        CustomerRelationship.builder()
            .id(UUID.randomUUID())
            .parentCustomerId(parentId)
            .childCustomerId(childId)
            .type(mapRelationshipType(relationshipType))
            .level(1) // Default level
            .tenantId(tenantId)
            .segmentId(UUID.randomUUID()) // TODO: Get actual segment ID
            .createdAt(LocalDateTime.now())
            .createdBy("system") // TODO: Get from security context
            .build();

    CustomerRelationship savedRelationship = customerRelationshipRepository.save(relationship);
    log.info("Successfully created customer relationship with ID: {}", savedRelationship.getId());
  }

  private void validateRelationshipData(
      UUID parentId, UUID childId, String relationshipType, Long tenantId) {
    if (parentId == null) {
      throw new CustomerRelationshipViolationException("Parent customer ID cannot be null");
    }

    if (childId == null) {
      throw new CustomerRelationshipViolationException("Child customer ID cannot be null");
    }

    if (parentId.equals(childId)) {
      throw new CustomerRelationshipViolationException(
          "Parent and child customer IDs cannot be the same");
    }

    if (!StringUtils.hasText(relationshipType)) {
      throw new CustomerRelationshipViolationException("Relationship type cannot be null or empty");
    }

    if (tenantId == null) {
      throw new CustomerRelationshipViolationException("Tenant ID cannot be null");
    }
  }

  private CustomerRelationship.RelationshipType mapRelationshipType(String relationshipType) {
    try {
      return CustomerRelationship.RelationshipType.valueOf(relationshipType.toUpperCase());
    } catch (IllegalArgumentException e) {
      throw new CustomerRelationshipViolationException(
          "Invalid relationship type: "
              + relationshipType
              + ". Valid types are: PAYING, REPORTING");
    }
  }
}
