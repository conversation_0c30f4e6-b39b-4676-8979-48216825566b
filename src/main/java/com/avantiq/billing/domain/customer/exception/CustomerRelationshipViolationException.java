package com.avantiq.billing.domain.customer.exception;

import com.avantiq.billing.domain.common.exception.DomainException;
import com.avantiq.billing.domain.common.exception.ErrorCode;

/** Exception thrown when an operation would violate customer relationship rules. */
public class CustomerRelationshipViolationException extends DomainException {

  public CustomerRelationshipViolationException(String message) {
    super(message, ErrorCode.CUSTOMER_RELATIONSHIP_VIOLATION);
  }

  public CustomerRelationshipViolationException(String message, Throwable cause) {
    super(message, cause, ErrorCode.CUSTOMER_RELATIONSHIP_VIOLATION);
  }
}
