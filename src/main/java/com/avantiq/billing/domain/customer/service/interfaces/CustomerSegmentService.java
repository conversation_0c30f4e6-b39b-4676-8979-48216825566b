package com.avantiq.billing.domain.customer.service.interfaces;

import com.avantiq.billing.domain.customer.model.CustomerSegment;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface CustomerSegmentService {
  List<CustomerSegment> findAll();

  Page<CustomerSegment> findAll(Pageable pageable);

  Optional<CustomerSegment> findById(UUID id);

  List<CustomerSegment> listSegments();

  CustomerSegment save(CustomerSegment segment);
}
