package com.avantiq.billing.domain.customer.model;

import com.avantiq.billing.domain.customer.validator.AddressValidator;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Address domain entity representing a physical address associated with a customer. This is a pure
 * domain entity without any infrastructure concerns.
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class Address {
  private UUID id;
  private String street;
  private String city;
  private String state;
  private String postalCode; // Consistent naming with JPA entity and mapper
  private String country;
  private AddressType addressType;
  private Long tenantId;

  public enum AddressType {
    BILLING,
    SHIPPING
  }

  // Domain methods
  public boolean isBillingAddress() {
    return AddressType.BILLING.equals(this.addressType);
  }

  public boolean isShippingAddress() {
    return AddressType.SHIPPING.equals(this.addressType);
  }

  /**
   * Validates the address according to business rules.
   *
   * @return true if valid, false otherwise
   * @throws IllegalArgumentException if validation fails with specific reason
   */
  public boolean validate() {
    if (street == null || street.trim().isEmpty()) {
      throw new IllegalArgumentException("Street is required");
    }
    if (city == null || city.trim().isEmpty()) {
      throw new IllegalArgumentException("City is required");
    }
    if (country == null || country.trim().isEmpty()) {
      throw new IllegalArgumentException("Country is required");
    }
    if (postalCode == null || postalCode.trim().isEmpty()) {
      throw new IllegalArgumentException("Postal code is required");
    }
    if (addressType == null) {
      throw new IllegalArgumentException("Address type is required");
    }

    // Use the specialized validator for country-specific validation
    if (!AddressValidator.isValidPostalCode(postalCode, country)) {
      throw new IllegalArgumentException("Invalid postal code format for country: " + country);
    }

    return true;
  }
}
