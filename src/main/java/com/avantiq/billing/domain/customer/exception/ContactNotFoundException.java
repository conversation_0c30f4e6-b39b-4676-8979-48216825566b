package com.avantiq.billing.domain.customer.exception;

import com.avantiq.billing.domain.common.exception.DomainException;
import com.avantiq.billing.domain.common.exception.ErrorCode;

/** Exception thrown when a contact with the requested identifier cannot be found. */
public class ContactNotFoundException extends DomainException {

  public ContactNotFoundException(String id) {
    super("Contact not found with ID: " + id, ErrorCode.CUSTOMER_NOT_FOUND);
  }

  public ContactNotFoundException(String message, Throwable cause) {
    super(message, cause, ErrorCode.CUSTOMER_NOT_FOUND);
  }
}
