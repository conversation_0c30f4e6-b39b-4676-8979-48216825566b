package com.avantiq.billing.domain.customer.exception;

import com.avantiq.billing.domain.common.exception.DomainException;
import com.avantiq.billing.domain.common.exception.ErrorCode;

/** Exception thrown when attempting to create a customer with an email that already exists. */
public class CustomerEmailAlreadyExistsException extends DomainException {

  private final String email;

  public CustomerEmailAlreadyExistsException(String email) {
    super("Customer with email already exists: " + email, ErrorCode.CUSTOMER_EMAIL_ALREADY_EXISTS);
    this.email = email;
  }

  public String getEmail() {
    return email;
  }
}
