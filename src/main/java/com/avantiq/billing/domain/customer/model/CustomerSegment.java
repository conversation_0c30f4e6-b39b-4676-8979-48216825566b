package com.avantiq.billing.domain.customer.model;

import java.util.UUID;
import lombok.Getter;
import lombok.Setter;

/**
 * CustomerSegment domain entity representing a segment or category that customers can belong to.
 * This is a pure domain entity without any infrastructure concerns.
 */
@Setter
@Getter
public class CustomerSegment {
  private UUID id;
  private String name;
  private String description;
  private Long tenantId;

  // Constructors
  public CustomerSegment() {}

  public CustomerSegment(UUID id, String name, String description) {
    this.id = id;
    this.name = name;
    this.description = description;
  }

  public CustomerSegment(String id, String name, String description) {
    this.id = UUID.fromString(id);
    this.name = name;
    this.description = description;
  }

  // Domain methods
  public boolean hasDescription() {
    return description != null && !description.trim().isEmpty();
  }
}
