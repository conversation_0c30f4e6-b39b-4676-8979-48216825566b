package com.avantiq.billing.domain.customer.service.impl;

import com.avantiq.billing.domain.customer.model.Address;
import com.avantiq.billing.domain.customer.model.Customer;
import com.avantiq.billing.domain.customer.service.interfaces.CustomerDomainService;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * Implementation of the CustomerDomainService interface. This contains domain logic that doesn't
 * naturally fit within a single entity.
 */
public class CustomerDomainServiceImpl implements CustomerDomainService {
  private static final Pattern EMAIL_PATTERN = Pattern.compile("^[A-Za-z0-9+_.-]+@(.+)$");

  @Override
  public boolean validateCustomer(Customer customer) {
    if (customer == null) {
      return false;
    }

    // Check required fields
    if (isEmpty(customer.getFirstName())
        || isEmpty(customer.getLastName())
        || isEmpty(customer.getEmail())
        || isEmpty(customer.getCountry())) {
      return false;
    }

    // Validate email format
    if (!EMAIL_PATTERN.matcher(customer.getEmail()).matches()) {
      return false;
    }

    // Validate status
    return customer.getStatus() != null;
  }

  @Override
  public Customer addAddress(Customer customer, Address address) {
    if (customer == null || address == null) {
      throw new IllegalArgumentException("Customer and address must not be null");
    }

    customer.addAddress(address);
    return customer;
  }

  @Override
  public Customer removeAddress(Customer customer, UUID addressId) {
    if (customer == null || addressId == null) {
      throw new IllegalArgumentException("Customer and addressId must not be null");
    }

    customer.getAddresses().removeIf(address -> addressId.equals(address.getId()));
    return customer;
  }

  @Override
  public Customer changeSegment(Customer customer, UUID segmentId) {
    if (customer == null || segmentId == null) {
      throw new IllegalArgumentException("Customer and segmentId must not be null");
    }

    customer.setSegmentId(segmentId);
    customer.setUpdatedAt(java.time.LocalDateTime.now());
    return customer;
  }

  @Override
  public Customer activateCustomer(Customer customer) {
    if (customer == null) {
      throw new IllegalArgumentException("Customer must not be null");
    }

    customer.activate();
    return customer;
  }

  @Override
  public Customer deactivateCustomer(Customer customer) {
    if (customer == null) {
      throw new IllegalArgumentException("Customer must not be null");
    }

    customer.deactivate();
    return customer;
  }

  private boolean isEmpty(String str) {
    return str == null || str.trim().isEmpty();
  }
}
