package com.avantiq.billing.domain.customer.model;

import java.util.UUID;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CustomerNote {
  private UUID id;
  private String note;
  private Long tenantId;

  public CustomerNote() {}

  public CustomerNote(UUID id, String note) {
    this.id = id;
    this.note = note;
  }

  public Long getTenantId() {
    return tenantId;
  }

  public void setTenantId(Long tenantId) {
    this.tenantId = tenantId;
  }
}
