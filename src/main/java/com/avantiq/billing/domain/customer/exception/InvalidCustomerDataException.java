package com.avantiq.billing.domain.customer.exception;

import com.avantiq.billing.domain.common.exception.DomainException;
import com.avantiq.billing.domain.common.exception.ErrorCode;

/** Exception thrown when an operation fails due to validation errors in customer data. */
public class InvalidCustomerDataException extends DomainException {

  public InvalidCustomerDataException(String message) {
    super(message, ErrorCode.INVALID_CUSTOMER_DATA);
  }

  public InvalidCustomerDataException(String message, Throwable cause) {
    super(message, cause, ErrorCode.INVALID_CUSTOMER_DATA);
  }
}
