package com.avantiq.billing.domain.customer.repository;

import com.avantiq.billing.domain.customer.model.CustomerNote;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface CustomerNoteRepository {
  List<CustomerNote> findByCustomerId(UUID customerId);

  CustomerNote save(CustomerNote note);

  void deleteById(UUID noteId);

  Optional<CustomerNote> findById(UUID noteId);
}
