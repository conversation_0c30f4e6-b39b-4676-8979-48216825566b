package com.avantiq.billing.domain.customer.service.impl;

import com.avantiq.billing.domain.customer.model.Contact;
import com.avantiq.billing.domain.customer.repository.ContactRepository;
import com.avantiq.billing.domain.customer.service.interfaces.ContactService;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public class ContactServiceImpl implements ContactService {

  private final ContactRepository contactRepository;

  public ContactServiceImpl(ContactRepository contactRepository) {
    this.contactRepository = contactRepository;
  }

  @Override
  public void addContact(
      UUID customerId,
      String firstName,
      String lastName,
      String email,
      String contactType,
      boolean isDefault) {
    Contact contact = new Contact(customerId, firstName, lastName, email, contactType, isDefault);
    contactRepository.save(contact);
  }

  @Override
  public List<Contact> listContacts(UUID customerId) {
    return findByCustomerId(customerId);
  }

  @Override
  public void updateContact(
      UUID contactId,
      String firstName,
      String lastName,
      String email,
      String contactType,
      boolean isDefault) {
    Optional<Contact> existingContact = findById(contactId);
    if (existingContact.isPresent()) {
      Contact contact = existingContact.get();
      contact.updateDetails(firstName, lastName, email, contactType, isDefault);
      contactRepository.save(contact);
    }
  }

  @Override
  public List<Contact> findByCustomerId(UUID customerId) {
    return contactRepository.findByCustomerId(customerId);
  }

  @Override
  public Optional<Contact> findById(UUID id) {
    return contactRepository.findById(id);
  }

  @Override
  public Contact save(Contact contact) {
    return contactRepository.save(contact);
  }
}
