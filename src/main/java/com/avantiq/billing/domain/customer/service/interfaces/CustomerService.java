package com.avantiq.billing.domain.customer.service.interfaces;

import com.avantiq.billing.domain.customer.model.Customer;
import java.util.Optional;
import java.util.UUID;

public interface CustomerService {
  Optional<Customer> findById(UUID id);

  Optional<Customer> findById(UUID id, boolean deep);

  Optional<Customer> findByEmail(String email);

  Optional<Customer> findByEmail(String email, boolean deep);

  Customer save(Customer customer);

  boolean existsByEmail(String email);
}
