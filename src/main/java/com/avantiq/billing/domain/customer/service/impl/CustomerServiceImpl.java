package com.avantiq.billing.domain.customer.service.impl;

import com.avantiq.billing.domain.customer.model.Customer;
import com.avantiq.billing.domain.customer.repository.CustomerRepository;
import com.avantiq.billing.domain.customer.service.interfaces.CustomerService;
import java.util.Optional;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CustomerServiceImpl implements CustomerService {
  private final CustomerRepository customerRepository;

  public CustomerServiceImpl(CustomerRepository customerRepository) {
    this.customerRepository = customerRepository;
  }

  @Override
  public Optional<Customer> findById(UUID id) {
    try {
      return customerRepository.findById(id);
    } catch (Exception e) {
      log.error("Error in findById", e);
      return Optional.empty();
    }
  }

  @Override
  public Optional<Customer> findById(UUID id, boolean deep) {
    try {
      if (deep) {
        // You would need to add a similar fetch join query for findById in the repository
        // For now, fetch by email with addresses as an example
        // return customerRepository.findByIdWithAddresses(id);
        // Fallback: fetch by id, then initialize addresses
        Optional<Customer> opt = customerRepository.findById(id);
        opt.ifPresent(c -> org.hibernate.Hibernate.initialize(c.getAddresses()));
        return opt;
      } else {
        return customerRepository.findById(id);
      }
    } catch (Exception e) {
      log.error("Error in findById", e);
      return Optional.empty();
    }
  }

  @Override
  public Optional<Customer> findByEmail(String email) {
    try {
      return customerRepository.findByEmail(email);
    } catch (Exception e) {
      log.error("Error in findByEmail", e);
      return Optional.empty();
    }
  }

  @Override
  public Optional<Customer> findByEmail(String email, boolean deep) {
    try {
      if (deep) {
        return customerRepository.findByEmailWithAddresses(email);
      } else {
        return customerRepository.findByEmail(email);
      }
    } catch (Exception e) {
      log.error("Error in findByEmail", e);
      return Optional.empty();
    }
  }

  @Override
  public Customer save(Customer customer) {
    try {
      return customerRepository.save(customer);
    } catch (Exception e) {
      log.error("Error in save", e);
      throw new RuntimeException("Failed to save customer: " + e.getMessage(), e);
    }
  }

  @Override
  public boolean existsByEmail(String email) {
    try {
      return customerRepository.findByEmail(email).isPresent();
    } catch (Exception e) {
      log.error("Error in existsByEmail", e);
      return false;
    }
  }
}
