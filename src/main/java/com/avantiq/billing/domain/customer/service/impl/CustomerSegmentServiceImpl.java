package com.avantiq.billing.domain.customer.service.impl;

import com.avantiq.billing.domain.common.exception.RepositoryException;
import com.avantiq.billing.domain.customer.model.CustomerSegment;
import com.avantiq.billing.domain.customer.repository.CustomerSegmentRepository;
import com.avantiq.billing.domain.customer.service.interfaces.CustomerSegmentService;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

@Slf4j
public class CustomerSegmentServiceImpl implements CustomerSegmentService {
  private final CustomerSegmentRepository customerSegmentRepository;

  public CustomerSegmentServiceImpl(CustomerSegmentRepository customerSegmentRepository) {
    this.customerSegmentRepository = customerSegmentRepository;
  }

  @Override
  public List<CustomerSegment> findAll() {
    try {
      return customerSegmentRepository.findAll();
    } catch (Exception e) {
      log.error("Error in findAll", e);
      throw new RepositoryException("Failed to fetch all customer segments", e);
    }
  }

  @Override
  public Page<CustomerSegment> findAll(Pageable pageable) {
    try {
      if (pageable == null) {
        throw new IllegalArgumentException("Pageable cannot be null");
      }
      return customerSegmentRepository.findAll(pageable);
    } catch (IllegalArgumentException e) {
      throw e;
    } catch (Exception e) {
      log.error("Error in paginated findAll", e);
      throw new RepositoryException("Failed to fetch customer segments page", e);
    }
  }

  @Override
  public Optional<CustomerSegment> findById(UUID id) {
    try {
      if (id == null) {
        throw new IllegalArgumentException("Segment ID cannot be null");
      }
      return customerSegmentRepository.findById(id);
    } catch (IllegalArgumentException e) {
      throw e;
    } catch (Exception e) {
      log.error("Error in findById for ID: {}", id, e);
      throw new RepositoryException("Failed to fetch customer segment by ID", e);
    }
  }

  @Override
  public List<CustomerSegment> listSegments() {
    try {
      return customerSegmentRepository.findAll();
    } catch (Exception e) {
      log.error("Error in listSegments", e);
      throw new RepositoryException("Failed to list customer segments", e);
    }
  }

  @Override
  public CustomerSegment save(CustomerSegment segment) {
    try {
      if (segment == null) {
        throw new IllegalArgumentException("Segment cannot be null");
      }
      return customerSegmentRepository.save(segment);
    } catch (IllegalArgumentException e) {
      throw e;
    } catch (Exception e) {
      log.error("Error in save for segment: {}", segment.getName(), e);
      throw new RepositoryException("Failed to save customer segment", e);
    }
  }
}
