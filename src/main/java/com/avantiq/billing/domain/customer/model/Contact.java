package com.avantiq.billing.domain.customer.model;

import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class Contact {
  private UUID id;
  private String firstName;
  private String lastName;
  private String email;
  private String contactType;
  private boolean isDefault;
  private Long tenantId;

  public Long getTenantId() {
    return tenantId;
  }

  public void setTenantId(Long tenantId) {
    this.tenantId = tenantId;
  }

  public Contact(
      String id,
      UUID customerId,
      String firstName,
      String lastName,
      String email,
      String contactType,
      boolean isDefault) {
    this.id = UUID.fromString(id);
    this.firstName = firstName;
    this.lastName = lastName;
    this.email = email;
    this.contactType = contactType;
    this.isDefault = isDefault;
  }

  public Contact(
      UUID customerId,
      String firstName,
      String lastName,
      String email,
      String contactType,
      boolean isDefault) {
    this.id = UUID.randomUUID();
    this.firstName = firstName;
    this.lastName = lastName;
    this.email = email;
    this.contactType = contactType;
    this.isDefault = isDefault;
  }

  public void updateDetails(
      String firstName, String lastName, String email, String contactType, boolean isDefault) {
    this.firstName = firstName;
    this.lastName = lastName;
    this.email = email;
    this.contactType = contactType;
    this.isDefault = isDefault;
  }

  public boolean isDefault() {
    return isDefault;
  }
}
