package com.avantiq.billing.domain.customer.model;

import java.time.LocalDateTime;
import java.util.UUID;
import lombok.Builder;
import lombok.Value;

/**
 * CustomerRelationship domain model representing hierarchical relationships between customers. This
 * supports complex customer structures like parent companies with subsidiaries.
 */
@Value
@Builder(toBuilder = true)
public class CustomerRelationship {
  UUID id;
  UUID parentCustomerId;
  UUID childCustomerId;
  RelationshipType type;
  Integer level;
  Long tenantId;
  UUID segmentId;
  LocalDateTime createdAt;
  String createdBy;

  /** Validate customer relationship for business rules. */
  public static void validateCustomerRelationship(CustomerRelationship relationship) {
    if (relationship == null) {
      throw new IllegalArgumentException("Customer relationship cannot be null");
    }

    if (relationship.getParentCustomerId() == null) {
      throw new IllegalArgumentException("Parent customer ID cannot be null");
    }

    if (relationship.getChildCustomerId() == null) {
      throw new IllegalArgumentException("Child customer ID cannot be null");
    }

    if (relationship.getParentCustomerId().equals(relationship.getChildCustomerId())) {
      throw new IllegalArgumentException("Parent and child customer IDs cannot be the same");
    }

    if (relationship.getType() == null) {
      throw new IllegalArgumentException("Relationship type cannot be null");
    }

    if (relationship.getLevel() != null && relationship.getLevel() < 1) {
      throw new IllegalArgumentException("Relationship level must be at least 1");
    }

    if (relationship.getTenantId() == null) {
      throw new IllegalArgumentException("Tenant ID cannot be null");
    }
  }

  /** Check if this relationship involves the specified customer. */
  public boolean involvesCustomer(UUID customerId) {
    return customerId != null
        && (customerId.equals(parentCustomerId) || customerId.equals(childCustomerId));
  }

  /** Check if this is a direct parent-child relationship (level 1). */
  public boolean isDirectRelationship() {
    return level != null && level == 1;
  }

  /** Get the relationship type as string for external systems. */
  public String getRelationshipTypeString() {
    return type != null ? type.name() : "";
  }

  /** Types of customer relationships supported. */
  public enum RelationshipType {
    PAYING("Paying relationship - parent pays for child"),
    REPORTING("Reporting relationship - child reports to parent");

    private final String description;

    RelationshipType(String description) {
      this.description = description;
    }

    public String getDescription() {
      return description;
    }

    /** Parse relationship type from string with fallback. */
    public static RelationshipType parseFromString(String typeString) {
      if (typeString == null || typeString.trim().isEmpty()) {
        return REPORTING; // Default
      }

      try {
        return valueOf(typeString.toUpperCase());
      } catch (IllegalArgumentException e) {
        // Try common aliases
        return switch (typeString.toUpperCase()) {
          case "PAYING", "PAY" -> PAYING;
          case "REPORTING", "REPORT" -> REPORTING;
          default -> REPORTING; // Default fallback
        };
      }
    }
  }
}
