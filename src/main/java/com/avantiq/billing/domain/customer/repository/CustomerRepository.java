package com.avantiq.billing.domain.customer.repository;

import com.avantiq.billing.domain.customer.model.Customer;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Repository interface for Customer domain entity. This defines the contract for accessing customer
 * data without implementation details.
 */
public interface CustomerRepository {
  /**
   * Find a customer by ID.
   *
   * @param id the customer ID
   * @return an Optional containing the customer if found, or empty if not found
   */
  Optional<Customer> findById(UUID id);

  /**
   * Find a customer by ID with tenant isolation.
   *
   * @param id the customer ID
   * @param tenantId the tenant ID
   * @return an Optional containing the customer if found, or empty if not found
   */
  Optional<Customer> findByIdAndTenantId(UUID id, Long tenantId);

  /**
   * Find a customer by email.
   *
   * @param email the customer email
   * @return an Optional containing the customer if found, or empty if not found
   */
  Optional<Customer> findByEmail(String email);

  /**
   * Find a customer by email with tenant isolation.
   *
   * @param email the customer email
   * @param tenantId the tenant ID
   * @return an Optional containing the customer if found, or empty if not found
   */
  Optional<Customer> findByEmailAndTenantId(String email, Long tenantId);

  /**
   * Find customers by first name and last name.
   *
   * @param firstName the customer first name
   * @param lastName the customer last name
   * @return a list of matching customers
   */
  List<Customer> findByFirstNameAndLastName(String firstName, String lastName);

  /**
   * Find customers by segment ID.
   *
   * @param segmentId the segment ID
   * @return a list of customers in the segment
   */
  List<Customer> findBySegmentId(UUID segmentId);

  /**
   * Find customers by company name.
   *
   * @param companyName the company name
   * @return a list of customers with the company name
   */
  List<Customer> findByCompanyName(String companyName);

  /**
   * Find customers by country.
   *
   * @param country the country
   * @return a list of customers in the country
   */
  List<Customer> findByCountry(String country);

  /**
   * Find customers by status.
   *
   * @param status the customer status
   * @return a list of customers with the status
   */
  List<Customer> findByStatus(Customer.Status status);

  /**
   * Save a customer.
   *
   * @param customer the customer to save
   * @return the saved customer
   */
  Customer save(Customer customer);

  /**
   * Delete a customer.
   *
   * @param customer the customer to delete
   */
  void delete(Customer customer);

  /**
   * Find a customer by ID with addresses eagerly loaded.
   *
   * @param id the customer ID
   * @return an Optional containing the customer with addresses if found, or empty if not found
   */
  Optional<Customer> findByIdWithAddresses(UUID id);

  /**
   * Find a customer by email with addresses eagerly loaded.
   *
   * @param email the customer email
   * @return an Optional containing the customer with addresses if found, or empty if not found
   */
  Optional<Customer> findByEmailWithAddresses(String email);

  /**
   * Find a customer by ID with addresses and tenant isolation.
   *
   * @param id the customer ID
   * @param tenantId the tenant ID
   * @return an Optional containing the customer with addresses if found, or empty if not found
   */
  Optional<Customer> findByIdWithAddressesAndTenantId(UUID id, Long tenantId);

  /**
   * Find a customer by email with addresses and tenant isolation.
   *
   * @param email the customer email
   * @param tenantId the tenant ID
   * @return an Optional containing the customer with addresses if found, or empty if not found
   */
  Optional<Customer> findByEmailWithAddressesAndTenantId(String email, Long tenantId);

  /**
   * Check if a customer exists by email with tenant isolation.
   *
   * @param email the customer email
   * @param tenantId the tenant ID
   * @return true if customer exists, false otherwise
   */
  boolean existsByEmailAndTenantId(String email, Long tenantId);

  /**
   * Find all customers with pagination and tenant isolation.
   *
   * @param tenantId the tenant ID
   * @param pageable pagination information
   * @return a page of customers
   */
  Page<Customer> findAllByTenantId(Long tenantId, Pageable pageable);

  /**
   * Find customers by status with pagination and tenant isolation.
   *
   * @param status the customer status
   * @param tenantId the tenant ID
   * @param pageable pagination information
   * @return a page of customers
   */
  Page<Customer> findByStatusAndTenantId(Customer.Status status, Long tenantId, Pageable pageable);

  /**
   * Find customers by segment with pagination and tenant isolation.
   *
   * @param segmentId the segment ID
   * @param tenantId the tenant ID
   * @param pageable pagination information
   * @return a page of customers
   */
  Page<Customer> findBySegmentIdAndTenantId(UUID segmentId, Long tenantId, Pageable pageable);
}
