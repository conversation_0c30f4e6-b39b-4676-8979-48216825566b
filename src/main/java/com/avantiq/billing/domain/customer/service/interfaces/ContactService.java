package com.avantiq.billing.domain.customer.service.interfaces;

import com.avantiq.billing.domain.customer.model.Contact;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface ContactService {

  List<Contact> findByCustomerId(UUID customerId);

  Optional<Contact> findById(UUID id);

  Contact save(Contact contact);

  void addContact(
      UUID customerId,
      String firstName,
      String lastName,
      String email,
      String contactType,
      boolean isDefault);

  List<Contact> listContacts(UUID customerId);

  void updateContact(
      UUID contactId,
      String firstName,
      String lastName,
      String email,
      String contactType,
      boolean isDefault);
}
