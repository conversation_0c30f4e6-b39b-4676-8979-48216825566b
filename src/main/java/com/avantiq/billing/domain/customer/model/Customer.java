package com.avantiq.billing.domain.customer.model;

import com.avantiq.billing.domain.common.exception.ValidationException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Customer domain entity representing a customer in the billing system. This is a pure domain
 * entity without any infrastructure concerns.
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class Customer {
  private UUID id;
  private String firstName;
  private String lastName;
  private String email;
  private String companyName;
  private String vatNumber;
  private String country;
  private UUID segmentId;
  private LocalDateTime createdAt;
  private LocalDateTime updatedAt;
  private Status status;
  private List<Address> addresses = new ArrayList<>();
  private Long tenantId;

  public enum Status {
    ACTIVE,
    INACTIVE
  }

  // Constructors
  public Customer(
      UUID id,
      String firstName,
      String lastName,
      String email,
      String country,
      UUID segmentId,
      Status status) {
    this.id = id;
    this.firstName = firstName;
    this.lastName = lastName;
    this.email = email;
    this.country = country;
    this.segmentId = segmentId;
    this.status = status;
    this.createdAt = LocalDateTime.now();
    this.updatedAt = LocalDateTime.now();
  }

  // Domain methods
  public void addAddress(Address address) {
    addresses.add(address);
  }

  public void removeAddress(Address address) {
    addresses.remove(address);
  }

  public void activate() {
    validateCanActivate();
    this.status = Status.ACTIVE;
    this.updatedAt = LocalDateTime.now();
  }

  public void deactivate() {
    this.status = Status.INACTIVE;
    this.updatedAt = LocalDateTime.now();
  }

  public boolean isActive() {
    return Status.ACTIVE.equals(this.status);
  }

  public void updateDetails(
      String firstName,
      String lastName,
      String email,
      String companyName,
      String vatNumber,
      String country) {
    this.firstName = firstName;
    this.lastName = lastName;
    this.email = email;
    this.companyName = companyName;
    this.vatNumber = vatNumber;
    this.country = country;
    this.updatedAt = LocalDateTime.now();
    validate(); // Validate after updating
  }

  /**
   * Validates customer data according to business rules.
   *
   * @throws IllegalArgumentException if validation fails
   */
  public void validate() {
    if (firstName == null || firstName.trim().isEmpty()) {
      throw new ValidationException(
          "Customer first name cannot be null or empty", "firstName", firstName);
    }

    if (lastName == null || lastName.trim().isEmpty()) {
      throw new ValidationException(
          "Customer last name cannot be null or empty", "lastName", lastName);
    }

    if (email == null || email.trim().isEmpty()) {
      throw new ValidationException("Customer email cannot be null or empty", "email", email);
    }

    if (!isValidEmail(email)) {
      throw new ValidationException("Customer email format is invalid", "email", email);
    }

    if (tenantId == null) {
      throw new ValidationException("Customer tenant ID cannot be null", "tenantId", tenantId);
    }

    if (country != null && !isValidCountryCode(country)) {
      throw new ValidationException("Invalid country code", "country", country);
    }

    // Validate addresses
    if (addresses != null) {
      for (Address address : addresses) {
        if (address != null) {
          address.validate();
        }
      }
    }
  }

  /** Validates email format using a simple regex. */
  private boolean isValidEmail(String email) {
    return email != null && email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
  }

  /** Validates country code (ISO 3166-1 alpha-2). */
  private boolean isValidCountryCode(String country) {
    return country != null && country.matches("^[A-Z]{2}$");
  }

  /** Validates that the customer can be activated. */
  public void validateCanActivate() {
    if (email == null || email.trim().isEmpty()) {
      throw new ValidationException("Cannot activate customer without email", "email", email);
    }

    if (firstName == null || firstName.trim().isEmpty()) {
      throw new ValidationException(
          "Cannot activate customer without first name", "firstName", firstName);
    }

    if (lastName == null || lastName.trim().isEmpty()) {
      throw new ValidationException(
          "Cannot activate customer without last name", "lastName", lastName);
    }
  }
}
