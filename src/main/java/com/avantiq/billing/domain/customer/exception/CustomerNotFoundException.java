package com.avantiq.billing.domain.customer.exception;

import com.avantiq.billing.domain.common.exception.DomainException;
import com.avantiq.billing.domain.common.exception.ErrorCode;

/** Exception thrown when a customer with the requested identifier cannot be found. */
public class CustomerNotFoundException extends DomainException {

  public CustomerNotFoundException(String id) {
    super("Customer not found with ID: " + id, ErrorCode.CUSTOMER_NOT_FOUND);
  }

  public CustomerNotFoundException(String message, Throwable cause) {
    super(message, cause, ErrorCode.CUSTOMER_NOT_FOUND);
  }
}
