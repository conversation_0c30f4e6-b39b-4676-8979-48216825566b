package com.avantiq.billing.domain.customer.repository;

import com.avantiq.billing.domain.customer.model.CustomerRelationship;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Domain repository interface for CustomerRelationship aggregate. Defines persistence operations
 * without exposing infrastructure concerns.
 */
public interface CustomerRelationshipRepository {

  /** Find customer relationship by ID with tenant isolation. */
  Optional<CustomerRelationship> findByIdAndTenantId(UUID id, Long tenantId);

  /** Find all relationships where the customer is a parent. */
  List<CustomerRelationship> findByParentCustomerIdAndTenantId(
      UUID parentCustomerId, Long tenantId);

  /** Find all relationships where the customer is a child. */
  List<CustomerRelationship> findByChildCustomerIdAndTenantId(UUID childCustomerId, Long tenantId);

  /** Find all relationships involving a customer (parent or child). */
  List<CustomerRelationship> findByCustomerIdAndTenantId(UUID customerId, Long tenantId);

  /** Find relationships by type within a tenant. */
  List<CustomerRelationship> findByTypeAndTenantId(
      CustomerRelationship.RelationshipType type, Long tenantId);

  /** Find direct relationships (level 1) for a customer. */
  List<CustomerRelationship> findDirectRelationshipsByCustomerIdAndTenantId(
      UUID customerId, Long tenantId);

  /** Save customer relationship. */
  CustomerRelationship save(CustomerRelationship relationship);

  /** Delete relationship by ID with tenant isolation. */
  void deleteByIdAndTenantId(UUID id, Long tenantId);

  /** Check if relationship exists between two customers. */
  boolean existsRelationshipBetweenCustomers(
      UUID parentCustomerId, UUID childCustomerId, Long tenantId);

  /** Check if a relationship would create a circular dependency. */
  boolean wouldCreateCircularDependency(UUID parentCustomerId, UUID childCustomerId, Long tenantId);

  /** Count relationships for a customer within a tenant. */
  long countByCustomerIdAndTenantId(UUID customerId, Long tenantId);
}
