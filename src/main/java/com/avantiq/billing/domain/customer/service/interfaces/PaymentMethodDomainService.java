package com.avantiq.billing.domain.customer.service.interfaces;

import java.util.UUID;

/**
 * Domain service interface for payment method operations from customer perspective. This interface
 * provides customer-focused payment method operations.
 */
public interface PaymentMethodDomainService {

  /** Add payment method for a customer. This is a simplified interface for customer operations. */
  UUID addPaymentMethod(UUID customerId, String type, String details, boolean isPrimary);

  /** Remove payment method for a customer. */
  void removePaymentMethod(UUID customerId, UUID paymentMethodId);

  /** Check if customer has any payment methods. */
  boolean hasPaymentMethods(UUID customerId);

  /** Get primary payment method ID for customer. */
  UUID getPrimaryPaymentMethodId(UUID customerId);
}
