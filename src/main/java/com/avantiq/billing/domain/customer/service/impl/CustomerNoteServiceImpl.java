package com.avantiq.billing.domain.customer.service.impl;

import com.avantiq.billing.domain.common.exception.RepositoryException;
import com.avantiq.billing.domain.customer.model.CustomerNote;
import com.avantiq.billing.domain.customer.repository.CustomerNoteRepository;
import com.avantiq.billing.domain.customer.service.interfaces.CustomerNoteService;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CustomerNoteServiceImpl implements CustomerNoteService {
  private final CustomerNoteRepository customerNoteRepository;

  public CustomerNoteServiceImpl(CustomerNoteRepository customerNoteRepository) {
    this.customerNoteRepository = customerNoteRepository;
  }

  @Override
  public CustomerNote createNote(CustomerNote note) {
    try {
      if (note == null) {
        throw new IllegalArgumentException("Note cannot be null");
      }
      return customerNoteRepository.save(note);
    } catch (IllegalArgumentException e) {
      throw e;
    } catch (Exception e) {
      log.error("Error creating note", e);
      throw new RepositoryException("Failed to create customer note", e);
    }
  }

  @Override
  public CustomerNote updateNote(CustomerNote note) {
    try {
      if (note == null) {
        throw new IllegalArgumentException("Note cannot be null");
      }
      if (note.getId() == null) {
        throw new IllegalArgumentException("Note ID cannot be null for update");
      }
      return customerNoteRepository.save(note);
    } catch (IllegalArgumentException e) {
      throw e;
    } catch (Exception e) {
      log.error("Error updating note", e);
      throw new RepositoryException("Failed to update customer note", e);
    }
  }

  @Override
  public boolean deleteNote(UUID noteId) {
    try {
      if (noteId == null) {
        throw new IllegalArgumentException("Note ID cannot be null");
      }
      customerNoteRepository.deleteById(noteId);
      return true;
    } catch (IllegalArgumentException e) {
      throw e;
    } catch (Exception e) {
      log.error("Error deleting note with ID: {}", noteId, e);
      throw new RepositoryException("Failed to delete customer note", e);
    }
  }

  @Override
  public Optional<CustomerNote> getNoteById(UUID noteId) {
    try {
      if (noteId == null) {
        throw new IllegalArgumentException("Note ID cannot be null");
      }
      return customerNoteRepository.findById(noteId);
    } catch (IllegalArgumentException e) {
      throw e;
    } catch (Exception e) {
      log.error("Error fetching note by ID: {}", noteId, e);
      throw new RepositoryException("Failed to fetch customer note by ID", e);
    }
  }

  @Override
  public List<CustomerNote> getNotesByCustomerId(UUID customerId) {
    try {
      if (customerId == null) {
        throw new IllegalArgumentException("Customer ID cannot be null");
      }
      return customerNoteRepository.findByCustomerId(customerId);
    } catch (IllegalArgumentException e) {
      throw e;
    } catch (Exception e) {
      log.error("Error fetching notes by customer ID: {}", customerId, e);
      throw new RepositoryException("Failed to fetch customer notes", e);
    }
  }
}
