package com.avantiq.billing.domain.customer.repository;

import com.avantiq.billing.domain.customer.model.CustomerSegment;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

/**
 * Repository interface for CustomerSegment domain entity. This defines the contract for accessing
 * customer segment data without implementation details.
 */
@Repository
public interface CustomerSegmentRepository {
  /**
   * Find all customer segments.
   *
   * @return a list of all customer segments
   */
  List<CustomerSegment> findAll();

  /**
   * Find all customer segments.
   *
   * @return a list of all customer segments
   */
  Page<CustomerSegment> findAll(Pageable pageable);

  /**
   * Find a customer segment by ID.
   *
   * @param id the segment ID
   * @return an Optional containing the segment if found, or empty if not found
   */
  Optional<CustomerSegment> findById(UUID id);

  /**
   * Find a customer segment by name.
   *
   * @param name the segment name
   * @return an Optional containing the segment if found, or empty if not found
   */
  Optional<CustomerSegment> findByName(String name);

  /**
   * Save a customer segment.
   *
   * @param segment the segment to save
   * @return the saved segment
   */
  CustomerSegment save(CustomerSegment segment);

  /**
   * Delete a customer segment.
   *
   * @param segment the segment to delete
   */
  void delete(CustomerSegment segment);
}
