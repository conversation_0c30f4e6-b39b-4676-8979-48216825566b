package com.avantiq.billing.domain.customer.validator;

import com.avantiq.billing.domain.customer.model.Address;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * Validator for customer addresses. Contains validation logic for address fields based on country.
 */
public class AddressValidator {

  private static final Map<String, Pattern> POSTAL_CODE_PATTERNS = new HashMap<>();

  static {
    // US postal code: 5 digits or 5+4 format (e.g., 12345 or 12345-6789)
    POSTAL_CODE_PATTERNS.put("US", Pattern.compile("^\\d{5}(-\\d{4})?$"));

    // UK postcode: Format like AA9A 9AA, A9A 9AA, A9 9AA, A99 9AA, AA9 9AA, AA99 9AA
    POSTAL_CODE_PATTERNS.put(
        "GB",
        Pattern.compile("^[A-Z]{1,2}[0-9][A-Z0-9]? ?[0-9][A-Z]{2}$", Pattern.CASE_INSENSITIVE));

    // Canada postal code: A9A 9A9 format with a space
    POSTAL_CODE_PATTERNS.put(
        "CA", Pattern.compile("^[A-Z][0-9][A-Z] ?[0-9][A-Z][0-9]$", Pattern.CASE_INSENSITIVE));

    // German postal code: 5 digits
    POSTAL_CODE_PATTERNS.put("DE", Pattern.compile("^\\d{5}$"));

    // Default pattern (for countries not specifically defined): 1-10 alphanumeric chars
    POSTAL_CODE_PATTERNS.put("DEFAULT", Pattern.compile("^[a-zA-Z0-9- ]{1,10}$"));
  }

  private AddressValidator() {
    // Utility class, no instantiation
  }

  /**
   * Validates an address against country-specific rules.
   *
   * @param address the address to validate
   * @return true if the address is valid, false otherwise
   */
  public static boolean isValid(Address address) {
    if (address == null) {
      return false;
    }

    // Required fields check
    if (isEmpty(address.getStreet())
        || isEmpty(address.getCity())
        || isEmpty(address.getPostalCode())
        || isEmpty(address.getCountry())) {
      return false;
    }

    // Postal code validation based on country
    return isValidPostalCode(address.getPostalCode(), address.getCountry());
  }

  /**
   * Validates a postal code against country-specific patterns.
   *
   * @param postalCode the postal code to validate
   * @param country the country code (ISO 2-letter)
   * @return true if valid, false otherwise
   */
  public static boolean isValidPostalCode(String postalCode, String country) {
    if (isEmpty(postalCode) || isEmpty(country)) {
      return false;
    }

    Pattern pattern =
        POSTAL_CODE_PATTERNS.getOrDefault(
            country.toUpperCase(), POSTAL_CODE_PATTERNS.get("DEFAULT"));

    return pattern.matcher(postalCode).matches();
  }

  private static boolean isEmpty(String str) {
    return str == null || str.trim().isEmpty();
  }
}
