package com.avantiq.billing.domain.security.repository;

import com.avantiq.billing.domain.security.model.Role;
import com.avantiq.billing.domain.security.model.User;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Domain repository interface for User entity. Defines the contract for user data access
 * operations.
 */
public interface UserRepository {

  /** Find user by ID within tenant context. */
  Optional<User> findById(UUID userId);

  /** Find user by username (global search for authentication). */
  Optional<User> findByUsername(String username);

  /** Find user by email (global search for authentication). */
  Optional<User> findByEmail(String email);

  /** Find user by username within tenant context. */
  Optional<User> findByUsernameAndTenantId(String username, Long tenantId);

  /** Find user by email within tenant context. */
  Optional<User> findByEmailAndTenantId(String email, Long tenantId);

  /** Save a user (create or update). */
  User save(User user);

  /** Delete a user by ID. */
  void deleteById(UUID userId);

  /** Find all users in a tenant. */
  List<User> findAllByTenantId(Long tenantId);

  /** Find active users in a tenant. */
  List<User> findActiveUsersByTenantId(Long tenantId);

  /** Find users by segment assignment. */
  List<User> findBySegmentIdAndTenantId(UUID segmentId, Long tenantId);

  /** Find users with specific role in tenant. */
  List<User> findByRoleAndTenantId(Role role, Long tenantId);

  /** Check if username exists in tenant. */
  boolean existsByUsernameAndTenantId(String username, Long tenantId);

  /** Check if email exists in tenant. */
  boolean existsByEmailAndTenantId(String email, Long tenantId);

  /** Count users in tenant. */
  long countByTenantId(Long tenantId);

  /** Count active users in tenant. */
  long countActiveUsersByTenantId(Long tenantId);
}
