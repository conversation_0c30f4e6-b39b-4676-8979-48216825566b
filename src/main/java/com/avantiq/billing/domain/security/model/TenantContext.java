package com.avantiq.billing.domain.security.model;

import java.util.Set;
import java.util.UUID;
import lombok.Builder;
import lombok.Value;

/**
 * Tenant context for request-scoped tenant information. Holds the current tenant and segment access
 * information for the request.
 */
@Value
@Builder
public class TenantContext {
  Long tenantId;
  UUID userId;
  Set<UUID> accessibleSegments;
  Role highestRole;

  /** Check if the current context can access a specific segment. */
  public boolean canAccessSegment(UUID segmentId) {
    // TENANT_ADMIN can access all segments
    if (highestRole == Role.TENANT_ADMIN) {
      return true;
    }
    // Other roles need explicit segment access
    return accessibleSegments != null && accessibleSegments.contains(segmentId);
  }

  /** Check if the current context has tenant-wide access. */
  public boolean hasTenantWideAccess() {
    return highestRole == Role.TENANT_ADMIN;
  }

  /** Create a new TenantContext from a User. */
  public static TenantContext fromUser(User user) {
    return TenantContext.builder()
        .tenantId(user.getTenantId())
        .userId(user.getUserId())
        .accessibleSegments(user.getAssignedSegments())
        .highestRole(user.getHighestRole())
        .build();
  }
}
