package com.avantiq.billing.domain.security.model;

/** Security permissions for the multi-tenant billing system. */
public enum Permission {
  /** Read access to all data within tenant. */
  READ_ALL_TENANT_DATA,

  /** Write access to all data within tenant. */
  WRITE_ALL_TENANT_DATA,

  /** Read access to specific segment data. */
  READ_SEGMENT_DATA,

  /** Write access to specific segment data. */
  WRITE_SEGMENT_DATA,

  /** Manage all users within tenant. */
  MANAGE_USERS,

  /** Manage users within specific segments. */
  MANA<PERSON>_SEGMENT_USERS,

  /** Manage segments within tenant. */
  <PERSON><PERSON><PERSON>_SEGMENTS
}
