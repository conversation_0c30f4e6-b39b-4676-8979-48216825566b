package com.avantiq.billing.domain.security.model;

import java.util.Collection;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.Builder;
import lombok.Value;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

/**
 * Spring Security UserDetails implementation for the billing system. Integrates domain User model
 * with Spring Security.
 */
@Value
@Builder
public class UserPrincipal implements UserDetails {
  UUID userId;
  String username;
  String email;
  String password;
  Long tenantId;
  Set<Role> roles;
  Set<UUID> assignedSegments;
  UserStatus status;

  @Override
  public Collection<? extends GrantedAuthority> getAuthorities() {
    return roles.stream()
        .map(role -> new SimpleGrantedAuthority("ROLE_" + role.getAuthority()))
        .collect(Collectors.toSet());
  }

  @Override
  public String getPassword() {
    return password;
  }

  @Override
  public String getUsername() {
    return username;
  }

  @Override
  public boolean isAccountNonExpired() {
    return status != UserStatus.SUSPENDED;
  }

  @Override
  public boolean isAccountNonLocked() {
    return status != UserStatus.SUSPENDED;
  }

  @Override
  public boolean isCredentialsNonExpired() {
    return true; // We handle this through JWT token expiration
  }

  @Override
  public boolean isEnabled() {
    return status == UserStatus.ACTIVE;
  }

  /** Check if user has a specific role. */
  public boolean hasRole(Role role) {
    return roles != null && roles.contains(role);
  }

  /** Check if user can access a specific segment. */
  public boolean canAccessSegment(UUID segmentId) {
    if (hasRole(Role.TENANT_ADMIN)) {
      return true;
    }
    return assignedSegments != null && assignedSegments.contains(segmentId);
  }

  /** Get the tenant context for this user. */
  public TenantContext getTenantContext() {
    return TenantContext.builder()
        .tenantId(tenantId)
        .userId(userId)
        .accessibleSegments(assignedSegments)
        .highestRole(getHighestRole())
        .build();
  }

  /** Get the highest privilege role. */
  public Role getHighestRole() {
    if (roles == null || roles.isEmpty()) {
      return Role.READONLY;
    }

    if (roles.contains(Role.TENANT_ADMIN)) return Role.TENANT_ADMIN;
    if (roles.contains(Role.SEGMENT_ADMIN)) return Role.SEGMENT_ADMIN;
    if (roles.contains(Role.USER)) return Role.USER;
    return Role.READONLY;
  }

  /** Create UserPrincipal from domain User model. */
  public static UserPrincipal fromUser(User user) {
    return UserPrincipal.builder()
        .userId(user.getUserId())
        .username(user.getUsername())
        .email(user.getEmail())
        .password(user.getPasswordHash())
        .tenantId(user.getTenantId())
        .roles(user.getRoles())
        .assignedSegments(user.getAssignedSegments())
        .status(user.getStatus())
        .build();
  }
}
