package com.avantiq.billing.domain.security.model;

import java.util.Set;

/** Security roles for multi-tenant billing system. */
public enum Role {
  /** Full access to all tenant data and segments. */
  TENANT_ADMIN(
      "TENANT_ADMIN",
      Set.of(
          Permission.READ_ALL_TENANT_DATA,
          Permission.WRITE_ALL_TENANT_DATA,
          Permission.MANAGE_USERS,
          Permission.MANAGE_SEGMENTS)),

  /** Access to specific segments within tenant. */
  SEGMENT_ADMIN(
      "SEGMENT_ADMIN",
      Set.of(
          Permission.READ_SEGMENT_DATA,
          Permission.WRITE_SEGMENT_DATA,
          Permission.MANAGE_SEGMENT_USERS)),

  /** Read/write access to assigned segments. */
  USER("USER", Set.of(Permission.READ_SEGMENT_DATA, Permission.WRITE_SEGMENT_DATA)),

  /** Read-only access to assigned segments. */
  READONLY("READONLY", Set.of(Permission.READ_SEGMENT_DATA));

  private final String authority;
  private final Set<Permission> permissions;

  Role(String authority, Set<Permission> permissions) {
    this.authority = authority;
    this.permissions = permissions;
  }

  public String getAuthority() {
    return authority;
  }

  public Set<Permission> getPermissions() {
    return permissions;
  }

  public boolean hasPermission(Permission permission) {
    return permissions.contains(permission);
  }

  /** Check if this role can access all segments within a tenant. */
  public boolean canAccessAllSegments() {
    return this == TENANT_ADMIN;
  }

  /** Check if this role can manage users. */
  public boolean canManageUsers() {
    return hasPermission(Permission.MANAGE_USERS) || hasPermission(Permission.MANAGE_SEGMENT_USERS);
  }
}
