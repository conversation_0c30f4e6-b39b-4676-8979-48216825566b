package com.avantiq.billing.domain.security.model;

import com.avantiq.billing.domain.common.Metadata;
import java.util.Set;
import java.util.UUID;
import lombok.Builder;
import lombok.Value;

/**
 * User domain model for multi-tenant billing system. Represents a user with tenant association,
 * roles, and segment access.
 */
@Value
@Builder
public class User {
  UUID userId;
  String username;
  String email;
  String passwordHash;
  Long tenantId;
  Set<Role> roles;
  Set<UUID> assignedSegments;
  UserStatus status;
  Metadata metadata;

  /** Check if user has a specific role. */
  public boolean hasRole(Role role) {
    return roles != null && roles.contains(role);
  }

  /** Check if user has a specific permission. */
  public boolean hasPermission(Permission permission) {
    if (roles == null) {
      return false;
    }
    return roles.stream().anyMatch(role -> role.hasPermission(permission));
  }

  /** Check if user can access a specific segment. */
  public boolean canAccessSegment(UUID segmentId) {
    // TENANT_ADMIN can access all segments
    if (hasRole(Role.TENANT_ADMIN)) {
      return true;
    }
    // Other roles need explicit segment assignment
    return assignedSegments != null && assignedSegments.contains(segmentId);
  }

  /** Check if user belongs to the specified tenant. */
  public boolean belongsToTenant(Long tenantId) {
    return this.tenantId != null && this.tenantId.equals(tenantId);
  }

  /** Check if user is active. */
  public boolean isActive() {
    return status == UserStatus.ACTIVE;
  }

  /** Get the highest privilege role. */
  public Role getHighestRole() {
    if (roles == null || roles.isEmpty()) {
      return Role.READONLY;
    }

    if (roles.contains(Role.TENANT_ADMIN)) return Role.TENANT_ADMIN;
    if (roles.contains(Role.SEGMENT_ADMIN)) return Role.SEGMENT_ADMIN;
    if (roles.contains(Role.USER)) return Role.USER;
    return Role.READONLY;
  }

  /** Check if user can access all segments in their tenant. */
  public boolean canAccessAllSegments() {
    return hasRole(Role.TENANT_ADMIN);
  }

  /** Validate that the password hash is properly BCrypt encoded. */
  public static void validatePasswordHash(String passwordHash) {
    if (passwordHash == null || passwordHash.trim().isEmpty()) {
      throw new IllegalArgumentException("Password hash cannot be null or empty");
    }

    // BCrypt hashes always start with $2a$, $2b$, $2x$, or $2y$ and are 60 characters long
    if (!passwordHash.matches("^\\$2[abxy]\\$\\d{2}\\$.{53}$")) {
      throw new IllegalArgumentException(
          "Password hash must be a valid BCrypt hash. Plain text passwords are not allowed.");
    }
  }

  /** Validate all user fields for consistency and security. */
  public static void validateUser(User user) {
    if (user == null) {
      throw new IllegalArgumentException("User cannot be null");
    }

    if (user.getUsername() == null || user.getUsername().trim().isEmpty()) {
      throw new IllegalArgumentException("Username cannot be null or empty");
    }

    if (user.getEmail() == null || user.getEmail().trim().isEmpty()) {
      throw new IllegalArgumentException("Email cannot be null or empty");
    }

    if (user.getTenantId() == null) {
      throw new IllegalArgumentException("Tenant ID cannot be null");
    }

    if (user.getStatus() == null) {
      throw new IllegalArgumentException("User status cannot be null");
    }

    validatePasswordHash(user.getPasswordHash());
  }
}
