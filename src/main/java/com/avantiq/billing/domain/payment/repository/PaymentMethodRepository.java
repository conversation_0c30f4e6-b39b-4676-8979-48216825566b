package com.avantiq.billing.domain.payment.repository;

import com.avantiq.billing.domain.payment.model.PaymentMethod;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Domain repository interface for PaymentMethod aggregate. This interface defines the contract for
 * payment method persistence without exposing infrastructure concerns.
 */
public interface PaymentMethodRepository {

  /** Find payment method by ID and tenant (for security). */
  Optional<PaymentMethod> findByIdAndTenantId(UUID id, Long tenantId);

  /** Find all payment methods for a customer within a tenant. */
  List<PaymentMethod> findByCustomerIdAndTenantId(UUID customerId, Long tenantId);

  /** Find primary payment method for a customer within a tenant. */
  Optional<PaymentMethod> findPrimaryByCustomerIdAndTenantId(UUID customerId, Long tenantId);

  /** Find payment methods by type within a tenant. */
  List<PaymentMethod> findByTypeAndTenantId(PaymentMethod.PaymentMethodType type, Long tenantId);

  /** Save payment method. */
  PaymentMethod save(PaymentMethod paymentMethod);

  /** Delete payment method by ID and tenant (for security). */
  void deleteByIdAndTenantId(UUID id, Long tenantId);

  /** Check if payment method exists by ID and tenant. */
  boolean existsByIdAndTenantId(UUID id, Long tenantId);

  /** Count payment methods for a customer within a tenant. */
  long countByCustomerIdAndTenantId(UUID customerId, Long tenantId);
}
