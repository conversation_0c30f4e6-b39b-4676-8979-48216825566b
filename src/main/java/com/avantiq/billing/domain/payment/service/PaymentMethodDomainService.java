package com.avantiq.billing.domain.payment.service;

import com.avantiq.billing.domain.payment.model.PaymentMethod;
import java.util.UUID;

/**
 * Domain service interface for PaymentMethod business logic. Contains business rules and
 * validations that don't naturally fit within a single aggregate.
 */
public interface PaymentMethodDomainService {

  /**
   * Validate if a customer can add a new payment method. Checks business rules like maximum number
   * of payment methods per customer.
   */
  void validateCanAddPaymentMethod(UUID customerId, Long tenantId);

  /**
   * Validate if a payment method can be set as primary. Checks business rules and ensures only one
   * primary per customer.
   */
  void validateCanSetAsPrimary(UUID customerId, UUID paymentMethodId, Long tenantId);

  /**
   * Handle setting a payment method as primary. This includes unsetting the current primary method.
   */
  void setPrimaryPaymentMethod(UUID customerId, UUID paymentMethodId, Long tenantId);

  /**
   * Validate if a payment method can be deleted. Checks business rules like preventing deletion of
   * the only payment method.
   */
  void validateCanDeletePaymentMethod(UUID customerId, UUID paymentMethodId, Long tenantId);

  /**
   * Validate payment method details based on type. Each payment method type has different
   * validation rules.
   */
  void validatePaymentMethodDetails(PaymentMethod.PaymentMethodType type, String details);
}
