package com.avantiq.billing.domain.payment.exception;

import com.avantiq.billing.domain.common.exception.DomainException;
import com.avantiq.billing.domain.common.exception.ErrorCode;

/** Exception thrown when a payment method is invalid. */
public class InvalidPaymentMethodException extends DomainException {

  public InvalidPaymentMethodException(String message) {
    super(message, ErrorCode.PAYMENT_METHOD_INVALID);
  }

  public InvalidPaymentMethodException(String message, Throwable cause) {
    super(message, cause, ErrorCode.PAYMENT_METHOD_INVALID);
  }
}
