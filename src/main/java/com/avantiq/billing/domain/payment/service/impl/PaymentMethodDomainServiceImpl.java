package com.avantiq.billing.domain.payment.service.impl;

import com.avantiq.billing.domain.payment.exception.InvalidPaymentMethodException;
import com.avantiq.billing.domain.payment.exception.PaymentMethodNotFoundException;
import com.avantiq.billing.domain.payment.model.PaymentMethod;
import com.avantiq.billing.domain.payment.repository.PaymentMethodRepository;
import com.avantiq.billing.domain.payment.service.PaymentMethodDomainService;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Implementation of PaymentMethodDomainService containing business logic for payment method
 * operations.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PaymentMethodDomainServiceImpl implements PaymentMethodDomainService {

  private final PaymentMethodRepository paymentMethodRepository;

  // Business rule: Maximum payment methods per customer
  private static final int MAX_PAYMENT_METHODS_PER_CUSTOMER = 10;

  // Regex patterns for validation
  private static final Pattern CREDIT_CARD_PATTERN = Pattern.compile("^[0-9]{13,19}$");
  private static final Pattern EMAIL_PATTERN = Pattern.compile("^[A-Za-z0-9+_.-]+@(.+)$");

  @Override
  public void validateCanAddPaymentMethod(UUID customerId, Long tenantId) {
    log.debug(
        "Validating if customer {} can add payment method in tenant {}", customerId, tenantId);

    long currentCount = paymentMethodRepository.countByCustomerIdAndTenantId(customerId, tenantId);

    if (currentCount >= MAX_PAYMENT_METHODS_PER_CUSTOMER) {
      throw new InvalidPaymentMethodException(
          String.format(
              "Customer cannot have more than %d payment methods",
              MAX_PAYMENT_METHODS_PER_CUSTOMER));
    }
  }

  @Override
  public void validateCanSetAsPrimary(UUID customerId, UUID paymentMethodId, Long tenantId) {
    log.debug(
        "Validating if payment method {} can be set as primary for customer {} in tenant {}",
        paymentMethodId,
        customerId,
        tenantId);

    // Verify the payment method exists and belongs to the customer
    Optional<PaymentMethod> paymentMethod =
        paymentMethodRepository.findByIdAndTenantId(paymentMethodId, tenantId);

    if (paymentMethod.isEmpty()) {
      throw new PaymentMethodNotFoundException("Payment method not found: " + paymentMethodId);
    }

    if (!paymentMethod.get().belongsToCustomer(customerId)) {
      throw new InvalidPaymentMethodException("Payment method does not belong to customer");
    }

    if (!paymentMethod.get().isActive()) {
      throw new InvalidPaymentMethodException("Cannot set inactive payment method as primary");
    }
  }

  @Override
  public void setPrimaryPaymentMethod(UUID customerId, UUID paymentMethodId, Long tenantId) {
    log.info(
        "Setting payment method {} as primary for customer {} in tenant {}",
        paymentMethodId,
        customerId,
        tenantId);

    // First validate the operation
    validateCanSetAsPrimary(customerId, paymentMethodId, tenantId);

    // Get all payment methods for the customer
    List<PaymentMethod> customerPaymentMethods =
        paymentMethodRepository.findByCustomerIdAndTenantId(customerId, tenantId);

    // Unset current primary and set new primary
    for (PaymentMethod pm : customerPaymentMethods) {
      if (pm.getId().equals(paymentMethodId)) {
        // Set this one as primary
        PaymentMethod updatedPM = pm.toBuilder().isPrimary(true).build();
        paymentMethodRepository.save(updatedPM);
      } else if (pm.isPrimary()) {
        // Unset this one as primary
        PaymentMethod updatedPM = pm.toBuilder().isPrimary(false).build();
        paymentMethodRepository.save(updatedPM);
      }
    }
  }

  @Override
  public void validateCanDeletePaymentMethod(UUID customerId, UUID paymentMethodId, Long tenantId) {
    log.debug(
        "Validating if payment method {} can be deleted for customer {} in tenant {}",
        paymentMethodId,
        customerId,
        tenantId);

    // Verify the payment method exists and belongs to the customer
    Optional<PaymentMethod> paymentMethod =
        paymentMethodRepository.findByIdAndTenantId(paymentMethodId, tenantId);

    if (paymentMethod.isEmpty()) {
      throw new PaymentMethodNotFoundException("Payment method not found: " + paymentMethodId);
    }

    if (!paymentMethod.get().belongsToCustomer(customerId)) {
      throw new InvalidPaymentMethodException("Payment method does not belong to customer");
    }

    // Business rule: Cannot delete the last payment method
    long totalMethods = paymentMethodRepository.countByCustomerIdAndTenantId(customerId, tenantId);
    if (totalMethods <= 1) {
      throw new InvalidPaymentMethodException("Cannot delete the last payment method for customer");
    }

    // If deleting primary, ensure there's another method that can become primary
    if (paymentMethod.get().isPrimary()) {
      List<PaymentMethod> otherMethods =
          paymentMethodRepository.findByCustomerIdAndTenantId(customerId, tenantId);

      boolean hasOtherActiveMethod =
          otherMethods.stream()
              .anyMatch(pm -> !pm.getId().equals(paymentMethodId) && pm.isActive());

      if (!hasOtherActiveMethod) {
        throw new InvalidPaymentMethodException(
            "Cannot delete primary payment method when no other active methods exist");
      }
    }
  }

  @Override
  public void validatePaymentMethodDetails(PaymentMethod.PaymentMethodType type, String details) {
    log.debug("Validating payment method details for type: {}", type);

    if (details == null || details.trim().isEmpty()) {
      throw new InvalidPaymentMethodException("Payment method details cannot be empty");
    }

    switch (type) {
      case CREDIT_CARD, DEBIT_CARD -> {
        if (!CREDIT_CARD_PATTERN.matcher(details.replaceAll("\\s+", "")).matches()) {
          throw new InvalidPaymentMethodException("Invalid credit/debit card number format");
        }
      }
      case PAYPAL -> {
        if (!EMAIL_PATTERN.matcher(details).matches()) {
          throw new InvalidPaymentMethodException("PayPal payment method requires valid email");
        }
      }
      case BANK_TRANSFER, ACH -> {
        // Basic validation - should contain account and routing numbers
        if (details.length() < 10) {
          throw new InvalidPaymentMethodException("Bank transfer details too short");
        }
      }
      case WIRE_TRANSFER -> {
        // Basic validation for wire transfer details
        if (details.length() < 15) {
          throw new InvalidPaymentMethodException("Wire transfer details incomplete");
        }
      }
      case STRIPE -> {
        // Stripe token validation - should start with specific prefixes
        if (!details.startsWith("tok_") && !details.startsWith("pm_")) {
          throw new InvalidPaymentMethodException("Invalid Stripe token format");
        }
      }
      default -> {
        throw new InvalidPaymentMethodException("Unsupported payment method type: " + type);
      }
    }
  }
}
