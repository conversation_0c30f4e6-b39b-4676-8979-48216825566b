package com.avantiq.billing.domain.payment.exception;

import com.avantiq.billing.domain.common.exception.DomainException;
import com.avantiq.billing.domain.common.exception.ErrorCode;

/** Exception thrown when a payment method cannot be found. */
public class PaymentMethodNotFoundException extends DomainException {

  /**
   * Constructs a new PaymentMethodNotFoundException with the specified payment method ID.
   *
   * @param paymentMethodId the ID of the payment method that was not found
   */
  public PaymentMethodNotFoundException(String paymentMethodId) {
    super(
        "Payment method not found with ID: " + paymentMethodId, ErrorCode.PAYMENT_METHOD_NOT_FOUND);
  }

  /**
   * Constructs a new PaymentMethodNotFoundException with the specified payment method ID and cause.
   *
   * @param paymentMethodId the ID of the payment method that was not found
   * @param cause the cause of the exception
   */
  public PaymentMethodNotFoundException(String paymentMethodId, Throwable cause) {
    super(
        "Payment method not found with ID: " + paymentMethodId,
        cause,
        ErrorCode.PAYMENT_METHOD_NOT_FOUND);
  }
}
