package com.avantiq.billing.domain.payment.exception;

import com.avantiq.billing.domain.common.exception.DomainException;
import com.avantiq.billing.domain.common.exception.ErrorCode;

/** Exception thrown when there's an issue processing a payment. */
public class PaymentProcessingException extends DomainException {

  public PaymentProcessingException(String message) {
    super(message, ErrorCode.PAYMENT_PROCESSING_ERROR);
  }

  public PaymentProcessingException(String message, Throwable cause) {
    super(message, cause, ErrorCode.PAYMENT_PROCESSING_ERROR);
  }
}
