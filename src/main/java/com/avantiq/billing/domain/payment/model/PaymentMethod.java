package com.avantiq.billing.domain.payment.model;

import java.time.LocalDateTime;
import java.util.UUID;
import lombok.Builder;
import lombok.Value;

/**
 * PaymentMethod domain model representing a customer's payment method. This is the core domain
 * representation separate from persistence concerns.
 */
@Value
@Builder(toBuilder = true)
public class PaymentMethod {
  UUID id;
  UUID customerId;
  Long tenantId;
  PaymentMethodType type;
  String details;
  boolean isPrimary;
  boolean isValidated;
  LocalDateTime createdAt;
  LocalDateTime lastModifiedAt;

  /** Check if this payment method belongs to the specified customer. */
  public boolean belongsToCustomer(UUID customerId) {
    return this.customerId != null && this.customerId.equals(customerId);
  }

  /** Check if this payment method belongs to the specified tenant. */
  public boolean belongsToTenant(Long tenantId) {
    return this.tenantId != null && this.tenantId.equals(tenantId);
  }

  /** Check if this payment method is active and can be used. */
  public boolean isActive() {
    return this.isValidated;
  }

  /** Validate payment method data for business rules. */
  public static void validatePaymentMethod(PaymentMethod paymentMethod) {
    if (paymentMethod == null) {
      throw new IllegalArgumentException("Payment method cannot be null");
    }

    if (paymentMethod.getCustomerId() == null) {
      throw new IllegalArgumentException("Customer ID cannot be null");
    }

    if (paymentMethod.getTenantId() == null) {
      throw new IllegalArgumentException("Tenant ID cannot be null");
    }

    if (paymentMethod.getType() == null) {
      throw new IllegalArgumentException("Payment method type cannot be null");
    }

    if (paymentMethod.getDetails() == null || paymentMethod.getDetails().trim().isEmpty()) {
      throw new IllegalArgumentException("Payment method details cannot be null or empty");
    }

    // Validate details format based on payment method type
    validateDetailsFormat(paymentMethod.getType(), paymentMethod.getDetails());
  }

  /** Validates payment method details format based on type. */
  private static void validateDetailsFormat(PaymentMethodType type, String details) {
    if (details == null || details.trim().isEmpty()) {
      throw new IllegalArgumentException("Payment method details cannot be empty");
    }

    switch (type) {
      case CREDIT_CARD:
      case DEBIT_CARD:
        if (!isValidCardNumber(details)) {
          throw new IllegalArgumentException("Invalid card number format");
        }
        break;
      case PAYPAL:
        if (!isValidEmail(details)) {
          throw new IllegalArgumentException("PayPal payment method requires valid email");
        }
        break;
      case BANK_TRANSFER:
      case ACH:
        if (!isValidAccountNumber(details)) {
          throw new IllegalArgumentException("Invalid bank account format");
        }
        break;
      case WIRE_TRANSFER:
        if (!isValidWireTransferDetails(details)) {
          throw new IllegalArgumentException("Invalid wire transfer details format");
        }
        break;
      case STRIPE:
        if (!isValidStripeToken(details)) {
          throw new IllegalArgumentException("Invalid Stripe token format");
        }
        break;
    }
  }

  /** Validates card number (basic format check). */
  private static boolean isValidCardNumber(String cardNumber) {
    return cardNumber != null && cardNumber.replaceAll("\\s|-", "").matches("^\\d{13,19}$");
  }

  /** Validates email format. */
  private static boolean isValidEmail(String email) {
    return email != null && email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
  }

  /** Validates bank account number format. */
  private static boolean isValidAccountNumber(String accountNumber) {
    return accountNumber != null && accountNumber.matches("^\\d{6,17}$");
  }

  /** Validates wire transfer details format. */
  private static boolean isValidWireTransferDetails(String details) {
    return details != null && details.length() >= 10; // Basic length check
  }

  /** Validates Stripe token format. */
  private static boolean isValidStripeToken(String token) {
    return token != null && (token.startsWith("tok_") || token.startsWith("pm_"));
  }

  /** Payment method types supported by the system. */
  public enum PaymentMethodType {
    CREDIT_CARD,
    DEBIT_CARD,
    BANK_TRANSFER,
    ACH,
    WIRE_TRANSFER,
    PAYPAL,
    STRIPE
  }
}
