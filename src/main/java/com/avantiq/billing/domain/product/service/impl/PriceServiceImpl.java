package com.avantiq.billing.domain.product.service.impl;

import com.avantiq.billing.domain.product.model.Price;
import com.avantiq.billing.domain.product.service.interfaces.PriceService;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/** Implementation of the PriceService interface. */
@Service
public class PriceServiceImpl implements PriceService {

  private final List<Price> priceRepository = new ArrayList<>(); // Simulated repository

  @Override
  public Price createPrice(Price price) {
    price.setId(UUID.randomUUID());
    price.setCreatedAt(LocalDateTime.now());
    price.setUpdatedAt(LocalDateTime.now());
    price.setVersion(1L);
    priceRepository.add(price);
    return price;
  }

  @Override
  public Price updatePrice(Price price) {
    deletePrice(price.getId().toString());
    price.setUpdatedAt(LocalDateTime.now());
    price.setVersion(price.getVersion() + 1);
    priceRepository.add(price);
    return price;
  }

  @Override
  public void deletePrice(String priceId) {
    priceRepository.removeIf(price -> price.getId().toString().equals(priceId));
  }

  @Override
  public Optional<Price> getPriceById(String priceId) {
    return priceRepository.stream()
        .filter(price -> price.getId().toString().equals(priceId))
        .findFirst();
  }

  @Override
  public List<Price> listAllPrices() {
    return new ArrayList<>(priceRepository);
  }

  @Override
  public List<Price> listPricesByProductId(String productId) {
    UUID productUuid = UUID.fromString(productId);
    return priceRepository.stream()
        .filter(price -> price.getProductId().equals(productUuid))
        .collect(Collectors.toList());
  }

  @Override
  public List<Price> listPricesByPriceBookId(String priceBookId) {
    UUID priceBookUuid = UUID.fromString(priceBookId);
    return priceRepository.stream()
        .filter(price -> price.getPriceBookId().equals(priceBookUuid))
        .collect(Collectors.toList());
  }

  @Override
  public boolean validatePrice(Price price) {
    if (price.getProductId() == null) {
      return false;
    }
    if (price.getPriceBookId() == null) {
      return false;
    }
    if (price.getPricing() == null) {
      return false;
    }
    if (price.getCurrency() == null || price.getCurrency().isEmpty()) {
      return false;
    }
    if (price.getBillingFrequency() == null) {
      return false;
    }
    if (price.getPriceType() == null) {
      return false;
    }
    if (price.getPriceStrategy() == null) {
      return false;
    }
    return true;
  }
}
