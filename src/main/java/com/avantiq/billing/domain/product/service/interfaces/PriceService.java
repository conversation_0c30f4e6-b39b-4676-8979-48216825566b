package com.avantiq.billing.domain.product.service.interfaces;

import com.avantiq.billing.domain.product.model.Price;
import java.util.List;
import java.util.Optional;

/** Domain service interface for price-related business logic. */
public interface PriceService {

  /**
   * Creates a new price.
   *
   * @param price the price to create
   * @return the created price
   */
  Price createPrice(Price price);

  /**
   * Updates an existing price.
   *
   * @param price the price to update
   * @return the updated price
   */
  Price updatePrice(Price price);

  /**
   * Deletes a price by ID.
   *
   * @param priceId the ID of the price to delete
   */
  void deletePrice(String priceId);

  /**
   * Retrieves a price by ID.
   *
   * @param priceId the ID of the price to retrieve
   * @return an Optional containing the price, or empty if not found
   */
  Optional<Price> getPriceById(String priceId);

  /**
   * Lists all prices.
   *
   * @return a list of all prices
   */
  List<Price> listAllPrices();

  /**
   * Lists prices by product ID.
   *
   * @param productId the ID of the product
   * @return a list of prices for the product
   */
  List<Price> listPricesByProductId(String productId);

  /**
   * Lists prices by price book ID.
   *
   * @param priceBookId the ID of the price book
   * @return a list of prices in the price book
   */
  List<Price> listPricesByPriceBookId(String priceBookId);

  /**
   * Validates the price details.
   *
   * @param price the price to validate
   * @return true if the price is valid, false otherwise
   */
  boolean validatePrice(Price price);
}
