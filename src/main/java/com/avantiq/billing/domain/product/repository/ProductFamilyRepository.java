package com.avantiq.billing.domain.product.repository;

import com.avantiq.billing.domain.product.model.ProductFamily;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Repository interface for ProductFamily domain entity. Defines the contract for accessing product
 * family data.
 */
public interface ProductFamilyRepository {

  /**
   * Find a product family by ID.
   *
   * @param familyId the product family ID
   * @return an Optional containing the product family if found
   */
  Optional<ProductFamily> findById(UUID familyId);

  /**
   * Find a product family by ID with tenant isolation.
   *
   * @param familyId the product family ID
   * @param tenantId the tenant ID
   * @return an Optional containing the product family if found
   */
  Optional<ProductFamily> findByIdAndTenantId(UUID familyId, Long tenantId);

  /**
   * Find product families by name.
   *
   * @param name the product family name
   * @return list of product families with matching name
   */
  List<ProductFamily> findByName(String name);

  /**
   * Find active product families with tenant isolation.
   *
   * @param tenantId the tenant ID
   * @return list of active product families
   */
  List<ProductFamily> findActiveByTenantId(Long tenantId);

  /**
   * Find all product families with pagination and tenant isolation.
   *
   * @param tenantId the tenant ID
   * @param pageable pagination information
   * @return a page of product families
   */
  Page<ProductFamily> findAllByTenantId(Long tenantId, Pageable pageable);

  /**
   * Save a product family.
   *
   * @param productFamily the product family to save
   * @return the saved product family
   */
  ProductFamily save(ProductFamily productFamily);

  /**
   * Delete a product family.
   *
   * @param productFamily the product family to delete
   */
  void delete(ProductFamily productFamily);

  /**
   * Check if a product family exists by ID and tenant.
   *
   * @param familyId the product family ID
   * @param tenantId the tenant ID
   * @return true if exists, false otherwise
   */
  boolean existsByIdAndTenantId(UUID familyId, Long tenantId);
}
