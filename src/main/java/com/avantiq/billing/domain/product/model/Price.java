package com.avantiq.billing.domain.product.model;

import com.avantiq.billing.domain.product.valueobject.Pricing;
import com.avantiq.billing.domain.product.valueobject.pricing.RampPricing;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/** Represents a price in the domain. */
public class Price {

  private UUID id;
  private UUID productId;
  private UUID priceBookId;
  private BillingFrequency billingFrequency;
  private String currency;
  private String unitOfMeasure;
  private BillingStrategy billingStrategy;
  private ProrationPolicy prorationPolicy;
  private boolean isDefault;
  private boolean isGrandfathered;
  private long version;
  private LocalDateTime createdAt;
  private LocalDateTime updatedAt;
  private PriceType priceType;
  private PriceStrategy priceStrategy;
  private Pricing pricing;

  public Price(
      UUID id,
      UUID productId,
      UUID priceBookId,
      BillingFrequency billingFrequency,
      String currency,
      String unitOfMeasure,
      PriceType priceType,
      PriceStrategy priceStrategy,
      BillingStrategy billingStrategy,
      Pricing pricing,
      ProrationPolicy prorationPolicy,
      boolean isDefault,
      boolean isGrandfathered,
      long version,
      LocalDateTime createdAt,
      LocalDateTime updatedAt) {
    this.id = id;
    this.productId = productId;
    this.priceBookId = priceBookId;
    this.billingFrequency = billingFrequency;
    this.currency = currency;
    this.unitOfMeasure = unitOfMeasure;
    this.priceType = priceType;
    this.priceStrategy = priceStrategy;
    this.billingStrategy = billingStrategy;
    this.pricing = pricing;
    this.prorationPolicy = prorationPolicy;
    this.isDefault = isDefault;
    this.isGrandfathered = isGrandfathered;
    this.version = version;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }

  public UUID getId() {
    return id;
  }

  public void setId(UUID id) {
    this.id = id;
  }

  public UUID getProductId() {
    return productId;
  }

  public void setProductId(UUID productId) {
    this.productId = productId;
  }

  public UUID getPriceBookId() {
    return priceBookId;
  }

  public void setPriceBookId(UUID priceBookId) {
    this.priceBookId = priceBookId;
  }

  public BillingFrequency getBillingFrequency() {
    return billingFrequency;
  }

  public void setBillingFrequency(BillingFrequency billingFrequency) {
    this.billingFrequency = billingFrequency;
  }

  public String getCurrency() {
    return currency;
  }

  public void setCurrency(String currency) {
    this.currency = currency;
  }

  public String getUnitOfMeasure() {
    return unitOfMeasure;
  }

  public void setUnitOfMeasure(String unitOfMeasure) {
    this.unitOfMeasure = unitOfMeasure;
  }

  public BillingStrategy getBillingStrategy() {
    return billingStrategy;
  }

  public void setBillingStrategy(BillingStrategy billingStrategy) {
    this.billingStrategy = billingStrategy;
  }

  public ProrationPolicy getProrationPolicy() {
    return prorationPolicy;
  }

  public void setProrationPolicy(ProrationPolicy prorationPolicy) {
    this.prorationPolicy = prorationPolicy;
  }

  public boolean isDefault() {
    return isDefault;
  }

  public void setDefault(boolean isDefault) {
    this.isDefault = isDefault;
  }

  public boolean isGrandfathered() {
    return isGrandfathered;
  }

  public void setGrandfathered(boolean isGrandfathered) {
    this.isGrandfathered = isGrandfathered;
  }

  public long getVersion() {
    return version;
  }

  public void setVersion(long version) {
    this.version = version;
  }

  public LocalDateTime getCreatedAt() {
    return createdAt;
  }

  public void setCreatedAt(LocalDateTime createdAt) {
    this.createdAt = createdAt;
  }

  public LocalDateTime getUpdatedAt() {
    return updatedAt;
  }

  public void setUpdatedAt(LocalDateTime updatedAt) {
    this.updatedAt = updatedAt;
  }

  public PriceType getPriceType() {
    return priceType;
  }

  public void setPriceType(PriceType priceType) {
    this.priceType = priceType;
  }

  public PriceStrategy getPriceStrategy() {
    return priceStrategy;
  }

  public void setPriceStrategy(PriceStrategy priceStrategy) {
    this.priceStrategy = priceStrategy;
  }

  public Pricing getPricing() {
    return pricing;
  }

  public void setPricing(Pricing pricing) {
    if (pricing == null) {
      throw new IllegalArgumentException("Pricing cannot be null");
    }
    if (this.priceStrategy != null && pricing.getStrategy() != this.priceStrategy) {
      throw new IllegalArgumentException(
          String.format(
              "Pricing strategy mismatch. Expected: %s, Got: %s",
              this.priceStrategy, pricing.getStrategy()));
    }
    pricing.validate(); // Validate the pricing configuration
    this.pricing = pricing;
  }

  // Pricing calculation methods

  /**
   * Calculates the price for a given quantity.
   *
   * @param quantity the quantity to price
   * @return the calculated amount
   * @throws IllegalStateException if no pricing configuration is set
   */
  public BigDecimal calculatePrice(BigDecimal quantity) {
    if (pricing == null) {
      throw new IllegalStateException("No pricing configuration set");
    }
    return pricing.calculateAmount(quantity);
  }

  /**
   * Calculates the price for a given quantity and billing period. Used for ramp pricing that
   * changes over time.
   *
   * @param quantity the quantity to price
   * @param billingPeriod the current billing period (for ramp pricing)
   * @return the calculated amount
   */
  public BigDecimal calculatePrice(BigDecimal quantity, Integer billingPeriod) {
    if (pricing == null) {
      throw new IllegalStateException("No pricing configuration set");
    }

    if (pricing instanceof RampPricing) {
      return ((RampPricing) pricing).calculateAmount(quantity, billingPeriod);
    }

    // For non-ramp pricing, ignore the billing period
    return calculatePrice(quantity);
  }

  @Override
  public String toString() {
    return "Price{"
        + "id="
        + id
        + ", productId="
        + productId
        + ", priceBookId="
        + priceBookId
        + ", billingFrequency="
        + billingFrequency
        + ", currency='"
        + currency
        + '\''
        + ", unitOfMeasure='"
        + unitOfMeasure
        + '\''
        + ", priceType="
        + priceType
        + ", priceStrategy="
        + priceStrategy
        + ", billingStrategy="
        + billingStrategy
        + ", pricing="
        + pricing
        + ", prorationPolicy="
        + prorationPolicy
        + ", isDefault="
        + isDefault
        + ", isGrandfathered="
        + isGrandfathered
        + ", version="
        + version
        + ", createdAt="
        + createdAt
        + ", updatedAt="
        + updatedAt
        + '}';
  }

  public enum BillingFrequency {
    MONTHLY,
    SEMI_ANNUALLY,
    ANNUALLY,
    ON_EVENT
  }

  public enum PriceType {
    RECURRING,
    USAGE,
    ONE_TIME,
    COMMITMENT
  }

  public enum PriceStrategy {
    FLAT,
    TIERED,
    VOLUME,
    OVERAGE,
    RAMP,
    PER_UNIT
  }

  public enum BillingStrategy {
    PREPAID,
    POSTPAID
  }

  public enum ProrationPolicy {
    FULL,
    PARTIAL,
    NONE
  }
}
