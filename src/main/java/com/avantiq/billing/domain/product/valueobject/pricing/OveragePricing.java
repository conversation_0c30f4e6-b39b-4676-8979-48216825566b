package com.avantiq.billing.domain.product.valueobject.pricing;

import com.avantiq.billing.domain.product.model.Price;
import com.avantiq.billing.domain.product.valueobject.Pricing;
import com.avantiq.billing.domain.product.valueobject.PricingSummary;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeName;
import java.math.BigDecimal;
import lombok.Builder;
import lombok.NonNull;
import lombok.Value;

/** Overage pricing with a base amount for included quantity and per-unit pricing for overage. */
@Value
@Builder
@JsonTypeName("OVERAGE")
public class OveragePricing implements Pricing {

  @NonNull BigDecimal includedQuantity;
  @NonNull BigDecimal basePrice;
  @NonNull BigDecimal overageUnitPrice;

  @JsonCreator
  public OveragePricing(
      @JsonProperty("includedQuantity") BigDecimal includedQuantity,
      @JsonProperty("basePrice") BigDecimal basePrice,
      @JsonProperty("overageUnitPrice") BigDecimal overageUnitPrice) {
    this.includedQuantity = includedQuantity;
    this.basePrice = basePrice;
    this.overageUnitPrice = overageUnitPrice;
  }

  @Override
  @JsonIgnore
  public Price.PriceStrategy getStrategy() {
    return Price.PriceStrategy.OVERAGE;
  }

  @Override
  public BigDecimal calculateAmount(BigDecimal quantity) {
    if (quantity == null || quantity.compareTo(BigDecimal.ZERO) < 0) {
      throw new IllegalArgumentException("Quantity must be non-negative");
    }

    if (quantity.compareTo(includedQuantity) <= 0) {
      return basePrice;
    }

    BigDecimal overageQuantity = quantity.subtract(includedQuantity);
    BigDecimal overageAmount = overageQuantity.multiply(overageUnitPrice);
    return basePrice.add(overageAmount);
  }

  @Override
  public void validate() {
    if (includedQuantity.compareTo(BigDecimal.ZERO) < 0) {
      throw new IllegalArgumentException("Included quantity must be non-negative");
    }
    if (basePrice.compareTo(BigDecimal.ZERO) < 0) {
      throw new IllegalArgumentException("Base price must be non-negative");
    }
    if (overageUnitPrice.compareTo(BigDecimal.ZERO) < 0) {
      throw new IllegalArgumentException("Overage unit price must be non-negative");
    }
  }

  @Override
  public PricingSummary summarize() {
    return PricingSummary.builder()
        .description("$" + basePrice + " for up to " + includedQuantity + " units")
        .formula("Base + overage × $" + overageUnitPrice)
        .details("Additional units: $" + overageUnitPrice + " each")
        .build();
  }
}
