package com.avantiq.billing.domain.product.service.interfaces;

import com.avantiq.billing.domain.product.model.Bundle;
import com.avantiq.billing.domain.product.model.BundleProduct;
import java.util.List;
import java.util.Optional;

/** Domain service interface for bundle-related business logic. */
public interface BundleService {

  /**
   * Creates a new bundle.
   *
   * @param bundle the bundle to create
   * @return the created bundle
   */
  Bundle createBundle(Bundle bundle);

  /**
   * Updates an existing bundle.
   *
   * @param bundle the bundle to update
   * @return the updated bundle
   */
  Bundle updateBundle(Bundle bundle);

  /**
   * Deletes a bundle by ID.
   *
   * @param bundleId the ID of the bundle to delete
   */
  void deleteBundle(String bundleId);

  /**
   * Retrieves a bundle by ID.
   *
   * @param bundleId the ID of the bundle to retrieve
   * @return an Optional containing the bundle, or empty if not found
   */
  Optional<Bundle> getBundleById(String bundleId);

  /**
   * Lists all bundles.
   *
   * @return a list of all bundles
   */
  List<Bundle> listAllBundles();

  /**
   * Adds a product to a bundle.
   *
   * @param bundleId the ID of the bundle
   * @param productId the ID of the product to add
   * @param quantity the quantity of the product
   * @param optionalFlag whether the product is optional
   * @return the created bundle product
   */
  BundleProduct addProductToBundle(
      String bundleId, String productId, int quantity, boolean optionalFlag);

  /**
   * Removes a product from a bundle.
   *
   * @param bundleProductId the ID of the bundle product to remove
   */
  void removeProductFromBundle(String bundleProductId);

  /**
   * Validates the bundle details.
   *
   * @param bundle the bundle to validate
   * @return true if the bundle is valid, false otherwise
   */
  boolean validateBundle(Bundle bundle);
}
