package com.avantiq.billing.domain.product.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.UUID;

/** Represents a product in the domain. */
public class Product {

  private UUID id;
  private String name;
  private String description;
  private BigDecimal price;
  private UUID productFamilyId;
  private String sku;
  private String taxCode;
  private String glCode;
  private String status;
  private String visibility;
  private LocalDateTime createdAt;
  private LocalDateTime updatedAt;
  private Long tenantId;

  // Define valid status values
  public static final String STATUS_ACTIVE = "ACTIVE";
  public static final String STATUS_INACTIVE = "INACTIVE";
  public static final String STATUS_DRAFT = "DRAFT";
  public static final String STATUS_DEPRECATED = "DEPRECATED";

  // Define valid visibility values
  public static final String VISIBILITY_PUBLIC = "PUBLIC";
  public static final String VISIBILITY_PRIVATE = "PRIVATE";
  public static final String VISIBILITY_INTERNAL = "INTERNAL";

  public Product() {
    // Default constructor
  }

  public Product(UUID id, String name, String description, BigDecimal price) {
    this.id = id;
    this.name = name;
    this.description = description;
    this.price = price;
  }

  public Product(
      UUID id,
      String name,
      String description,
      BigDecimal price,
      UUID productFamilyId,
      String sku,
      String taxCode,
      String glCode,
      String status,
      String visibility,
      LocalDateTime createdAt,
      LocalDateTime updatedAt,
      Long tenantId) {
    this.id = id;
    this.name = name;
    this.description = description;
    this.price = price;
    this.productFamilyId = productFamilyId;
    this.sku = sku;
    this.taxCode = taxCode;
    this.glCode = glCode;
    this.status = status;
    this.visibility = visibility;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
    this.tenantId = tenantId;
  }

  public UUID getId() {
    return id;
  }

  public void setId(UUID id) {
    this.id = id;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public BigDecimal getPrice() {
    return price;
  }

  public void setPrice(BigDecimal price) {
    this.price = price;
  }

  public UUID getProductFamilyId() {
    return productFamilyId;
  }

  public void setProductFamilyId(UUID productFamilyId) {
    this.productFamilyId = productFamilyId;
  }

  public String getSku() {
    return sku;
  }

  public void setSku(String sku) {
    this.sku = sku;
  }

  public String getTaxCode() {
    return taxCode;
  }

  public void setTaxCode(String taxCode) {
    this.taxCode = taxCode;
  }

  public String getGlCode() {
    return glCode;
  }

  public void setGlCode(String glCode) {
    this.glCode = glCode;
  }

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public String getVisibility() {
    return visibility;
  }

  public void setVisibility(String visibility) {
    this.visibility = visibility;
  }

  public LocalDateTime getCreatedAt() {
    return createdAt;
  }

  public void setCreatedAt(LocalDateTime createdAt) {
    this.createdAt = createdAt;
  }

  public LocalDateTime getUpdatedAt() {
    return updatedAt;
  }

  public void setUpdatedAt(LocalDateTime updatedAt) {
    this.updatedAt = updatedAt;
  }

  public Long getTenantId() {
    return tenantId;
  }

  public void setTenantId(Long tenantId) {
    this.tenantId = tenantId;
  }

  /**
   * Validates that the product data is valid according to business rules.
   *
   * @throws IllegalArgumentException if validation fails
   */
  public void validate() {
    if (name == null || name.trim().isEmpty()) {
      throw new IllegalArgumentException("Product name cannot be empty");
    }

    if (price != null && price.compareTo(BigDecimal.ZERO) < 0) {
      throw new IllegalArgumentException("Product price cannot be negative");
    }

    if (status != null && !isValidStatus(status)) {
      throw new IllegalArgumentException("Invalid product status: " + status);
    }

    if (visibility != null && !isValidVisibility(visibility)) {
      throw new IllegalArgumentException("Invalid product visibility: " + visibility);
    }
  }

  /**
   * Checks if the given status is valid.
   *
   * @param status the status to check
   * @return true if valid, false otherwise
   */
  private boolean isValidStatus(String status) {
    return STATUS_ACTIVE.equals(status)
        || STATUS_INACTIVE.equals(status)
        || STATUS_DRAFT.equals(status)
        || STATUS_DEPRECATED.equals(status);
  }

  /**
   * Checks if the given visibility is valid.
   *
   * @param visibility the visibility to check
   * @return true if valid, false otherwise
   */
  private boolean isValidVisibility(String visibility) {
    return VISIBILITY_PUBLIC.equals(visibility)
        || VISIBILITY_PRIVATE.equals(visibility)
        || VISIBILITY_INTERNAL.equals(visibility);
  }

  /** Activates the product. */
  public void activate() {
    this.status = STATUS_ACTIVE;
    this.updatedAt = LocalDateTime.now();
  }

  /** Deactivates the product. */
  public void deactivate() {
    this.status = STATUS_INACTIVE;
    this.updatedAt = LocalDateTime.now();
  }

  /** Marks the product as deprecated. */
  public void deprecate() {
    this.status = STATUS_DEPRECATED;
    this.updatedAt = LocalDateTime.now();
  }

  /**
   * Updates the product details.
   *
   * @param name the new name
   * @param description the new description
   * @param price the new price
   * @param sku the new SKU
   */
  public void updateDetails(String name, String description, BigDecimal price, String sku) {
    this.name = name;
    this.description = description;
    this.price = price;
    this.sku = sku;
    this.updatedAt = LocalDateTime.now();
    validate();
  }

  /**
   * Assigns the product to a product family.
   *
   * @param productFamilyId the ID of the product family
   */
  public void assignToFamily(UUID productFamilyId) {
    this.productFamilyId = productFamilyId;
    this.updatedAt = LocalDateTime.now();
  }

  /**
   * Checks if the product is active.
   *
   * @return true if active, false otherwise
   */
  public boolean isActive() {
    return STATUS_ACTIVE.equals(this.status);
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    Product product = (Product) o;
    return Objects.equals(id, product.id);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id);
  }

  @Override
  public String toString() {
    return "Product{"
        + "id="
        + id
        + ", name='"
        + name
        + '\''
        + ", description='"
        + description
        + '\''
        + ", price="
        + price
        + ", productFamilyId="
        + productFamilyId
        + ", sku='"
        + sku
        + '\''
        + ", taxCode='"
        + taxCode
        + '\''
        + ", glCode='"
        + glCode
        + '\''
        + ", status='"
        + status
        + '\''
        + ", visibility='"
        + visibility
        + '\''
        + ", createdAt="
        + createdAt
        + ", updatedAt="
        + updatedAt
        + ", tenantId="
        + tenantId
        + '}';
  }
}
