package com.avantiq.billing.domain.product.valueobject;

import com.avantiq.billing.domain.product.model.Price;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import java.math.BigDecimal;

/** Base interface for all pricing strategies. Implementations should be immutable value objects. */
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type")
@JsonSubTypes({
  @JsonSubTypes.Type(
      value = com.avantiq.billing.domain.product.valueobject.pricing.FlatPricing.class,
      name = "FLAT"),
  @JsonSubTypes.Type(
      value = com.avantiq.billing.domain.product.valueobject.pricing.TieredPricing.class,
      name = "TIERED"),
  @JsonSubTypes.Type(
      value = com.avantiq.billing.domain.product.valueobject.pricing.VolumePricing.class,
      name = "VOLUME"),
  @JsonSubTypes.Type(
      value = com.avantiq.billing.domain.product.valueobject.pricing.OveragePricing.class,
      name = "OVERA<PERSON>"),
  @JsonSubTypes.Type(
      value = com.avantiq.billing.domain.product.valueobject.pricing.RampPricing.class,
      name = "RAMP"),
  @JsonSubTypes.Type(
      value = com.avantiq.billing.domain.product.valueobject.pricing.PerUnitPricing.class,
      name = "PER_UNIT")
})
public interface Pricing {

  /**
   * Gets the pricing strategy type.
   *
   * @return the pricing strategy
   */
  Price.PriceStrategy getStrategy();

  /**
   * Calculates the total amount based on the given quantity.
   *
   * @param quantity the quantity to price
   * @return the calculated amount
   */
  BigDecimal calculateAmount(BigDecimal quantity);

  /**
   * Validates the pricing configuration.
   *
   * @throws IllegalArgumentException if the configuration is invalid
   */
  void validate();

  /**
   * Provides a human-readable summary of the pricing.
   *
   * @return pricing summary for UI display
   */
  PricingSummary summarize();
}
