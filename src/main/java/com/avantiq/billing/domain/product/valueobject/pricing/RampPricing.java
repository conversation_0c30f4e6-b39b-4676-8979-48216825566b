package com.avantiq.billing.domain.product.valueobject.pricing;

import com.avantiq.billing.domain.product.model.Price;
import com.avantiq.billing.domain.product.valueobject.Pricing;
import com.avantiq.billing.domain.product.valueobject.PricingSummary;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeName;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Builder;
import lombok.NonNull;
import lombok.Value;

/**
 * Ramp pricing where price changes over time periods. Wraps another pricing strategy and applies
 * time-based discounts.
 */
@Value
@Builder
@JsonTypeName("RAMP")
public class RampPricing implements Pricing {

  @NonNull Pricing basePricing; // Can be any other pricing type
  @NonNull List<RampPeriod> periods;

  @JsonCreator
  public RampPricing(
      @JsonProperty("basePricing") Pricing basePricing,
      @JsonProperty("periods") List<RampPeriod> periods) {
    this.basePricing = basePricing;
    this.periods = periods;
  }

  @Value
  @Builder
  public static class RampPeriod {
    @NonNull Integer periodNumber;
    Integer durationMonths; // null = forever
    @NonNull BigDecimal discountPercent; // 0-100
    String label; // Optional, e.g., "Introductory offer"

    @JsonCreator
    public RampPeriod(
        @JsonProperty("periodNumber") Integer periodNumber,
        @JsonProperty("durationMonths") Integer durationMonths,
        @JsonProperty("discountPercent") BigDecimal discountPercent,
        @JsonProperty("label") String label) {
      this.periodNumber = periodNumber;
      this.durationMonths = durationMonths;
      this.discountPercent = discountPercent;
      this.label = label;
    }
  }

  @Override
  @JsonIgnore
  public Price.PriceStrategy getStrategy() {
    return Price.PriceStrategy.RAMP;
  }

  @Override
  public BigDecimal calculateAmount(BigDecimal quantity) {
    // Default to first period
    return calculateAmount(quantity, 1);
  }

  public BigDecimal calculateAmount(BigDecimal quantity, Integer currentPeriod) {
    if (quantity == null || quantity.compareTo(BigDecimal.ZERO) < 0) {
      throw new IllegalArgumentException("Quantity must be non-negative");
    }
    if (currentPeriod == null || currentPeriod < 1) {
      throw new IllegalArgumentException("Period must be positive");
    }

    BigDecimal baseAmount = basePricing.calculateAmount(quantity);
    RampPeriod period = findPeriod(currentPeriod);

    if (period.discountPercent.compareTo(BigDecimal.ZERO) == 0) {
      return baseAmount;
    }

    BigDecimal discount =
        baseAmount
            .multiply(period.discountPercent)
            .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);

    return baseAmount.subtract(discount);
  }

  private RampPeriod findPeriod(Integer periodNumber) {
    return periods.stream()
        .filter(p -> p.periodNumber.equals(periodNumber))
        .findFirst()
        .orElseGet(() -> periods.get(periods.size() - 1)); // Default to last period
  }

  @Override
  public void validate() {
    if (basePricing == null) {
      throw new IllegalArgumentException("Base pricing cannot be null");
    }
    basePricing.validate();

    if (periods == null || periods.isEmpty()) {
      throw new IllegalArgumentException("Ramp periods cannot be empty");
    }

    // Validate each period
    for (RampPeriod period : periods) {
      if (period.periodNumber < 1) {
        throw new IllegalArgumentException("Period number must be positive");
      }
      if (period.durationMonths != null && period.durationMonths < 1) {
        throw new IllegalArgumentException("Duration months must be positive");
      }
      if (period.discountPercent.compareTo(BigDecimal.ZERO) < 0
          || period.discountPercent.compareTo(new BigDecimal("100")) > 0) {
        throw new IllegalArgumentException("Discount percent must be between 0 and 100");
      }
    }

    // Validate period sequence
    for (int i = 0; i < periods.size(); i++) {
      if (periods.get(i).periodNumber != i + 1) {
        throw new IllegalArgumentException("Period numbers must be sequential starting from 1");
      }
    }

    // Only last period can have null duration
    for (int i = 0; i < periods.size() - 1; i++) {
      if (periods.get(i).durationMonths == null) {
        throw new IllegalArgumentException("Only the last period can have unlimited duration");
      }
    }
  }

  @Override
  public PricingSummary summarize() {
    String periodDescriptions =
        periods.stream()
            .map(
                period -> {
                  String duration =
                      period.durationMonths == null ? "ongoing" : period.durationMonths + " months";
                  String label = period.label != null ? " - " + period.label : "";
                  return "Period "
                      + period.periodNumber
                      + " ("
                      + duration
                      + "): "
                      + period.discountPercent
                      + "% discount"
                      + label;
                })
            .collect(Collectors.joining(", "));

    return PricingSummary.builder()
        .description("Time-based pricing with " + periods.size() + " periods")
        .formula("Base price with period discounts")
        .details(periodDescriptions)
        .build();
  }
}
