package com.avantiq.billing.domain.product.service.interfaces;

import com.avantiq.billing.domain.product.model.Product;
import java.util.List;
import java.util.Optional;

/** Domain service interface for product-related business logic. */
public interface ProductDomainService {

  /**
   * Validates the product details.
   *
   * @param product the product to validate
   * @return true if the product is valid, false otherwise
   */
  boolean validateProduct(Product product);

  /**
   * Creates a new product.
   *
   * @param product the product to create
   * @return the created product
   */
  Product createProduct(Product product);

  /**
   * Updates an existing product.
   *
   * @param product the product to update
   * @return the updated product
   */
  Product updateProduct(Product product);

  /**
   * Deletes a product by ID.
   *
   * @param productId the ID of the product to delete
   */
  void deleteProduct(String productId);

  /**
   * Retrieves a product by ID.
   *
   * @param productId the ID of the product to retrieve
   * @return an Optional containing the product, or empty if not found
   */
  Optional<Product> getProductById(String productId);

  /**
   * Lists all products.
   *
   * @return a list of all products
   */
  List<Product> listAllProducts();
}
