package com.avantiq.billing.domain.product.service.impl;

import com.avantiq.billing.domain.product.model.PriceBook;
import com.avantiq.billing.domain.product.service.interfaces.PriceBookService;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.stereotype.Service;

/** Implementation of the PriceBookService interface. */
@Service
public class PriceBookServiceImpl implements PriceBookService {

  private final List<PriceBook> priceBookRepository = new ArrayList<>(); // Simulated repository

  @Override
  public PriceBook createPriceBook(PriceBook priceBook) {
    priceBook.setId(UUID.randomUUID());
    priceBookRepository.add(priceBook);
    return priceBook;
  }

  @Override
  public PriceBook updatePriceBook(PriceBook priceBook) {
    deletePriceBook(priceBook.getId().toString());
    priceBookRepository.add(priceBook);
    return priceBook;
  }

  @Override
  public void deletePriceBook(String priceBookId) {
    priceBookRepository.removeIf(priceBook -> priceBook.getId().toString().equals(priceBookId));
  }

  @Override
  public Optional<PriceBook> getPriceBookById(String priceBookId) {
    return priceBookRepository.stream()
        .filter(priceBook -> priceBook.getId().toString().equals(priceBookId))
        .findFirst();
  }

  @Override
  public List<PriceBook> listAllPriceBooks() {
    return new ArrayList<>(priceBookRepository);
  }

  @Override
  public boolean validatePriceBook(PriceBook priceBook) {
    if (priceBook.getName() == null || priceBook.getName().isEmpty()) {
      return false;
    }
    if (priceBook.getCurrency() == null || priceBook.getCurrency().isEmpty()) {
      return false;
    }
    return true;
  }
}
