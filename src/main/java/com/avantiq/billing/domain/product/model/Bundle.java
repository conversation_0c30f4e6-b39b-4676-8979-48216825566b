package com.avantiq.billing.domain.product.model;

import java.time.LocalDateTime;
import java.util.UUID;

/** Represents a product bundle in the domain. */
public class Bundle {

  private UUID bundleId;
  private String name;
  private String description;
  private String status;
  private String pricingStrategy;
  private long version;
  private LocalDateTime createdAt;
  private LocalDateTime updatedAt;

  public Bundle(
      UUID bundleId,
      String name,
      String description,
      String status,
      String pricingStrategy,
      long version,
      LocalDateTime createdAt,
      LocalDateTime updatedAt) {
    this.bundleId = bundleId;
    this.name = name;
    this.description = description;
    this.status = status;
    this.pricingStrategy = pricingStrategy;
    this.version = version;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }

  public UUID getBundleId() {
    return bundleId;
  }

  public void setBundleId(UUID bundleId) {
    this.bundleId = bundleId;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public String getPricingStrategy() {
    return pricingStrategy;
  }

  public void setPricingStrategy(String pricingStrategy) {
    this.pricingStrategy = pricingStrategy;
  }

  public long getVersion() {
    return version;
  }

  public void setVersion(long version) {
    this.version = version;
  }

  public LocalDateTime getCreatedAt() {
    return createdAt;
  }

  public void setCreatedAt(LocalDateTime createdAt) {
    this.createdAt = createdAt;
  }

  public LocalDateTime getUpdatedAt() {
    return updatedAt;
  }

  public void setUpdatedAt(LocalDateTime updatedAt) {
    this.updatedAt = updatedAt;
  }

  @Override
  public String toString() {
    return "Bundle{"
        + "bundleId="
        + bundleId
        + ", name='"
        + name
        + '\''
        + ", description='"
        + description
        + '\''
        + ", status='"
        + status
        + '\''
        + ", pricingStrategy='"
        + pricingStrategy
        + '\''
        + ", version="
        + version
        + ", createdAt="
        + createdAt
        + ", updatedAt="
        + updatedAt
        + '}';
  }
}
