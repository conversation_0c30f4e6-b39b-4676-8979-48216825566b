package com.avantiq.billing.domain.product.repository;

import com.avantiq.billing.domain.product.model.PriceBook;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Repository interface for PriceBook domain entity. Defines the contract for accessing price book
 * data.
 */
public interface PriceBookRepository {

  /**
   * Find a price book by ID.
   *
   * @param priceBookId the price book ID
   * @return an Optional containing the price book if found
   */
  Optional<PriceBook> findById(UUID priceBookId);

  /**
   * Find a price book by ID with tenant isolation.
   *
   * @param priceBookId the price book ID
   * @param tenantId the tenant ID
   * @return an Optional containing the price book if found
   */
  Optional<PriceBook> findByIdAndTenantId(UUID priceBookId, Long tenantId);

  /**
   * Find price books by name.
   *
   * @param name the price book name
   * @return list of price books with matching name
   */
  List<PriceBook> findByName(String name);

  /**
   * Find active price books with tenant isolation.
   *
   * @param tenantId the tenant ID
   * @return list of active price books
   */
  List<PriceBook> findActiveByTenantId(Long tenantId);

  /**
   * Find price books by currency and tenant.
   *
   * @param currency the currency code
   * @param tenantId the tenant ID
   * @return list of price books for the currency
   */
  List<PriceBook> findByCurrencyAndTenantId(String currency, Long tenantId);

  /**
   * Find all price books with pagination and tenant isolation.
   *
   * @param tenantId the tenant ID
   * @param pageable pagination information
   * @return a page of price books
   */
  Page<PriceBook> findAllByTenantId(Long tenantId, Pageable pageable);

  /**
   * Save a price book.
   *
   * @param priceBook the price book to save
   * @return the saved price book
   */
  PriceBook save(PriceBook priceBook);

  /**
   * Delete a price book.
   *
   * @param priceBook the price book to delete
   */
  void delete(PriceBook priceBook);

  /**
   * Check if a price book exists by ID and tenant.
   *
   * @param priceBookId the price book ID
   * @param tenantId the tenant ID
   * @return true if exists, false otherwise
   */
  boolean existsByIdAndTenantId(UUID priceBookId, Long tenantId);
}
