package com.avantiq.billing.domain.product.model;

import java.util.UUID;

/** Represents a product within a bundle in the domain. */
public class BundleProduct {

  private UUID id;
  private UUID bundleId;
  private UUID productId;
  private int quantity;
  private boolean optionalFlag;

  public BundleProduct(UUID id, UUID bundleId, UUID productId, int quantity, boolean optionalFlag) {
    this.id = id;
    this.bundleId = bundleId;
    this.productId = productId;
    this.quantity = quantity;
    this.optionalFlag = optionalFlag;
  }

  public UUID getId() {
    return id;
  }

  public void setId(UUID id) {
    this.id = id;
  }

  public UUID getBundleId() {
    return bundleId;
  }

  public void setBundleId(UUID bundleId) {
    this.bundleId = bundleId;
  }

  public UUID getProductId() {
    return productId;
  }

  public void setProductId(UUID productId) {
    this.productId = productId;
  }

  public int getQuantity() {
    return quantity;
  }

  public void setQuantity(int quantity) {
    this.quantity = quantity;
  }

  public boolean isOptionalFlag() {
    return optionalFlag;
  }

  public void setOptionalFlag(boolean optionalFlag) {
    this.optionalFlag = optionalFlag;
  }

  @Override
  public String toString() {
    return "BundleProduct{"
        + "id="
        + id
        + ", bundleId="
        + bundleId
        + ", productId="
        + productId
        + ", quantity="
        + quantity
        + ", optionalFlag="
        + optionalFlag
        + '}';
  }
}
