package com.avantiq.billing.domain.product.exception;

import com.avantiq.billing.domain.common.exception.DomainException;
import com.avantiq.billing.domain.common.exception.ErrorCode;

/** Exception thrown when product data is invalid or doesn't meet business rules. */
public class InvalidProductDataException extends DomainException {

  public InvalidProductDataException(String message) {
    super(message, ErrorCode.INVALID_PRODUCT_DATA);
  }

  public InvalidProductDataException(String message, Throwable cause) {
    super(message, cause, ErrorCode.INVALID_PRODUCT_DATA);
  }
}
