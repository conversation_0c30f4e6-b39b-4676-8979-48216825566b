package com.avantiq.billing.domain.product.service.impl;

import com.avantiq.billing.domain.product.model.ProductFamily;
import com.avantiq.billing.domain.product.service.interfaces.ProductFamilyService;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.stereotype.Service;

/** Implementation of the ProductFamilyService interface. */
@Service
public class ProductFamilyServiceImpl implements ProductFamilyService {

  private final List<ProductFamily> productFamilyRepository =
      new ArrayList<>(); // Simulated repository

  @Override
  public ProductFamily createProductFamily(ProductFamily productFamily) {
    productFamily.setId(UUID.randomUUID());
    productFamily.setCreatedAt(LocalDateTime.now());
    productFamily.setUpdatedAt(LocalDateTime.now());
    productFamilyRepository.add(productFamily);
    return productFamily;
  }

  @Override
  public ProductFamily updateProductFamily(ProductFamily productFamily) {
    deleteProductFamily(productFamily.getId().toString());
    productFamily.setUpdatedAt(LocalDateTime.now());
    productFamilyRepository.add(productFamily);
    return productFamily;
  }

  @Override
  public void deleteProductFamily(String productFamilyId) {
    productFamilyRepository.removeIf(
        productFamily -> productFamily.getId().toString().equals(productFamilyId));
  }

  @Override
  public Optional<ProductFamily> getProductFamilyById(String productFamilyId) {
    return productFamilyRepository.stream()
        .filter(productFamily -> productFamily.getId().toString().equals(productFamilyId))
        .findFirst();
  }

  @Override
  public List<ProductFamily> listAllProductFamilies() {
    return new ArrayList<>(productFamilyRepository);
  }

  @Override
  public boolean validateProductFamily(ProductFamily productFamily) {
    if (productFamily.getName() == null || productFamily.getName().isEmpty()) {
      return false;
    }
    return true;
  }
}
