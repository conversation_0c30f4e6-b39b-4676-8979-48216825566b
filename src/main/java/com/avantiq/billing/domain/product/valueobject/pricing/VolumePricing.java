package com.avantiq.billing.domain.product.valueobject.pricing;

import com.avantiq.billing.domain.product.model.Price;
import com.avantiq.billing.domain.product.valueobject.Pricing;
import com.avantiq.billing.domain.product.valueobject.PricingSummary;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeName;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Builder;
import lombok.NonNull;
import lombok.Value;

/**
 * Volume-based pricing where all units are charged at the rate of the applicable bracket. Unlike
 * tiered pricing, all units get the same rate based on total volume.
 */
@Value
@Builder
@JsonTypeName("VOLUME")
public class VolumePricing implements Pricing {

  @NonNull List<VolumeBracket> brackets;

  @JsonCreator
  public VolumePricing(@JsonProperty("brackets") List<VolumeBracket> brackets) {
    this.brackets = brackets;
  }

  @Value
  @Builder
  public static class VolumeBracket {
    @NonNull BigDecimal minQuantity;
    BigDecimal maxQuantity; // null = infinity
    @NonNull BigDecimal unitPrice;
    String label; // Optional, e.g., "Enterprise discount"

    @JsonCreator
    public VolumeBracket(
        @JsonProperty("minQuantity") BigDecimal minQuantity,
        @JsonProperty("maxQuantity") BigDecimal maxQuantity,
        @JsonProperty("unitPrice") BigDecimal unitPrice,
        @JsonProperty("label") String label) {
      this.minQuantity = minQuantity;
      this.maxQuantity = maxQuantity;
      this.unitPrice = unitPrice;
      this.label = label;
    }
  }

  @Override
  @JsonIgnore
  public Price.PriceStrategy getStrategy() {
    return Price.PriceStrategy.VOLUME;
  }

  @Override
  public BigDecimal calculateAmount(BigDecimal quantity) {
    if (quantity == null || quantity.compareTo(BigDecimal.ZERO) < 0) {
      throw new IllegalArgumentException("Quantity must be non-negative");
    }

    VolumeBracket applicableBracket = findApplicableBracket(quantity);
    return quantity.multiply(applicableBracket.unitPrice);
  }

  private VolumeBracket findApplicableBracket(BigDecimal quantity) {
    for (VolumeBracket bracket : brackets) {
      boolean inRange =
          quantity.compareTo(bracket.minQuantity) >= 0
              && (bracket.maxQuantity == null || quantity.compareTo(bracket.maxQuantity) <= 0);

      if (inRange) {
        return bracket;
      }
    }

    throw new IllegalStateException("No applicable volume bracket found for quantity: " + quantity);
  }

  @Override
  public void validate() {
    if (brackets == null || brackets.isEmpty()) {
      throw new IllegalArgumentException("Volume brackets cannot be empty");
    }

    // Validate each bracket
    for (VolumeBracket bracket : brackets) {
      if (bracket.minQuantity.compareTo(BigDecimal.ZERO) < 0) {
        throw new IllegalArgumentException("Bracket min quantity must be non-negative");
      }
      if (bracket.maxQuantity != null && bracket.maxQuantity.compareTo(bracket.minQuantity) < 0) {
        throw new IllegalArgumentException("Bracket max quantity must be >= min quantity");
      }
      if (bracket.unitPrice.compareTo(BigDecimal.ZERO) < 0) {
        throw new IllegalArgumentException("Bracket unit price must be non-negative");
      }
    }

    // Validate bracket continuity
    for (int i = 0; i < brackets.size() - 1; i++) {
      VolumeBracket current = brackets.get(i);
      VolumeBracket next = brackets.get(i + 1);

      if (current.maxQuantity == null) {
        throw new IllegalArgumentException("Only the last bracket can have unlimited max quantity");
      }

      BigDecimal expectedNext = current.maxQuantity.add(BigDecimal.ONE);
      if (!expectedNext.equals(next.minQuantity)) {
        throw new IllegalArgumentException("Gap or overlap between brackets at position " + i);
      }
    }
  }

  @Override
  public PricingSummary summarize() {
    String bracketDescriptions =
        brackets.stream()
            .map(
                bracket -> {
                  String range =
                      bracket.maxQuantity == null
                          ? bracket.minQuantity + "+"
                          : bracket.minQuantity + "-" + bracket.maxQuantity;
                  String label = bracket.label != null ? " (" + bracket.label + ")" : "";
                  return range + " units: $" + bracket.unitPrice + "/unit" + label;
                })
            .collect(Collectors.joining(", "));

    return PricingSummary.builder()
        .description("Volume pricing")
        .formula("All units at bracket rate")
        .details(bracketDescriptions)
        .build();
  }
}
