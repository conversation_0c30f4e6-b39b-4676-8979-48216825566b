package com.avantiq.billing.domain.product.validator;

import com.avantiq.billing.domain.product.model.Price;
import com.avantiq.billing.domain.product.valueobject.Pricing;
import com.avantiq.billing.domain.product.valueobject.pricing.FlatPricing;
import com.avantiq.billing.domain.product.valueobject.pricing.OveragePricing;
import com.avantiq.billing.domain.product.valueobject.pricing.PerUnitPricing;
import com.avantiq.billing.domain.product.valueobject.pricing.RampPricing;
import com.avantiq.billing.domain.product.valueobject.pricing.TieredPricing;
import com.avantiq.billing.domain.product.valueobject.pricing.VolumePricing;
import org.springframework.stereotype.Component;

/** Validates Pricing configurations for business rules and consistency. */
@Component
public class PricingValidator {

  /**
   * Validates a pricing configuration based on its strategy type.
   *
   * @param pricing the pricing to validate
   * @throws IllegalArgumentException if validation fails
   */
  public void validate(Pricing pricing) {
    if (pricing == null) {
      throw new IllegalArgumentException("Pricing cannot be null");
    }

    // First, let the pricing validate itself
    pricing.validate();

    // Then apply strategy-specific validation
    switch (pricing.getStrategy()) {
      case FLAT:
        validateFlatPricing((FlatPricing) pricing);
        break;
      case TIERED:
        validateTieredPricing((TieredPricing) pricing);
        break;
      case VOLUME:
        validateVolumePricing((VolumePricing) pricing);
        break;
      case OVERAGE:
        validateOveragePricing((OveragePricing) pricing);
        break;
      case RAMP:
        validateRampPricing((RampPricing) pricing);
        break;
      case PER_UNIT:
        validatePerUnitPricing((PerUnitPricing) pricing);
        break;
      default:
        throw new IllegalArgumentException("Unknown pricing strategy: " + pricing.getStrategy());
    }
  }

  /**
   * Validates that a pricing configuration matches the expected strategy.
   *
   * @param pricing the pricing to validate
   * @param expectedStrategy the expected strategy
   * @throws IllegalArgumentException if strategies don't match
   */
  public void validateStrategy(Pricing pricing, Price.PriceStrategy expectedStrategy) {
    if (pricing == null) {
      throw new IllegalArgumentException("Pricing cannot be null");
    }

    if (pricing.getStrategy() != expectedStrategy) {
      throw new IllegalArgumentException(
          String.format(
              "Pricing strategy mismatch. Expected: %s, Got: %s",
              expectedStrategy, pricing.getStrategy()));
    }
  }

  private void validateFlatPricing(FlatPricing pricing) {
    // FlatPricing validation is already handled in the class itself
    // Add any additional business rules here if needed
  }

  private void validateTieredPricing(TieredPricing pricing) {
    // Additional business rule validation
    if (pricing.getTiers().size() > 10) {
      throw new IllegalArgumentException("Maximum of 10 tiers allowed");
    }

    // Ensure first tier starts at 0
    if (pricing.getTiers().get(0).getMinQuantity().signum() != 0) {
      throw new IllegalArgumentException("First tier must start at quantity 0");
    }
  }

  private void validateVolumePricing(VolumePricing pricing) {
    // Additional business rule validation
    if (pricing.getBrackets().size() > 10) {
      throw new IllegalArgumentException("Maximum of 10 volume brackets allowed");
    }

    // Ensure first bracket starts at 0
    if (pricing.getBrackets().get(0).getMinQuantity().signum() != 0) {
      throw new IllegalArgumentException("First bracket must start at quantity 0");
    }
  }

  private void validateOveragePricing(OveragePricing pricing) {
    // Additional business rule validation
    // For example, overage price should typically be higher than the implied base unit price
    // But this is not enforced as there might be valid business cases for lower overage pricing
  }

  private void validateRampPricing(RampPricing pricing) {
    // Additional business rule validation
    if (pricing.getPeriods().size() > 12) {
      throw new IllegalArgumentException("Maximum of 12 ramp periods allowed");
    }

    // Validate the base pricing recursively
    validate(pricing.getBasePricing());
  }

  private void validatePerUnitPricing(PerUnitPricing pricing) {
    // Additional business rule validation
    if (pricing.getBlockSize() != null) {
      // Common block sizes validation
      if (pricing.getPricingUnit().equalsIgnoreCase("request")
          || pricing.getPricingUnit().equalsIgnoreCase("requests")) {
        // For requests, common block sizes are 1000, 10000, 100000, 1000000
        // But we don't enforce this as there might be valid custom block sizes
      }
    }
  }
}
