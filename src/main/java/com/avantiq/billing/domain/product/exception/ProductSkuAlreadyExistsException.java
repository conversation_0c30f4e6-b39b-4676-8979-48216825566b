package com.avantiq.billing.domain.product.exception;

import com.avantiq.billing.domain.common.exception.DomainException;
import com.avantiq.billing.domain.common.exception.ErrorCode;

/** Exception thrown when attempting to create a product with a SKU that already exists. */
public class ProductSkuAlreadyExistsException extends DomainException {

  public ProductSkuAlreadyExistsException(String sku) {
    super("Product with SKU already exists: " + sku, ErrorCode.PRODUCT_SKU_ALREADY_EXISTS);
  }

  public ProductSkuAlreadyExistsException(String message, Throwable cause) {
    super(message, cause, ErrorCode.PRODUCT_SKU_ALREADY_EXISTS);
  }
}
