package com.avantiq.billing.domain.product.service.impl;

import com.avantiq.billing.domain.product.model.Bundle;
import com.avantiq.billing.domain.product.model.BundleProduct;
import com.avantiq.billing.domain.product.service.interfaces.BundleService;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.stereotype.Service;

/** Implementation of the BundleService interface. */
@Service
public class BundleServiceImpl implements BundleService {

  private final List<Bundle> bundleRepository = new ArrayList<>(); // Simulated repository
  private final List<BundleProduct> bundleProductRepository =
      new ArrayList<>(); // Simulated repository

  @Override
  public Bundle createBundle(Bundle bundle) {
    bundle.setBundleId(UUID.randomUUID());
    bundle.setCreatedAt(LocalDateTime.now());
    bundle.setUpdatedAt(LocalDateTime.now());
    bundle.setVersion(1L);
    bundleRepository.add(bundle);
    return bundle;
  }

  @Override
  public Bundle updateBundle(Bundle bundle) {
    deleteBundle(bundle.getBundleId().toString());
    bundle.setUpdatedAt(LocalDateTime.now());
    bundle.setVersion(bundle.getVersion() + 1);
    bundleRepository.add(bundle);
    return bundle;
  }

  @Override
  public void deleteBundle(String bundleId) {
    bundleRepository.removeIf(bundle -> bundle.getBundleId().toString().equals(bundleId));

    // Also remove any associated bundle products
    bundleProductRepository.removeIf(
        bundleProduct -> bundleProduct.getBundleId().toString().equals(bundleId));
  }

  @Override
  public Optional<Bundle> getBundleById(String bundleId) {
    return bundleRepository.stream()
        .filter(bundle -> bundle.getBundleId().toString().equals(bundleId))
        .findFirst();
  }

  @Override
  public List<Bundle> listAllBundles() {
    return new ArrayList<>(bundleRepository);
  }

  @Override
  public BundleProduct addProductToBundle(
      String bundleId, String productId, int quantity, boolean optionalFlag) {
    UUID bundleUuid = UUID.fromString(bundleId);
    UUID productUuid = UUID.fromString(productId);

    BundleProduct bundleProduct =
        new BundleProduct(UUID.randomUUID(), bundleUuid, productUuid, quantity, optionalFlag);

    bundleProductRepository.add(bundleProduct);

    // Update the bundle's updatedAt and version
    getBundleById(bundleId)
        .ifPresent(
            bundle -> {
              bundle.setUpdatedAt(LocalDateTime.now());
              bundle.setVersion(bundle.getVersion() + 1);
            });

    return bundleProduct;
  }

  @Override
  public void removeProductFromBundle(String bundleProductId) {
    Optional<BundleProduct> bundleProductOpt =
        bundleProductRepository.stream()
            .filter(bp -> bp.getId().toString().equals(bundleProductId))
            .findFirst();

    bundleProductOpt.ifPresent(
        bundleProduct -> {
          bundleProductRepository.remove(bundleProduct);

          // Update the bundle's updatedAt and version
          getBundleById(bundleProduct.getBundleId().toString())
              .ifPresent(
                  bundle -> {
                    bundle.setUpdatedAt(LocalDateTime.now());
                    bundle.setVersion(bundle.getVersion() + 1);
                  });
        });
  }

  @Override
  public boolean validateBundle(Bundle bundle) {
    if (bundle.getName() == null || bundle.getName().isEmpty()) {
      return false;
    }
    if (bundle.getStatus() == null || bundle.getStatus().isEmpty()) {
      return false;
    }
    return true;
  }
}
