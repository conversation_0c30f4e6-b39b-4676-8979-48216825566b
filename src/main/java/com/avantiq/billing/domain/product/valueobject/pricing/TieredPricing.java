package com.avantiq.billing.domain.product.valueobject.pricing;

import com.avantiq.billing.domain.product.model.Price;
import com.avantiq.billing.domain.product.valueobject.Pricing;
import com.avantiq.billing.domain.product.valueobject.PricingSummary;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeName;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Builder;
import lombok.NonNull;
import lombok.Value;

/**
 * Tiered/graduated pricing where different quantities fall into different price tiers. Each tier's
 * quantity is charged at that tier's rate (cumulative).
 */
@Value
@Builder
@JsonTypeName("TIERED")
public class TieredPricing implements Pricing {

  @NonNull List<Tier> tiers;

  @JsonCreator
  public TieredPricing(@JsonProperty("tiers") List<Tier> tiers) {
    this.tiers = tiers;
  }

  @Value
  @Builder
  public static class Tier {
    @NonNull BigDecimal minQuantity;
    BigDecimal maxQuantity; // null = infinity
    @NonNull BigDecimal unitPrice;
    String label; // Optional, e.g., "Starter tier"

    @JsonCreator
    public Tier(
        @JsonProperty("minQuantity") BigDecimal minQuantity,
        @JsonProperty("maxQuantity") BigDecimal maxQuantity,
        @JsonProperty("unitPrice") BigDecimal unitPrice,
        @JsonProperty("label") String label) {
      this.minQuantity = minQuantity;
      this.maxQuantity = maxQuantity;
      this.unitPrice = unitPrice;
      this.label = label;
    }
  }

  @Override
  @JsonIgnore
  public Price.PriceStrategy getStrategy() {
    return Price.PriceStrategy.TIERED;
  }

  @Override
  public BigDecimal calculateAmount(BigDecimal quantity) {
    if (quantity == null || quantity.compareTo(BigDecimal.ZERO) < 0) {
      throw new IllegalArgumentException("Quantity must be non-negative");
    }

    BigDecimal total = BigDecimal.ZERO;
    BigDecimal remaining = quantity;

    for (Tier tier : tiers) {
      if (remaining.compareTo(BigDecimal.ZERO) <= 0) {
        break;
      }

      BigDecimal tierQuantity = calculateTierQuantity(remaining, tier);
      if (tierQuantity.compareTo(BigDecimal.ZERO) > 0) {
        total = total.add(tier.unitPrice.multiply(tierQuantity));
        remaining = remaining.subtract(tierQuantity);
      }
    }

    return total;
  }

  private BigDecimal calculateTierQuantity(BigDecimal remaining, Tier tier) {
    // Calculate how many units can be consumed from this tier
    if (tier.maxQuantity == null) {
      // Unlimited tier - consume all remaining
      return remaining;
    }

    // Calculate tier capacity
    BigDecimal tierCapacity = tier.maxQuantity.subtract(tier.minQuantity).add(BigDecimal.ONE);

    // Consume the minimum of remaining quantity and tier capacity
    return remaining.min(tierCapacity);
  }

  @Override
  public void validate() {
    if (tiers == null || tiers.isEmpty()) {
      throw new IllegalArgumentException("Tiers cannot be empty");
    }

    // Validate each tier
    for (Tier tier : tiers) {
      if (tier.minQuantity.compareTo(BigDecimal.ZERO) < 0) {
        throw new IllegalArgumentException("Tier min quantity must be non-negative");
      }
      if (tier.maxQuantity != null && tier.maxQuantity.compareTo(tier.minQuantity) < 0) {
        throw new IllegalArgumentException("Tier max quantity must be >= min quantity");
      }
      if (tier.unitPrice.compareTo(BigDecimal.ZERO) < 0) {
        throw new IllegalArgumentException("Tier unit price must be non-negative");
      }
    }

    // Validate tier continuity
    for (int i = 0; i < tiers.size() - 1; i++) {
      Tier current = tiers.get(i);
      Tier next = tiers.get(i + 1);

      if (current.maxQuantity == null) {
        throw new IllegalArgumentException("Only the last tier can have unlimited max quantity");
      }

      BigDecimal expectedNext = current.maxQuantity.add(BigDecimal.ONE);
      if (!expectedNext.equals(next.minQuantity)) {
        throw new IllegalArgumentException("Gap or overlap between tiers at position " + i);
      }
    }
  }

  @Override
  public PricingSummary summarize() {
    String tierDescriptions =
        tiers.stream()
            .map(
                tier -> {
                  String range =
                      tier.maxQuantity == null
                          ? tier.minQuantity + "+"
                          : tier.minQuantity + "-" + tier.maxQuantity;
                  String label = tier.label != null ? " (" + tier.label + ")" : "";
                  return range + ": $" + tier.unitPrice + "/unit" + label;
                })
            .collect(Collectors.joining(", "));

    return PricingSummary.builder()
        .description("Tiered pricing")
        .formula("Cumulative tiers")
        .details(tierDescriptions)
        .build();
  }
}
