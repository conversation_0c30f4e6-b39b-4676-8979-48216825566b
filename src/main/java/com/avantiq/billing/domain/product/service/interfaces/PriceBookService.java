package com.avantiq.billing.domain.product.service.interfaces;

import com.avantiq.billing.domain.product.model.PriceBook;
import java.util.List;
import java.util.Optional;

/** Domain service interface for price book-related business logic. */
public interface PriceBookService {

  /**
   * Creates a new price book.
   *
   * @param priceBook the price book to create
   * @return the created price book
   */
  PriceBook createPriceBook(PriceBook priceBook);

  /**
   * Updates an existing price book.
   *
   * @param priceBook the price book to update
   * @return the updated price book
   */
  PriceBook updatePriceBook(PriceBook priceBook);

  /**
   * Deletes a price book by ID.
   *
   * @param priceBookId the ID of the price book to delete
   */
  void deletePriceBook(String priceBookId);

  /**
   * Retrieves a price book by ID.
   *
   * @param priceBookId the ID of the price book to retrieve
   * @return an Optional containing the price book, or empty if not found
   */
  Optional<PriceBook> getPriceBookById(String priceBookId);

  /**
   * Lists all price books.
   *
   * @return a list of all price books
   */
  List<PriceBook> listAllPriceBooks();

  /**
   * Validates the price book details.
   *
   * @param priceBook the price book to validate
   * @return true if the price book is valid, false otherwise
   */
  boolean validatePriceBook(PriceBook priceBook);
}
