package com.avantiq.billing.domain.product.service.interfaces;

import com.avantiq.billing.domain.product.model.ProductFamily;
import java.util.List;
import java.util.Optional;

/** Domain service interface for product family-related business logic. */
public interface ProductFamilyService {

  /**
   * Creates a new product family.
   *
   * @param productFamily the product family to create
   * @return the created product family
   */
  ProductFamily createProductFamily(ProductFamily productFamily);

  /**
   * Updates an existing product family.
   *
   * @param productFamily the product family to update
   * @return the updated product family
   */
  ProductFamily updateProductFamily(ProductFamily productFamily);

  /**
   * Deletes a product family by ID.
   *
   * @param productFamilyId the ID of the product family to delete
   */
  void deleteProductFamily(String productFamilyId);

  /**
   * Retrieves a product family by ID.
   *
   * @param productFamilyId the ID of the product family to retrieve
   * @return an Optional containing the product family, or empty if not found
   */
  Optional<ProductFamily> getProductFamilyById(String productFamilyId);

  /**
   * Lists all product families.
   *
   * @return a list of all product families
   */
  List<ProductFamily> listAllProductFamilies();

  /**
   * Validates the product family details.
   *
   * @param productFamily the product family to validate
   * @return true if the product family is valid, false otherwise
   */
  boolean validateProductFamily(ProductFamily productFamily);
}
