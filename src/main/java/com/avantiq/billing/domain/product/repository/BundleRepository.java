package com.avantiq.billing.domain.product.repository;

import com.avantiq.billing.domain.product.model.Bundle;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Repository interface for Bundle domain entity. Defines the contract for accessing bundle data.
 */
public interface BundleRepository {

  /**
   * Find a bundle by ID.
   *
   * @param bundleId the bundle ID
   * @return an Optional containing the bundle if found
   */
  Optional<Bundle> findById(UUID bundleId);

  /**
   * Find a bundle by ID with tenant isolation.
   *
   * @param bundleId the bundle ID
   * @param tenantId the tenant ID
   * @return an Optional containing the bundle if found
   */
  Optional<Bundle> findByIdAndTenantId(UUID bundleId, Long tenantId);

  /**
   * Find bundles by name.
   *
   * @param name the bundle name
   * @return list of bundles with matching name
   */
  List<Bundle> findByName(String name);

  /**
   * Find active bundles with tenant isolation.
   *
   * @param tenantId the tenant ID
   * @return list of active bundles
   */
  List<Bundle> findActiveByTenantId(Long tenantId);

  /**
   * Find all bundles with pagination and tenant isolation.
   *
   * @param tenantId the tenant ID
   * @param pageable pagination information
   * @return a page of bundles
   */
  Page<Bundle> findAllByTenantId(Long tenantId, Pageable pageable);

  /**
   * Save a bundle.
   *
   * @param bundle the bundle to save
   * @return the saved bundle
   */
  Bundle save(Bundle bundle);

  /**
   * Delete a bundle.
   *
   * @param bundle the bundle to delete
   */
  void delete(Bundle bundle);

  /**
   * Check if a bundle exists by ID and tenant.
   *
   * @param bundleId the bundle ID
   * @param tenantId the tenant ID
   * @return true if exists, false otherwise
   */
  boolean existsByIdAndTenantId(UUID bundleId, Long tenantId);
}
