package com.avantiq.billing.domain.product.service.impl;

import com.avantiq.billing.domain.product.model.Product;
import com.avantiq.billing.domain.product.service.interfaces.ProductDomainService;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/** Implementation of the ProductDomainService interface. */
public class ProductDomainServiceImpl implements ProductDomainService {

  private final List<Product> productRepository = new ArrayList<>(); // Simulated repository

  @Override
  public boolean validateProduct(Product product) {
    if (product.getName() == null || product.getName().isEmpty()) {
      return false;
    }
    if (product.getPrice() == null || product.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
      return false;
    }
    return true;
  }

  @Override
  public Product createProduct(Product product) {
    product.setId(UUID.randomUUID());
    productRepository.add(product);
    return product;
  }

  @Override
  public Product updateProduct(Product product) {
    deleteProduct(product.getId().toString());
    productRepository.add(product);
    return product;
  }

  @Override
  public void deleteProduct(String productId) {
    productRepository.removeIf(product -> product.getId().toString().equals(productId));
  }

  @Override
  public Optional<Product> getProductById(String productId) {
    return productRepository.stream()
        .filter(product -> product.getId().toString().equals(productId))
        .findFirst();
  }

  @Override
  public List<Product> listAllProducts() {
    return new ArrayList<>(productRepository);
  }
}
