package com.avantiq.billing.domain.product.exception;

import com.avantiq.billing.domain.common.exception.DomainException;
import com.avantiq.billing.domain.common.exception.ErrorCode;

/** Exception thrown when a product with the requested identifier cannot be found. */
public class ProductNotFoundException extends DomainException {

  public ProductNotFoundException(String id) {
    super("Product not found with ID: " + id, ErrorCode.PRODUCT_NOT_FOUND);
  }

  public ProductNotFoundException(String message, Throwable cause) {
    super(message, cause, ErrorCode.PRODUCT_NOT_FOUND);
  }
}
