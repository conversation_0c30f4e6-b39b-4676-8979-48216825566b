package com.avantiq.billing.domain.product.factory;

import com.avantiq.billing.domain.product.valueobject.Pricing;
import com.avantiq.billing.domain.product.valueobject.pricing.FlatPricing;
import com.avantiq.billing.domain.product.valueobject.pricing.OveragePricing;
import com.avantiq.billing.domain.product.valueobject.pricing.PerUnitPricing;
import com.avantiq.billing.domain.product.valueobject.pricing.RampPricing;
import com.avantiq.billing.domain.product.valueobject.pricing.TieredPricing;
import com.avantiq.billing.domain.product.valueobject.pricing.VolumePricing;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import org.springframework.stereotype.Component;

/** Factory for creating Pricing objects with fluent builders. */
@Component
public class PricingFactory {

  // Simple factory methods

  public FlatPricing createFlatPricing(BigDecimal unitPrice) {
    return FlatPricing.builder().unitPrice(unitPrice).build();
  }

  public OveragePricing createOveragePricing(
      BigDecimal includedQuantity, BigDecimal basePrice, BigDecimal overageUnitPrice) {
    return OveragePricing.builder()
        .includedQuantity(includedQuantity)
        .basePrice(basePrice)
        .overageUnitPrice(overageUnitPrice)
        .build();
  }

  public PerUnitPricing createPerUnitPricing(BigDecimal pricePerUnit, String pricingUnit) {
    return PerUnitPricing.builder().pricePerUnit(pricePerUnit).pricingUnit(pricingUnit).build();
  }

  public PerUnitPricing createBlockPricing(
      BigDecimal pricePerBlock, String pricingUnit, BigDecimal blockSize) {
    return PerUnitPricing.builder()
        .pricePerUnit(pricePerBlock)
        .pricingUnit(pricingUnit)
        .blockSize(blockSize)
        .blockRounding(RoundingMode.CEILING)
        .build();
  }

  // Builder pattern for complex pricing structures

  public TieredPricingBuilder tieredPricing() {
    return new TieredPricingBuilder();
  }

  public VolumePricingBuilder volumePricing() {
    return new VolumePricingBuilder();
  }

  public RampPricingBuilder rampPricing(Pricing basePricing) {
    return new RampPricingBuilder(basePricing);
  }

  // Builder classes

  public class TieredPricingBuilder {
    private final List<TieredPricing.Tier> tiers = new ArrayList<>();

    public TieredPricingBuilder addTier(
        BigDecimal minQuantity, BigDecimal maxQuantity, BigDecimal unitPrice) {
      return addTier(minQuantity, maxQuantity, unitPrice, null);
    }

    public TieredPricingBuilder addTier(
        BigDecimal minQuantity, BigDecimal maxQuantity, BigDecimal unitPrice, String label) {
      tiers.add(
          TieredPricing.Tier.builder()
              .minQuantity(minQuantity)
              .maxQuantity(maxQuantity)
              .unitPrice(unitPrice)
              .label(label)
              .build());
      return this;
    }

    public TieredPricingBuilder addUnlimitedTier(BigDecimal minQuantity, BigDecimal unitPrice) {
      return addTier(minQuantity, null, unitPrice, null);
    }

    public TieredPricingBuilder addUnlimitedTier(
        BigDecimal minQuantity, BigDecimal unitPrice, String label) {
      return addTier(minQuantity, null, unitPrice, label);
    }

    public TieredPricing build() {
      if (tiers.isEmpty()) {
        throw new IllegalStateException("At least one tier must be added");
      }

      TieredPricing pricing = TieredPricing.builder().tiers(new ArrayList<>(tiers)).build();

      pricing.validate();
      return pricing;
    }
  }

  public class VolumePricingBuilder {
    private final List<VolumePricing.VolumeBracket> brackets = new ArrayList<>();

    public VolumePricingBuilder addBracket(
        BigDecimal minQuantity, BigDecimal maxQuantity, BigDecimal unitPrice) {
      return addBracket(minQuantity, maxQuantity, unitPrice, null);
    }

    public VolumePricingBuilder addBracket(
        BigDecimal minQuantity, BigDecimal maxQuantity, BigDecimal unitPrice, String label) {
      brackets.add(
          VolumePricing.VolumeBracket.builder()
              .minQuantity(minQuantity)
              .maxQuantity(maxQuantity)
              .unitPrice(unitPrice)
              .label(label)
              .build());
      return this;
    }

    public VolumePricingBuilder addUnlimitedBracket(BigDecimal minQuantity, BigDecimal unitPrice) {
      return addBracket(minQuantity, null, unitPrice, null);
    }

    public VolumePricingBuilder addUnlimitedBracket(
        BigDecimal minQuantity, BigDecimal unitPrice, String label) {
      return addBracket(minQuantity, null, unitPrice, label);
    }

    public VolumePricing build() {
      if (brackets.isEmpty()) {
        throw new IllegalStateException("At least one bracket must be added");
      }

      VolumePricing pricing = VolumePricing.builder().brackets(new ArrayList<>(brackets)).build();

      pricing.validate();
      return pricing;
    }
  }

  public class RampPricingBuilder {
    private final Pricing basePricing;
    private final List<RampPricing.RampPeriod> periods = new ArrayList<>();

    public RampPricingBuilder(Pricing basePricing) {
      if (basePricing == null) {
        throw new IllegalArgumentException("Base pricing cannot be null");
      }
      this.basePricing = basePricing;
    }

    public RampPricingBuilder addPeriod(Integer durationMonths, BigDecimal discountPercent) {
      return addPeriod(durationMonths, discountPercent, null);
    }

    public RampPricingBuilder addPeriod(
        Integer durationMonths, BigDecimal discountPercent, String label) {
      int periodNumber = periods.size() + 1;
      periods.add(
          RampPricing.RampPeriod.builder()
              .periodNumber(periodNumber)
              .durationMonths(durationMonths)
              .discountPercent(discountPercent)
              .label(label)
              .build());
      return this;
    }

    public RampPricingBuilder addOngoingPeriod(BigDecimal discountPercent) {
      return addPeriod(null, discountPercent, null);
    }

    public RampPricingBuilder addOngoingPeriod(BigDecimal discountPercent, String label) {
      return addPeriod(null, discountPercent, label);
    }

    public RampPricing build() {
      if (periods.isEmpty()) {
        throw new IllegalStateException("At least one period must be added");
      }

      RampPricing pricing =
          RampPricing.builder().basePricing(basePricing).periods(new ArrayList<>(periods)).build();

      pricing.validate();
      return pricing;
    }
  }
}
