package com.avantiq.billing.domain.product.model;

import java.time.LocalDate;
import java.util.UUID;

/** Represents a price book in the domain. */
public class PriceBook {

  private UUID id;
  private String name;
  private String segment;
  private String currency;
  private LocalDate startDate;
  private LocalDate endDate;
  private String status;

  public PriceBook(
      UUID id,
      String name,
      String segment,
      String currency,
      LocalDate startDate,
      LocalDate endDate,
      String status) {
    this.id = id;
    this.name = name;
    this.segment = segment;
    this.currency = currency;
    this.startDate = startDate;
    this.endDate = endDate;
    this.status = status;
  }

  public UUID getId() {
    return id;
  }

  public void setId(UUID id) {
    this.id = id;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getSegment() {
    return segment;
  }

  public void setSegment(String segment) {
    this.segment = segment;
  }

  public String getCurrency() {
    return currency;
  }

  public void setCurrency(String currency) {
    this.currency = currency;
  }

  public LocalDate getStartDate() {
    return startDate;
  }

  public void setStartDate(LocalDate startDate) {
    this.startDate = startDate;
  }

  public LocalDate getEndDate() {
    return endDate;
  }

  public void setEndDate(LocalDate endDate) {
    this.endDate = endDate;
  }

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  @Override
  public String toString() {
    return "PriceBook{"
        + "id="
        + id
        + ", name='"
        + name
        + '\''
        + ", segment='"
        + segment
        + '\''
        + ", currency='"
        + currency
        + '\''
        + ", startDate="
        + startDate
        + ", endDate="
        + endDate
        + ", status='"
        + status
        + '\''
        + '}';
  }
}
