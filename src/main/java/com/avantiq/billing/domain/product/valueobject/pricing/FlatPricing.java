package com.avantiq.billing.domain.product.valueobject.pricing;

import com.avantiq.billing.domain.product.model.Price;
import com.avantiq.billing.domain.product.valueobject.Pricing;
import com.avantiq.billing.domain.product.valueobject.PricingSummary;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeName;
import java.math.BigDecimal;
import lombok.Builder;
import lombok.NonNull;
import lombok.Value;

/** Simple flat rate pricing - quantity multiplied by unit price. */
@Value
@Builder
@JsonTypeName("FLAT")
public class FlatPricing implements Pricing {

  @NonNull BigDecimal unitPrice;

  @JsonCreator
  public FlatPricing(@JsonProperty("unitPrice") BigDecimal unitPrice) {
    this.unitPrice = unitPrice;
  }

  @Override
  @JsonIgnore
  public Price.PriceStrategy getStrategy() {
    return Price.PriceStrategy.FLAT;
  }

  @Override
  public BigDecimal calculateAmount(BigDecimal quantity) {
    if (quantity == null || quantity.compareTo(BigDecimal.ZERO) < 0) {
      throw new IllegalArgumentException("Quantity must be non-negative");
    }
    return unitPrice.multiply(quantity);
  }

  @Override
  public void validate() {
    if (unitPrice == null || unitPrice.compareTo(BigDecimal.ZERO) < 0) {
      throw new IllegalArgumentException("Unit price must be non-negative");
    }
  }

  @Override
  public PricingSummary summarize() {
    return PricingSummary.builder()
        .description("$" + unitPrice + " per unit")
        .formula("quantity × $" + unitPrice)
        .build();
  }
}
