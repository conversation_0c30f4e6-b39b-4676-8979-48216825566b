package com.avantiq.billing.domain.product.repository;

import com.avantiq.billing.domain.product.model.Price;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/** Repository interface for Price domain entity. Defines the contract for accessing price data. */
public interface PriceRepository {

  /**
   * Find a price by ID.
   *
   * @param priceId the price ID
   * @return an Optional containing the price if found
   */
  Optional<Price> findById(UUID priceId);

  /**
   * Find prices by product ID.
   *
   * @param productId the product ID
   * @return list of prices for the product
   */
  List<Price> findByProductId(UUID productId);

  /**
   * Find prices by price book ID.
   *
   * @param priceBookId the price book ID
   * @return list of prices in the price book
   */
  List<Price> findByPriceBookId(UUID priceBookId);

  /**
   * Find prices by product and price book.
   *
   * @param productId the product ID
   * @param priceBookId the price book ID
   * @return list of prices for the product in the price book
   */
  List<Price> findByProductIdAndPriceBookId(UUID productId, UUID priceBookId);

  /**
   * Find active prices by product with tenant isolation.
   *
   * @param productId the product ID
   * @param tenantId the tenant ID
   * @return list of active prices
   */
  List<Price> findActiveByProductIdAndTenantId(UUID productId, Long tenantId);

  /**
   * Find all prices with pagination and tenant isolation.
   *
   * @param tenantId the tenant ID
   * @param pageable pagination information
   * @return a page of prices
   */
  Page<Price> findAllByTenantId(Long tenantId, Pageable pageable);

  /**
   * Save a price.
   *
   * @param price the price to save
   * @return the saved price
   */
  Price save(Price price);

  /**
   * Delete a price.
   *
   * @param price the price to delete
   */
  void delete(Price price);

  /**
   * Check if a price exists for product and price book.
   *
   * @param productId the product ID
   * @param priceBookId the price book ID
   * @return true if exists, false otherwise
   */
  boolean existsByProductIdAndPriceBookId(UUID productId, UUID priceBookId);
}
