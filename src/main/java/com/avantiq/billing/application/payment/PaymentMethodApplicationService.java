package com.avantiq.billing.application.payment;

import com.avantiq.billing.domain.payment.exception.PaymentMethodNotFoundException;
import com.avantiq.billing.domain.payment.model.PaymentMethod;
import com.avantiq.billing.domain.payment.repository.PaymentMethodRepository;
import com.avantiq.billing.domain.payment.service.PaymentMethodDomainService;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Application service for PaymentMethod operations. Orchestrates domain services and repositories
 * to fulfill use cases.
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class PaymentMethodApplicationService {

  private final PaymentMethodRepository paymentMethodRepository;
  private final PaymentMethodDomainService paymentMethodDomainService;

  /** Add a new payment method for a customer. */
  public PaymentMethodResult addPaymentMethod(AddPaymentMethodRequest request) {
    log.info(
        "Adding payment method for customer: {} in tenant: {}",
        request.getCustomerId(),
        request.getTenantId());

    // Validate business rules
    paymentMethodDomainService.validateCanAddPaymentMethod(
        request.getCustomerId(), request.getTenantId());
    paymentMethodDomainService.validatePaymentMethodDetails(
        request.getType(), request.getDetails());

    // Create domain model
    PaymentMethod paymentMethod =
        PaymentMethod.builder()
            .id(UUID.randomUUID())
            .customerId(request.getCustomerId())
            .tenantId(request.getTenantId())
            .type(request.getType())
            .details(request.getDetails())
            .isPrimary(request.isPrimary())
            .isValidated(false) // New payment methods start unvalidated
            .createdAt(LocalDateTime.now())
            .lastModifiedAt(LocalDateTime.now())
            .build();

    // Handle primary payment method logic
    if (request.isPrimary()) {
      paymentMethodDomainService.setPrimaryPaymentMethod(
          request.getCustomerId(), paymentMethod.getId(), request.getTenantId());
    }

    // Save
    PaymentMethod saved = paymentMethodRepository.save(paymentMethod);

    log.info(
        "Successfully added payment method with ID: {} for customer: {}",
        saved.getId(),
        request.getCustomerId());

    return PaymentMethodResult.builder()
        .paymentMethodId(saved.getId())
        .customerId(saved.getCustomerId())
        .tenantId(saved.getTenantId())
        .type(saved.getType())
        .isPrimary(saved.isPrimary())
        .isValidated(saved.isValidated())
        .success(true)
        .build();
  }

  /** Get payment method by ID with tenant isolation. */
  @Transactional(readOnly = true)
  public Optional<PaymentMethod> getPaymentMethod(UUID paymentMethodId, Long tenantId) {
    log.debug("Retrieving payment method with ID: {} in tenant: {}", paymentMethodId, tenantId);

    return paymentMethodRepository.findByIdAndTenantId(paymentMethodId, tenantId);
  }

  /** Get all payment methods for a customer. */
  @Transactional(readOnly = true)
  public List<PaymentMethod> getCustomerPaymentMethods(UUID customerId, Long tenantId) {
    log.debug("Retrieving payment methods for customer: {} in tenant: {}", customerId, tenantId);

    return paymentMethodRepository.findByCustomerIdAndTenantId(customerId, tenantId);
  }

  /** Get primary payment method for a customer. */
  @Transactional(readOnly = true)
  public Optional<PaymentMethod> getPrimaryPaymentMethod(UUID customerId, Long tenantId) {
    log.debug(
        "Retrieving primary payment method for customer: {} in tenant: {}", customerId, tenantId);

    return paymentMethodRepository.findPrimaryByCustomerIdAndTenantId(customerId, tenantId);
  }

  /** Set a payment method as primary. */
  public void setPrimaryPaymentMethod(UUID customerId, UUID paymentMethodId, Long tenantId) {
    log.info(
        "Setting payment method {} as primary for customer: {} in tenant: {}",
        paymentMethodId,
        customerId,
        tenantId);

    paymentMethodDomainService.setPrimaryPaymentMethod(customerId, paymentMethodId, tenantId);
  }

  /** Delete a payment method. */
  public void deletePaymentMethod(UUID customerId, UUID paymentMethodId, Long tenantId) {
    log.info(
        "Deleting payment method {} for customer: {} in tenant: {}",
        paymentMethodId,
        customerId,
        tenantId);

    // Validate business rules
    paymentMethodDomainService.validateCanDeletePaymentMethod(
        customerId, paymentMethodId, tenantId);

    // Delete
    paymentMethodRepository.deleteByIdAndTenantId(paymentMethodId, tenantId);

    log.info("Successfully deleted payment method: {}", paymentMethodId);
  }

  /** Validate a payment method (e.g., after external verification). */
  public void validatePaymentMethod(UUID paymentMethodId, Long tenantId) {
    log.info("Validating payment method: {} in tenant: {}", paymentMethodId, tenantId);

    PaymentMethod paymentMethod =
        paymentMethodRepository
            .findByIdAndTenantId(paymentMethodId, tenantId)
            .orElseThrow(
                () ->
                    new PaymentMethodNotFoundException(
                        "Payment method not found: " + paymentMethodId));

    PaymentMethod validatedPaymentMethod =
        paymentMethod.toBuilder().isValidated(true).lastModifiedAt(LocalDateTime.now()).build();

    paymentMethodRepository.save(validatedPaymentMethod);

    log.info("Successfully validated payment method: {}", paymentMethodId);
  }

  /** Request DTO for adding payment method. */
  @Value
  @lombok.Builder
  public static class AddPaymentMethodRequest {
    UUID customerId;
    Long tenantId;
    PaymentMethod.PaymentMethodType type;
    String details;
    boolean isPrimary;
  }

  /** Result DTO for payment method operations. */
  @Value
  @lombok.Builder
  public static class PaymentMethodResult {
    UUID paymentMethodId;
    UUID customerId;
    Long tenantId;
    PaymentMethod.PaymentMethodType type;
    boolean isPrimary;
    boolean isValidated;
    boolean success;
  }
}
