package com.avantiq.billing.application.security;

import com.avantiq.billing.domain.security.model.Role;
import com.avantiq.billing.domain.security.model.UserPrincipal;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

/**
 * Service for segment-based security validation. Verifies user access to specific segments within
 * their tenant.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SegmentSecurityService {

  private final TenantSecurityService tenantSecurityService;

  /** Check if the current user can access the specified segment. */
  public boolean canAccessSegment(UUID segmentId) {
    return getCurrentUserPrincipal().map(user -> user.canAccessSegment(segmentId)).orElse(false);
  }

  /** Check if the current user can access all segments (tenant admin). */
  public boolean canAccessAllSegments() {
    return getCurrentUserPrincipal().map(user -> user.hasRole(Role.TENANT_ADMIN)).orElse(false);
  }

  /** Get the current user's accessible segments. */
  public Optional<Set<UUID>> getCurrentUserSegments() {
    return getCurrentUserPrincipal().map(UserPrincipal::getAssignedSegments);
  }

  /**
   * Verify that the current user can access the specified segment. Throws SecurityException if
   * access is denied.
   */
  public void verifySegmentAccess(UUID segmentId) {
    if (!canAccessSegment(segmentId)) {
      String currentUser = tenantSecurityService.getCurrentUsername().orElse("unknown");
      Long currentTenant = tenantSecurityService.getCurrentTenantId().orElse(null);

      log.warn(
          "Segment access denied: user {} from tenant {} attempted to access segment {}",
          currentUser,
          currentTenant,
          segmentId);

      throw new SecurityException("Access denied to segment: " + segmentId);
    }
  }

  /** Filter a set of segment IDs to only include those the current user can access. */
  public Set<UUID> filterAccessibleSegments(Set<UUID> segmentIds) {
    if (canAccessAllSegments()) {
      return segmentIds; // Tenant admin can access all
    }

    Optional<Set<UUID>> userSegments = getCurrentUserSegments();
    if (userSegments.isEmpty()) {
      return Set.of(); // No accessible segments
    }

    return segmentIds.stream()
        .filter(userSegments.get()::contains)
        .collect(java.util.stream.Collectors.toSet());
  }

  /** Check if the current user is a segment admin for any segments. */
  public boolean isCurrentUserSegmentAdmin() {
    return getCurrentUserPrincipal().map(user -> user.hasRole(Role.SEGMENT_ADMIN)).orElse(false);
  }

  /** Check if the current user has write access to the specified segment. */
  public boolean canWriteToSegment(UUID segmentId) {
    return getCurrentUserPrincipal()
        .map(
            user -> {
              // Check if user can access the segment
              if (!user.canAccessSegment(segmentId)) {
                return false;
              }
              // Check if user has write permissions (not READONLY)
              return !user.getRoles().equals(Set.of(Role.READONLY));
            })
        .orElse(false);
  }

  /** Verify that the current user has write access to the specified segment. */
  public void verifySegmentWriteAccess(UUID segmentId) {
    if (!canWriteToSegment(segmentId)) {
      String currentUser = tenantSecurityService.getCurrentUsername().orElse("unknown");

      log.warn(
          "Segment write access denied: user {} attempted to write to segment {}",
          currentUser,
          segmentId);

      throw new SecurityException("Write access denied to segment: " + segmentId);
    }
  }

  /** Get segments for tenant-wide operations (for tenant admins only). */
  public Set<UUID> getAllAccessibleSegments() {
    if (!canAccessAllSegments()) {
      return getCurrentUserSegments().orElse(Set.of());
    }

    // For tenant admins, this would typically query all segments in the tenant
    // For now, return the user's assigned segments
    return getCurrentUserSegments().orElse(Set.of());
  }

  /** Get the current UserPrincipal from Spring Security context. */
  private Optional<UserPrincipal> getCurrentUserPrincipal() {
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

    if (authentication == null || !authentication.isAuthenticated()) {
      return Optional.empty();
    }

    Object principal = authentication.getPrincipal();
    if (principal instanceof UserPrincipal userPrincipal) {
      return Optional.of(userPrincipal);
    }

    return Optional.empty();
  }
}
