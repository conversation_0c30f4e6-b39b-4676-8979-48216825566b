package com.avantiq.billing.application.security;

import com.avantiq.billing.domain.security.model.User;
import com.avantiq.billing.domain.security.model.UserPrincipal;
import com.avantiq.billing.domain.security.repository.UserRepository;
import com.avantiq.billing.infrastructure.security.jwt.JwtTokenProvider;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Application service for authentication operations. Handles login, logout, and user authentication
 * business logic.
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class AuthenticationApplicationService {

  private final JwtTokenProvider jwtTokenProvider;
  private final PasswordEncoder passwordEncoder;
  private final UserRepository userRepository;

  /** Authenticate user and generate JWT token. */
  public AuthenticationResult login(LoginRequest request) {
    log.info("Login attempt for username: {}", request.getUsername());

    try {
      // Authenticate user with database lookup
      User user = validateCredentials(request.getUsername(), request.getPassword());

      UserPrincipal userPrincipal = UserPrincipal.fromUser(user);
      String token = jwtTokenProvider.generateToken(userPrincipal);

      log.info(
          "Successful login for user: {} (tenant: {})", user.getUsername(), user.getTenantId());

      return AuthenticationResult.builder()
          .token(token)
          .userId(user.getUserId())
          .username(user.getUsername())
          .tenantId(user.getTenantId())
          .expiresAt(
              jwtTokenProvider
                  .getExpirationDateFromToken(token)
                  .toInstant()
                  .atZone(java.time.ZoneId.systemDefault())
                  .toLocalDateTime())
          .success(true)
          .build();

    } catch (Exception ex) {
      log.warn(
          "Failed login attempt for username: {} - {}", request.getUsername(), ex.getMessage());
      throw new BadCredentialsException("Invalid username or password");
    }
  }

  /**
   * Logout user and invalidate token. Note: In a production system, you might want to maintain a
   * token blacklist.
   */
  public void logout() {
    String username = getCurrentUsername().orElse("unknown");
    log.info("Logout for user: {}", username);

    // Clear security context
    SecurityContextHolder.clearContext();

    // TODO: Add token to blacklist if implementing token revocation
  }

  /** Refresh JWT token for authenticated user. */
  public AuthenticationResult refreshToken() {
    UserPrincipal currentUser =
        getCurrentUserPrincipal()
            .orElseThrow(() -> new BadCredentialsException("No authenticated user"));

    String newToken = jwtTokenProvider.generateToken(currentUser);

    log.debug("Token refreshed for user: {}", currentUser.getUsername());

    return AuthenticationResult.builder()
        .token(newToken)
        .userId(currentUser.getUserId())
        .username(currentUser.getUsername())
        .tenantId(currentUser.getTenantId())
        .expiresAt(
            jwtTokenProvider
                .getExpirationDateFromToken(newToken)
                .toInstant()
                .atZone(java.time.ZoneId.systemDefault())
                .toLocalDateTime())
        .success(true)
        .build();
  }

  /** Validate user credentials using database lookup. */
  private User validateCredentials(String username, String password) {
    log.debug("Validating credentials for username: {}", username);

    // Look up user by username
    Optional<User> userOptional = userRepository.findByUsername(username);
    if (userOptional.isEmpty()) {
      log.warn("User not found: {}", username);
      throw new BadCredentialsException("Invalid username or password");
    }

    User user = userOptional.get();

    // Validate user data integrity (including password hash)
    try {
      User.validateUser(user);
    } catch (IllegalArgumentException ex) {
      log.error("User data validation failed for user: {} - {}", username, ex.getMessage());
      throw new BadCredentialsException("User account has invalid data");
    }

    // Check user status
    if (!user.isActive()) {
      log.warn("Inactive user login attempt: {}", username);
      throw new BadCredentialsException("User account is not active");
    }

    // Verify password
    if (!passwordEncoder.matches(password, user.getPasswordHash())) {
      log.warn("Invalid password for user: {}", username);
      throw new BadCredentialsException("Invalid username or password");
    }

    log.debug(
        "Successfully validated credentials for user: {} (tenant: {})",
        username,
        user.getTenantId());
    return user;
  }

  /** Get current authenticated user's username. */
  private Optional<String> getCurrentUsername() {
    return getCurrentUserPrincipal().map(UserPrincipal::getUsername);
  }

  /** Get current UserPrincipal from security context. */
  private Optional<UserPrincipal> getCurrentUserPrincipal() {
    var authentication = SecurityContextHolder.getContext().getAuthentication();

    if (authentication == null || !authentication.isAuthenticated()) {
      return Optional.empty();
    }

    Object principal = authentication.getPrincipal();
    if (principal instanceof UserPrincipal userPrincipal) {
      return Optional.of(userPrincipal);
    }

    return Optional.empty();
  }

  /** Login request data transfer object. */
  @Value
  @lombok.Builder
  public static class LoginRequest {
    String username;
    String password;
    String tenantDomain; // Optional: for tenant identification in multi-tenant setups
  }

  /** Authentication result data transfer object. */
  @Value
  @lombok.Builder
  public static class AuthenticationResult {
    String token;
    UUID userId;
    String username;
    Long tenantId;
    LocalDateTime expiresAt;
    boolean success;
  }
}
