package com.avantiq.billing.application.security;

import com.avantiq.billing.domain.security.model.UserPrincipal;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

/**
 * Service for tenant-based security validation. Verifies user access to specific tenants and
 * enforces tenant isolation.
 */
@Service
@Slf4j
public class TenantSecurityService {

  /** Check if the current user can access the specified tenant. */
  public boolean canAccessTenant(Long tenantId) {
    return getCurrentUserPrincipal().map(user -> user.getTenantId().equals(tenantId)).orElse(false);
  }

  /** Get the current user's tenant ID. */
  public Optional<Long> getCurrentTenantId() {
    return getCurrentUserPrincipal().map(UserPrincipal::getTenantId);
  }

  /**
   * Verify that the current user belongs to the specified tenant. Throws SecurityException if
   * access is denied.
   */
  public void verifyTenantAccess(Long tenantId) {
    if (!canAccessTenant(tenantId)) {
      String currentUser = getCurrentUsername().orElse("unknown");
      Long currentTenant = getCurrentTenantId().orElse(null);

      log.warn(
          "Tenant access denied: user {} from tenant {} attempted to access tenant {}",
          currentUser,
          currentTenant,
          tenantId);

      throw new SecurityException("Access denied to tenant: " + tenantId);
    }
  }

  /** Get the current authenticated user's username. */
  public Optional<String> getCurrentUsername() {
    return getCurrentUserPrincipal().map(UserPrincipal::getUsername);
  }

  /** Check if the current user is a tenant admin. */
  public boolean isCurrentUserTenantAdmin() {
    return getCurrentUserPrincipal()
        .map(user -> user.hasRole(com.avantiq.billing.domain.security.model.Role.TENANT_ADMIN))
        .orElse(false);
  }

  /** Get the current UserPrincipal from Spring Security context. */
  private Optional<UserPrincipal> getCurrentUserPrincipal() {
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

    if (authentication == null || !authentication.isAuthenticated()) {
      return Optional.empty();
    }

    Object principal = authentication.getPrincipal();
    if (principal instanceof UserPrincipal userPrincipal) {
      return Optional.of(userPrincipal);
    }

    return Optional.empty();
  }

  /**
   * Ensure the current request has a valid tenant context. Used for operations that require tenant
   * isolation.
   */
  public Long requireCurrentTenantId() {
    return getCurrentTenantId()
        .orElseThrow(() -> new SecurityException("No tenant context available"));
  }
}
