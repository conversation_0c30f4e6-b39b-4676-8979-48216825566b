package com.avantiq.billing.application.product;

import com.avantiq.billing.domain.product.model.ProductFamily;
import com.avantiq.billing.domain.product.service.interfaces.ProductFamilyService;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ProductFamilyApplicationService {

  private final ProductFamilyService productFamilyService;

  @Autowired
  public ProductFamilyApplicationService(ProductFamilyService productFamilyService) {
    this.productFamilyService = productFamilyService;
  }

  public ProductFamily createProductFamily(ProductFamily productFamily) {
    // Delegate to the domain service for business logic
    return productFamilyService.createProductFamily(productFamily);
  }

  public ProductFamily updateProductFamily(ProductFamily productFamily) {
    // Delegate to the domain service for updating product family logic
    return productFamilyService.updateProductFamily(productFamily);
  }

  public void deleteProductFamily(String productFamilyId) {
    // Delegate to the domain service for deletion logic
    productFamilyService.deleteProductFamily(productFamilyId);
  }

  public Optional<ProductFamily> getProductFamilyById(String productFamilyId) {
    // Delegate to the domain service for retrieval logic
    return productFamilyService.getProductFamilyById(productFamilyId);
  }

  public List<ProductFamily> listAllProductFamilies() {
    // Delegate to the domain service for listing logic
    return productFamilyService.listAllProductFamilies();
  }
}
