package com.avantiq.billing.application.product;

import com.avantiq.billing.domain.product.model.Price;
import com.avantiq.billing.domain.product.service.interfaces.PriceService;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PriceApplicationService {

  private final PriceService priceService;

  @Autowired
  public PriceApplicationService(PriceService priceService) {
    this.priceService = priceService;
  }

  public Price createPrice(Price price) {
    // Delegate to the domain service for business logic
    return priceService.createPrice(price);
  }

  public Price updatePrice(Price price) {
    // Delegate to the domain service for updating price logic
    return priceService.updatePrice(price);
  }

  public void deletePrice(String priceId) {
    // Delegate to the domain service for deletion logic
    priceService.deletePrice(priceId);
  }

  public Optional<Price> getPriceById(String priceId) {
    // Delegate to the domain service for retrieval logic
    return priceService.getPriceById(priceId);
  }

  public List<Price> listAllPrices() {
    // Delegate to the domain service for listing logic
    return priceService.listAllPrices();
  }

  public List<Price> listPricesByProductId(String productId) {
    // Delegate to the domain service for listing prices by product ID logic
    return priceService.listPricesByProductId(productId);
  }

  public List<Price> listPricesByPriceBookId(String priceBookId) {
    // Delegate to the domain service for listing prices by price book ID logic
    return priceService.listPricesByPriceBookId(priceBookId);
  }
}
