package com.avantiq.billing.application.product;

import com.avantiq.billing.domain.product.model.PriceBook;
import com.avantiq.billing.domain.product.service.interfaces.PriceBookService;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PriceBookApplicationService {

  private final PriceBookService priceBookService;

  @Autowired
  public PriceBookApplicationService(PriceBookService priceBookService) {
    this.priceBookService = priceBookService;
  }

  public PriceBook createPriceBook(PriceBook priceBook) {
    // Delegate to the domain service for business logic
    return priceBookService.createPriceBook(priceBook);
  }

  public PriceBook updatePriceBook(PriceBook priceBook) {
    // Delegate to the domain service for updating price book logic
    return priceBookService.updatePriceBook(priceBook);
  }

  public void deletePriceBook(String priceBookId) {
    // Delegate to the domain service for deletion logic
    priceBookService.deletePriceBook(priceBookId);
  }

  public Optional<PriceBook> getPriceBookById(String priceBookId) {
    // Delegate to the domain service for retrieval logic
    return priceBookService.getPriceBookById(priceBookId);
  }

  public List<PriceBook> listAllPriceBooks() {
    // Delegate to the domain service for listing logic
    return priceBookService.listAllPriceBooks();
  }
}
