package com.avantiq.billing.application.product;

import com.avantiq.billing.domain.product.model.Product;
import com.avantiq.billing.domain.product.service.interfaces.ProductDomainService;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;

@Service
public class ProductApplicationService {

  private final ProductDomainService productDomainService;

  @Autowired
  public ProductApplicationService(ProductDomainService productDomainService) {
    this.productDomainService = productDomainService;
  }

  @PreAuthorize("hasRole('SEGMENT_ADMIN')")
  public Product createProduct(Product product) {
    // Validate the product first
    if (!productDomainService.validateProduct(product)) {
      throw new IllegalArgumentException("Invalid product data");
    }
    // Delegate to the domain service for business logic
    return productDomainService.createProduct(product);
  }

  @PreAuthorize("hasRole('SEGMENT_ADMIN')")
  public Product updateProduct(Product product) {
    // Validate the product first
    if (!productDomainService.validateProduct(product)) {
      throw new IllegalArgumentException("Invalid product data");
    }
    // Delegate to the domain service for updating product logic
    return productDomainService.updateProduct(product);
  }

  @PreAuthorize("hasRole('TENANT_ADMIN')")
  public void deleteProduct(String productId) {
    // Delegate to the domain service for deletion logic
    productDomainService.deleteProduct(productId);
  }

  @PreAuthorize("hasRole('USER')")
  public Optional<Product> getProductById(String productId) {
    // Delegate to the domain service to retrieve product by ID
    return productDomainService.getProductById(productId);
  }

  @PreAuthorize("hasRole('USER')")
  public List<Product> listAllProducts() {
    // Delegate to the domain service to list all products
    return productDomainService.listAllProducts();
  }
}
