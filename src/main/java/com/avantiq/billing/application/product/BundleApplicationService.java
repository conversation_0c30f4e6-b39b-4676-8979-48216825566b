package com.avantiq.billing.application.product;

import com.avantiq.billing.domain.product.model.Bundle;
import com.avantiq.billing.domain.product.model.BundleProduct;
import com.avantiq.billing.domain.product.service.interfaces.BundleService;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class BundleApplicationService {

  private final BundleService bundleService;

  @Autowired
  public BundleApplicationService(BundleService bundleService) {
    this.bundleService = bundleService;
  }

  public Bundle createBundle(Bundle bundle) {
    // Delegate to the domain service for business logic
    return bundleService.createBundle(bundle);
  }

  public Bundle updateBundle(Bundle bundle) {
    // Delegate to the domain service for updating bundle logic
    return bundleService.updateBundle(bundle);
  }

  public void deleteBundle(String bundleId) {
    // Delegate to the domain service for deletion logic
    bundleService.deleteBundle(bundleId);
  }

  public Optional<Bundle> getBundleById(String bundleId) {
    // Delegate to the domain service for retrieval logic
    return bundleService.getBundleById(bundleId);
  }

  public List<Bundle> listAllBundles() {
    // Delegate to the domain service for listing logic
    return bundleService.listAllBundles();
  }

  public BundleProduct addProductToBundle(
      String bundleId, String productId, int quantity, boolean optionalFlag) {
    // Delegate to the domain service for adding product to bundle logic
    return bundleService.addProductToBundle(bundleId, productId, quantity, optionalFlag);
  }

  public void removeProductFromBundle(String bundleProductId) {
    // Delegate to the domain service for removing product from bundle logic
    bundleService.removeProductFromBundle(bundleProductId);
  }
}
