package com.avantiq.billing.application.exception;

/** Exception thrown when an operation cannot be performed due to business rules. */
public class InvalidOperationException extends ApplicationException {

  public InvalidOperationException(String message) {
    super(message, "INVALID_OPERATION");
  }

  public InvalidOperationException(String message, Throwable cause) {
    super(message, "INVALID_OPERATION", cause);
  }
}
