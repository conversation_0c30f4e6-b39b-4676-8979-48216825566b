package com.avantiq.billing.application.exception;

/** Exception thrown when a requested resource cannot be found. */
public class ResourceNotFoundException extends ApplicationException {

  public ResourceNotFoundException(String resourceType, String identifier) {
    super(
        String.format("%s not found with identifier: %s", resourceType, identifier),
        "RESOURCE_NOT_FOUND");
  }

  public ResourceNotFoundException(String message) {
    super(message, "RESOURCE_NOT_FOUND");
  }
}
