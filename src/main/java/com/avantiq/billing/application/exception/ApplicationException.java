package com.avantiq.billing.application.exception;

/**
 * Base exception for all application-level exceptions. These represent errors in the application
 * layer (use case failures).
 */
public class ApplicationException extends RuntimeException {

  private final String errorCode;

  public ApplicationException(String message, String errorCode) {
    super(message);
    this.errorCode = errorCode;
  }

  public ApplicationException(String message, String errorCode, Throwable cause) {
    super(message, cause);
    this.errorCode = errorCode;
  }

  public String getErrorCode() {
    return errorCode;
  }
}
