package com.avantiq.billing.application.customer;

import com.avantiq.billing.application.exception.InvalidOperationException;
import com.avantiq.billing.application.exception.ResourceAlreadyExistsException;
import com.avantiq.billing.application.exception.ResourceNotFoundException;
import com.avantiq.billing.domain.customer.model.Address;
import com.avantiq.billing.domain.customer.model.Customer;
import com.avantiq.billing.domain.customer.repository.CustomerRepository;
import com.avantiq.billing.domain.customer.service.interfaces.CustomerDomainService;
import com.avantiq.billing.infrastructure.security.context.TenantContextService;
import java.util.Optional;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Application service for customer-related use cases. This orchestrates domain objects and services
 * to fulfill business use cases.
 */
@Service
@Slf4j
public class CustomerApplicationService {
  private final CustomerRepository customerRepository;
  private final CustomerDomainService customerDomainService;
  private final TenantContextService tenantContextService;

  private static final String CUSTOMER_NOT_FOUND = "Customer with ID {} not found";
  private static final String ERROR_IN_METHOD = "Error in {}";
  private static final String INVALID_CUSTOMER_DATA = "Invalid customer data";
  private static final String CUSTOMER_EXISTS = "Customer with email {} already exists";

  @Autowired
  public CustomerApplicationService(
      CustomerRepository customerRepository,
      CustomerDomainService customerDomainService,
      TenantContextService tenantContextService) {
    this.customerRepository = customerRepository;
    this.customerDomainService = customerDomainService;
    this.tenantContextService = tenantContextService;
  }

  /**
   * Find a customer by ID.
   *
   * @param id the customer ID
   * @return an Optional containing the customer if found, or empty if not found
   */
  @PreAuthorize("hasRole('USER')")
  public Optional<Customer> findById(UUID id) {
    if (id == null) {
      throw new IllegalArgumentException("Customer ID cannot be null");
    }

    Long tenantId = tenantContextService.getCurrentTenantId();
    return customerRepository.findByIdAndTenantId(id, tenantId);
  }

  /**
   * Find a customer by ID with addresses eagerly loaded.
   *
   * @param id the customer ID
   * @return an Optional containing the customer if found, or empty if not found
   */
  public Optional<Customer> findByIdWithAddresses(UUID id) {
    if (id == null) {
      throw new IllegalArgumentException("Customer ID cannot be null");
    }

    Long tenantId = tenantContextService.getCurrentTenantId();
    return customerRepository.findByIdWithAddressesAndTenantId(id, tenantId);
  }

  /**
   * Find a customer by email.
   *
   * @param email the customer email
   * @return an Optional containing the customer if found, or empty if not found
   */
  public Optional<Customer> findByEmail(String email) {
    if (email == null || email.trim().isEmpty()) {
      throw new IllegalArgumentException("Customer email cannot be null or empty");
    }

    Long tenantId = tenantContextService.getCurrentTenantId();
    return customerRepository.findByEmailAndTenantId(email, tenantId);
  }

  /**
   * Find a customer by email with addresses eagerly loaded.
   *
   * @param email the customer email
   * @return an Optional containing the customer if found, or empty if not found
   */
  public Optional<Customer> findByEmailWithAddresses(String email) {
    if (email == null || email.trim().isEmpty()) {
      throw new IllegalArgumentException("Customer email cannot be null or empty");
    }

    Long tenantId = tenantContextService.getCurrentTenantId();
    return customerRepository.findByEmailWithAddressesAndTenantId(email, tenantId);
  }

  /**
   * Create a new customer.
   *
   * @param customer the customer to create
   * @return the created customer
   * @throws IllegalArgumentException if customer data is invalid
   * @throws ResourceAlreadyExistsException if customer with same email already exists
   */
  @PreAuthorize("hasRole('USER')")
  @Transactional
  public Customer createCustomer(Customer customer) {
    if (customer == null) {
      throw new IllegalArgumentException("Customer cannot be null");
    }

    if (!customerDomainService.validateCustomer(customer)) {
      throw new IllegalArgumentException(INVALID_CUSTOMER_DATA);
    }

    // Check if customer with same email already exists in the same tenant
    Long tenantId = tenantContextService.getCurrentTenantId();
    if (customerRepository.existsByEmailAndTenantId(customer.getEmail(), tenantId)) {
      throw new ResourceAlreadyExistsException("Customer", customer.getEmail());
    }

    // Set tenant ID on the customer before saving
    customer.setTenantId(tenantId);

    Customer savedCustomer = customerRepository.save(customer);
    log.info("Created customer with ID: {} for tenant: {}", savedCustomer.getId(), tenantId);
    return savedCustomer;
  }

  /**
   * Update an existing customer.
   *
   * @param id the customer ID
   * @param customerData the updated customer data
   * @return the updated customer
   * @throws IllegalArgumentException if customer data is invalid
   * @throws ResourceNotFoundException if customer not found
   */
  @PreAuthorize("hasRole('USER')")
  @Transactional
  public Customer updateCustomer(UUID id, Customer customerData) {
    if (id == null) {
      throw new IllegalArgumentException("Customer ID cannot be null");
    }
    if (customerData == null) {
      throw new IllegalArgumentException("Customer data cannot be null");
    }

    Long tenantId = tenantContextService.getCurrentTenantId();
    Optional<Customer> optionalCustomer = customerRepository.findByIdAndTenantId(id, tenantId);

    Customer customer =
        optionalCustomer.orElseThrow(
            () -> new ResourceNotFoundException("Customer", id.toString()));

    customer.updateDetails(
        customerData.getFirstName(),
        customerData.getLastName(),
        customerData.getEmail(),
        customerData.getCompanyName(),
        customerData.getVatNumber(),
        customerData.getCountry());

    if (!customerDomainService.validateCustomer(customer)) {
      throw new IllegalArgumentException(INVALID_CUSTOMER_DATA);
    }

    Customer updatedCustomer = customerRepository.save(customer);
    log.info("Updated customer with ID: {} for tenant: {}", id, tenantId);
    return updatedCustomer;
  }

  /**
   * Add an address to a customer.
   *
   * @param customerId the customer ID
   * @param address the address to add
   * @return the updated customer
   * @throws IllegalArgumentException if parameters are invalid
   * @throws ResourceNotFoundException if customer not found
   */
  @Transactional
  public Customer addAddress(UUID customerId, Address address) {
    if (customerId == null) {
      throw new IllegalArgumentException("Customer ID cannot be null");
    }
    if (address == null) {
      throw new IllegalArgumentException("Address cannot be null");
    }

    Long tenantId = tenantContextService.getCurrentTenantId();
    Optional<Customer> optionalCustomer =
        customerRepository.findByIdAndTenantId(customerId, tenantId);

    Customer customer =
        optionalCustomer.orElseThrow(
            () -> new ResourceNotFoundException("Customer", customerId.toString()));

    customerDomainService.addAddress(customer, address);
    Customer updatedCustomer = customerRepository.save(customer);
    log.info("Added address to customer ID: {} for tenant: {}", customerId, tenantId);
    return updatedCustomer;
  }

  /**
   * Change a customer's segment.
   *
   * @param customerId the customer ID
   * @param segmentId the new segment ID
   * @return the updated customer
   * @throws IllegalArgumentException if parameters are invalid
   * @throws ResourceNotFoundException if customer not found
   */
  @PreAuthorize("hasRole('SEGMENT_ADMIN')")
  @Transactional
  public Customer changeSegment(UUID customerId, UUID segmentId) {
    if (customerId == null) {
      throw new IllegalArgumentException("Customer ID cannot be null");
    }
    if (segmentId == null) {
      throw new IllegalArgumentException("Segment ID cannot be null");
    }

    Long tenantId = tenantContextService.getCurrentTenantId();
    Optional<Customer> optionalCustomer =
        customerRepository.findByIdAndTenantId(customerId, tenantId);

    Customer customer =
        optionalCustomer.orElseThrow(
            () -> new ResourceNotFoundException("Customer", customerId.toString()));

    customerDomainService.changeSegment(customer, segmentId);
    Customer updatedCustomer = customerRepository.save(customer);
    log.info(
        "Changed segment for customer ID: {} to segment: {} for tenant: {}",
        customerId,
        segmentId,
        tenantId);
    return updatedCustomer;
  }

  /**
   * Activate a customer.
   *
   * @param customerId the customer ID
   * @return the activated customer
   * @throws IllegalArgumentException if customer ID is invalid
   * @throws ResourceNotFoundException if customer not found
   * @throws InvalidOperationException if customer cannot be activated
   */
  @Transactional
  public Customer activateCustomer(UUID customerId) {
    if (customerId == null) {
      throw new IllegalArgumentException("Customer ID cannot be null");
    }

    Long tenantId = tenantContextService.getCurrentTenantId();
    Optional<Customer> optionalCustomer =
        customerRepository.findByIdAndTenantId(customerId, tenantId);

    Customer customer =
        optionalCustomer.orElseThrow(
            () -> new ResourceNotFoundException("Customer", customerId.toString()));

    customerDomainService.activateCustomer(customer);
    Customer activatedCustomer = customerRepository.save(customer);
    log.info("Activated customer ID: {} for tenant: {}", customerId, tenantId);
    return activatedCustomer;
  }

  /**
   * Deactivate a customer.
   *
   * @param customerId the customer ID
   * @return the deactivated customer
   * @throws IllegalArgumentException if customer ID is invalid
   * @throws ResourceNotFoundException if customer not found
   * @throws InvalidOperationException if customer cannot be deactivated
   */
  @Transactional
  public Customer deactivateCustomer(UUID customerId) {
    if (customerId == null) {
      throw new IllegalArgumentException("Customer ID cannot be null");
    }

    Long tenantId = tenantContextService.getCurrentTenantId();
    Optional<Customer> optionalCustomer =
        customerRepository.findByIdAndTenantId(customerId, tenantId);

    Customer customer =
        optionalCustomer.orElseThrow(
            () -> new ResourceNotFoundException("Customer", customerId.toString()));

    customerDomainService.deactivateCustomer(customer);
    Customer deactivatedCustomer = customerRepository.save(customer);
    log.info("Deactivated customer ID: {} for tenant: {}", customerId, tenantId);
    return deactivatedCustomer;
  }

  /**
   * Check if a customer exists by email.
   *
   * @param email the customer email
   * @return true if a customer with the email exists, false otherwise
   */
  public boolean existsByEmail(String email) {
    try {
      Long tenantId = tenantContextService.getCurrentTenantId();
      return customerRepository.existsByEmailAndTenantId(email, tenantId);
    } catch (Exception e) {
      if (log.isErrorEnabled()) {
        log.error(ERROR_IN_METHOD, "existsByEmail", e);
      }
      return false;
    }
  }
}
