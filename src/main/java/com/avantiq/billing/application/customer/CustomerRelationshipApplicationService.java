package com.avantiq.billing.application.customer;

import com.avantiq.billing.domain.customer.service.interfaces.CustomerRelationshipService;
import com.avantiq.billing.infrastructure.security.context.TenantContextService;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Application service for customer relationship-related use cases. This orchestrates domain objects
 * and services to fulfill business use cases.
 */
@Service
@Slf4j
public class CustomerRelationshipApplicationService {
  private final CustomerRelationshipService customerRelationshipService;
  private final TenantContextService tenantContextService;

  @Autowired
  public CustomerRelationshipApplicationService(
      CustomerRelationshipService customerRelationshipService,
      TenantContextService tenantContextService) {
    this.customerRelationshipService = customerRelationshipService;
    this.tenantContextService = tenantContextService;
  }

  /**
   * Link a relationship between two customers.
   *
   * @param parentId the parent customer ID
   * @param childId the child customer ID
   * @param relationshipType the type of relationship
   * @throws IllegalArgumentException if parameters are invalid
   */
  @Transactional
  public void linkRelationship(UUID parentId, UUID childId, String relationshipType) {
    if (parentId == null) {
      throw new IllegalArgumentException("Parent customer ID cannot be null");
    }
    if (childId == null) {
      throw new IllegalArgumentException("Child customer ID cannot be null");
    }
    if (relationshipType == null || relationshipType.trim().isEmpty()) {
      throw new IllegalArgumentException("Relationship type cannot be null or empty");
    }

    Long tenantId = tenantContextService.getCurrentTenantId();
    customerRelationshipService.linkRelationship(parentId, childId, relationshipType, tenantId);
    log.info(
        "Linked relationship between parent {} and child {} with type {} in tenant {}",
        parentId,
        childId,
        relationshipType,
        tenantId);
  }
}
