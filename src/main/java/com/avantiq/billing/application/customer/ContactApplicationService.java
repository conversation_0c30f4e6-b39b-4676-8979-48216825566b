package com.avantiq.billing.application.customer;

import com.avantiq.billing.domain.customer.model.Contact;
import com.avantiq.billing.domain.customer.service.interfaces.ContactService;
import com.avantiq.billing.infrastructure.security.context.TenantContextService;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Application service for contact-related use cases. This orchestrates domain objects and services
 * to fulfill business use cases.
 */
@Service
@Slf4j
public class ContactApplicationService {
  private final ContactService contactService;
  private final TenantContextService tenantContextService;

  @Autowired
  public ContactApplicationService(
      ContactService contactService, TenantContextService tenantContextService) {
    this.contactService = contactService;
    this.tenantContextService = tenantContextService;
  }

  /**
   * Add a contact to a customer.
   *
   * @param customerId the customer ID
   * @param firstName the contact's first name
   * @param lastName the contact's last name
   * @param email the contact's email
   * @param contactType the type of contact
   * @param isDefault whether this is the default contact
   * @throws IllegalArgumentException if parameters are invalid
   */
  @Transactional
  public void addContact(
      UUID customerId,
      String firstName,
      String lastName,
      String email,
      String contactType,
      boolean isDefault) {
    if (customerId == null) {
      throw new IllegalArgumentException("Customer ID cannot be null");
    }

    Long tenantId = tenantContextService.getCurrentTenantId();
    contactService.addContact(customerId, firstName, lastName, email, contactType, isDefault);
    log.info("Added contact for customer ID: {} in tenant: {}", customerId, tenantId);
  }

  /**
   * Update an existing contact.
   *
   * @param contactId the contact ID
   * @param firstName the contact's first name
   * @param lastName the contact's last name
   * @param email the contact's email
   * @param contactType the type of contact
   * @param isDefault whether this is the default contact
   * @throws IllegalArgumentException if parameters are invalid
   */
  @Transactional
  public void updateContact(
      UUID contactId,
      String firstName,
      String lastName,
      String email,
      String contactType,
      boolean isDefault) {
    if (contactId == null) {
      throw new IllegalArgumentException("Contact ID cannot be null");
    }

    Long tenantId = tenantContextService.getCurrentTenantId();
    contactService.updateContact(contactId, firstName, lastName, email, contactType, isDefault);
    log.info("Updated contact ID: {} in tenant: {}", contactId, tenantId);
  }

  /**
   * List all contacts for a customer.
   *
   * @param customerId the customer ID
   * @return list of contacts
   * @throws IllegalArgumentException if customer ID is invalid
   */
  public List<Contact> listContacts(UUID customerId) {
    if (customerId == null) {
      throw new IllegalArgumentException("Customer ID cannot be null");
    }

    return contactService.listContacts(customerId);
  }

  /**
   * Find a contact by ID.
   *
   * @param contactId the contact ID
   * @return optional contact
   * @throws IllegalArgumentException if contact ID is invalid
   */
  public Optional<Contact> findById(UUID contactId) {
    if (contactId == null) {
      throw new IllegalArgumentException("Contact ID cannot be null");
    }

    return contactService.findById(contactId);
  }

  /**
   * Save a contact.
   *
   * @param contact the contact to save
   * @return the saved contact
   * @throws IllegalArgumentException if contact is invalid
   */
  @Transactional
  public Contact save(Contact contact) {
    if (contact == null) {
      throw new IllegalArgumentException("Contact cannot be null");
    }

    Long tenantId = tenantContextService.getCurrentTenantId();
    Contact savedContact = contactService.save(contact);
    log.info("Saved contact ID: {} in tenant: {}", savedContact.getId(), tenantId);
    return savedContact;
  }
}
