package com.avantiq.billing.application.customer;

import com.avantiq.billing.domain.customer.model.CustomerSegment;
import com.avantiq.billing.domain.customer.service.interfaces.CustomerSegmentService;
import com.avantiq.billing.infrastructure.security.context.TenantContextService;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Application service for customer segment-related use cases. This orchestrates domain objects and
 * services to fulfill business use cases.
 */
@Service
@Slf4j
public class CustomerSegmentApplicationService {
  private final CustomerSegmentService customerSegmentService;
  private final TenantContextService tenantContextService;

  @Autowired
  public CustomerSegmentApplicationService(
      CustomerSegmentService customerSegmentService, TenantContextService tenantContextService) {
    this.customerSegmentService = customerSegmentService;
    this.tenantContextService = tenantContextService;
  }

  /**
   * List all customer segments.
   *
   * @return list of customer segments
   */
  public List<CustomerSegment> listSegments() {
    Long tenantId = tenantContextService.getCurrentTenantId();
    List<CustomerSegment> segments = customerSegmentService.listSegments();
    log.info("Listed {} segments for tenant: {}", segments.size(), tenantId);
    return segments;
  }

  /**
   * Save a customer segment.
   *
   * @param segment the segment to save
   * @return the saved segment
   * @throws IllegalArgumentException if segment is invalid
   */
  @Transactional
  public CustomerSegment save(CustomerSegment segment) {
    if (segment == null) {
      throw new IllegalArgumentException("Segment cannot be null");
    }

    Long tenantId = tenantContextService.getCurrentTenantId();
    CustomerSegment savedSegment = customerSegmentService.save(segment);
    log.info("Saved segment ID: {} in tenant: {}", savedSegment.getId(), tenantId);
    return savedSegment;
  }

  /**
   * Find a segment by ID.
   *
   * @param segmentId the segment ID
   * @return optional segment
   * @throws IllegalArgumentException if segment ID is invalid
   */
  public Optional<CustomerSegment> findById(UUID segmentId) {
    if (segmentId == null) {
      throw new IllegalArgumentException("Segment ID cannot be null");
    }

    return customerSegmentService.findById(segmentId);
  }
}
