-- Migration to remove deprecated charge-related columns
-- This migration drops the old charge columns after ensuring data has been migrated

-- Drop old columns that have been replaced
ALTER TABLE prices 
    DROP COLUMN IF EXISTS charge_type,
    DROP COLUMN IF EXISTS charge_strategy,
    DROP COLUMN IF EXISTS charge;

-- Ensure new columns have NOT NULL constraints
ALTER TABLE prices
    ALTER COLUMN price_type SET NOT NULL,
    ALTER COLUMN price_strategy SET NOT NULL;

-- Add comment to clarify the pricing column structure
COMMENT ON COLUMN prices.pricing IS 'JSONB column containing polymorphic pricing configuration based on price_strategy';