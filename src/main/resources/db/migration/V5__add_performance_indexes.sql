-- V5__add_performance_indexes.sql
-- Add performance indexes for frequently queried fields

-- =============================================================================
-- CUSTOMER INDEXES
-- =============================================================================

-- Index for customer queries by email and tenant
CREATE INDEX idx_customers_email_tenant ON customers(email, tenant_id);

-- Index for customer queries by segment and tenant
CREATE INDEX idx_customers_segment_tenant ON customers(segment_id, tenant_id);

-- Index for customer queries by status and tenant
CREATE INDEX idx_customers_status_tenant ON customers(status, tenant_id);

-- Index for customer addresses by customer and tenant
CREATE INDEX idx_customer_addresses_customer_tenant ON customer_addresses(customer_id, tenant_id);

-- =============================================================================
-- PRODUCT INDEXES
-- =============================================================================

-- Index for product queries by SKU and tenant
CREATE INDEX idx_products_sku_tenant ON products(sku, tenant_id);

-- Index for product queries by family and tenant
CREATE INDEX idx_products_family_tenant ON products(family_id, tenant_id);

-- Index for product queries by status and tenant
CREATE INDEX idx_products_status_tenant ON products(status, tenant_id);

-- Index for bundle queries by name and tenant
CREATE INDEX idx_bundles_name_tenant ON bundles(name, tenant_id);

-- Index for bundle products by bundle
CREATE INDEX idx_bundle_products_bundle ON bundle_products(bundle_id);

-- Index for price queries by product and price book
CREATE INDEX idx_prices_product_pricebook ON prices(product_id, price_book_id);

-- Index for price book queries by currency and tenant
CREATE INDEX idx_price_books_currency_tenant ON price_books(currency, tenant_id);

-- =============================================================================
-- PAYMENT INDEXES
-- =============================================================================

-- Index for payment method queries by customer and tenant
CREATE INDEX idx_payment_methods_customer_tenant ON payment_methods(customer_id, tenant_id);

-- Index for payment method queries by type and tenant
CREATE INDEX idx_payment_methods_type_tenant ON payment_methods(type, tenant_id);

-- =============================================================================
-- SECURITY INDEXES
-- =============================================================================

-- Index for user queries by username (already exists but ensure it's there)
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);

-- Index for user queries by email (already exists but ensure it's there)
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);

-- Index for user queries by tenant and status
CREATE INDEX idx_users_tenant_status ON users(tenant_id, status);

-- =============================================================================
-- AUDIT AND RELATIONSHIP INDEXES
-- =============================================================================

-- Index for customer notes by customer and tenant
CREATE INDEX idx_customer_notes_customer_tenant ON customer_notes(customer_id, tenant_id);

-- Index for customer relationships by parent and tenant
CREATE INDEX idx_customer_relationships_parent_tenant ON customer_relationships(parent_customer_id, tenant_id);

-- Index for customer relationships by child and tenant
CREATE INDEX idx_customer_relationships_child_tenant ON customer_relationships(child_customer_id, tenant_id);

-- Index for contact queries by customer
CREATE INDEX idx_contacts_customer ON contacts(customer_id);

-- =============================================================================
-- GENERAL PERFORMANCE INDEXES
-- =============================================================================

-- Index for all main entities by created_at for time-based queries
CREATE INDEX idx_customers_created_at ON customers(created_at DESC);
CREATE INDEX idx_products_created_at ON products(created_at DESC);
CREATE INDEX idx_payment_methods_created_at ON payment_methods(created_at DESC);

-- Index for all main entities by last_modified_at for sync operations
CREATE INDEX idx_customers_last_modified ON customers(last_modified_at DESC);
CREATE INDEX idx_products_last_modified ON products(last_modified_at DESC);
CREATE INDEX idx_payment_methods_last_modified ON payment_methods(last_modified_at DESC);