-- V1__initial_schema.sql
-- Initial database schema for Avantiq Billing System
-- Creates core billing domain tables with multi-tenant support

-- =============================================================================
-- TENANT MANAGEMENT TABLES
-- =============================================================================

CREATE TABLE tenants (
    tenant_id BIGSERIAL PRIMARY KEY,
    tenant_name VARCHAR(255) NOT NULL UNIQUE,
    tenant_domain VARCHAR(255) NOT NULL UNIQUE,
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    subscription_plan VARCHAR(50),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_tenants_domain ON tenants(tenant_domain);
CREATE INDEX idx_tenants_status ON tenants(status);

-- =============================================================================
-- CUSTOMER DOMAIN TABLES
-- =============================================================================

-- Customer segments for tenant-based grouping
CREATE TABLE customer_segments (
    segment_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id BIGINT NOT NULL REFERENCES tenants(tenant_id),
    segment_name VARCHAR(255) NOT NULL,
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, segment_name)
);

CREATE INDEX idx_customer_segments_tenant_id ON customer_segments(tenant_id);
CREATE INDEX idx_customer_segments_active ON customer_segments(is_active);

-- Main customers table with tenant isolation
CREATE TABLE customers (
    customer_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id BIGINT NOT NULL REFERENCES tenants(tenant_id),
    segment_id UUID REFERENCES customer_segments(segment_id),
    first_name VARCHAR(255) NOT NULL,
    last_name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    company_name VARCHAR(255),
    vat_number VARCHAR(50),
    country VARCHAR(3),
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    last_modified_by VARCHAR(255),
    version BIGINT NOT NULL DEFAULT 0,
    UNIQUE(tenant_id, email)
);

CREATE INDEX idx_customers_tenant_id ON customers(tenant_id);
CREATE INDEX idx_customers_segment_id ON customers(segment_id);
CREATE INDEX idx_customers_email ON customers(tenant_id, email);
CREATE INDEX idx_customers_status ON customers(status);
CREATE INDEX idx_customers_company ON customers(company_name);

-- Customer addresses
CREATE TABLE customer_addresses (
    address_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES customers(customer_id) ON DELETE CASCADE,
    address_type VARCHAR(20) NOT NULL DEFAULT 'BILLING',
    street_line1 VARCHAR(255) NOT NULL,
    street_line2 VARCHAR(255),
    city VARCHAR(255) NOT NULL,
    state_province VARCHAR(255),
    postal_code VARCHAR(20) NOT NULL,
    country VARCHAR(3) NOT NULL,
    is_primary BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_customer_addresses_customer_id ON customer_addresses(customer_id);
CREATE INDEX idx_customer_addresses_type ON customer_addresses(address_type);

-- Customer notes
CREATE TABLE customer_notes (
    note_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES customers(customer_id) ON DELETE CASCADE,
    note_text TEXT NOT NULL,
    note_type VARCHAR(50) DEFAULT 'GENERAL',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255)
);

CREATE INDEX idx_customer_notes_customer_id ON customer_notes(customer_id);
CREATE INDEX idx_customer_notes_type ON customer_notes(note_type);

-- Customer relationships (hierarchies)
CREATE TABLE customer_relationships (
    relationship_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    parent_customer_id UUID NOT NULL REFERENCES customers(customer_id),
    child_customer_id UUID NOT NULL REFERENCES customers(customer_id),
    relationship_type VARCHAR(50) NOT NULL DEFAULT 'SUBSIDIARY',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    UNIQUE(parent_customer_id, child_customer_id),
    CHECK (parent_customer_id != child_customer_id)
);

CREATE INDEX idx_customer_relationships_parent ON customer_relationships(parent_customer_id);
CREATE INDEX idx_customer_relationships_child ON customer_relationships(child_customer_id);

-- Customer contacts
CREATE TABLE contacts (
    contact_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES customers(customer_id) ON DELETE CASCADE,
    tenant_id BIGINT NOT NULL REFERENCES tenants(tenant_id),
    first_name VARCHAR(255) NOT NULL,
    last_name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    contact_type VARCHAR(50) NOT NULL DEFAULT 'PRIMARY',
    is_default BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_contacts_customer_id ON contacts(customer_id);
CREATE INDEX idx_contacts_tenant_id ON contacts(tenant_id);
CREATE INDEX idx_contacts_email ON contacts(tenant_id, email);
CREATE INDEX idx_contacts_type ON contacts(contact_type);

-- =============================================================================
-- PRODUCT DOMAIN TABLES
-- =============================================================================

-- Product families for organization
CREATE TABLE product_families (
    family_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id BIGINT NOT NULL REFERENCES tenants(tenant_id),
    family_name VARCHAR(255) NOT NULL,
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, family_name)
);

CREATE INDEX idx_product_families_tenant_id ON product_families(tenant_id);
CREATE INDEX idx_product_families_active ON product_families(is_active);

-- Main products table
CREATE TABLE products (
    product_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id BIGINT NOT NULL REFERENCES tenants(tenant_id),
    family_id UUID REFERENCES product_families(family_id),
    product_name VARCHAR(255) NOT NULL,
    sku VARCHAR(100),
    description TEXT,
    product_type VARCHAR(50) NOT NULL DEFAULT 'SERVICE',
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    last_modified_by VARCHAR(255),
    version BIGINT NOT NULL DEFAULT 0,
    UNIQUE(tenant_id, sku)
);

CREATE INDEX idx_products_tenant_id ON products(tenant_id);
CREATE INDEX idx_products_family_id ON products(family_id);
CREATE INDEX idx_products_sku ON products(tenant_id, sku);
CREATE INDEX idx_products_type ON products(product_type);
CREATE INDEX idx_products_status ON products(status);

-- Price books for tenant-specific pricing
CREATE TABLE price_books (
    price_book_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id BIGINT NOT NULL REFERENCES tenants(tenant_id),
    segment_id UUID REFERENCES customer_segments(segment_id),
    price_book_name VARCHAR(255) NOT NULL,
    description TEXT,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    is_active BOOLEAN NOT NULL DEFAULT true,
    effective_date DATE NOT NULL DEFAULT CURRENT_DATE,
    expiry_date DATE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, price_book_name)
);

CREATE INDEX idx_price_books_tenant_id ON price_books(tenant_id);
CREATE INDEX idx_price_books_segment_id ON price_books(segment_id);
CREATE INDEX idx_price_books_active ON price_books(is_active);
CREATE INDEX idx_price_books_dates ON price_books(effective_date, expiry_date);

-- Product prices
CREATE TABLE prices (
    price_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    price_book_id UUID NOT NULL REFERENCES price_books(price_book_id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(product_id),
    unit_price DECIMAL(19,4) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    effective_date DATE NOT NULL DEFAULT CURRENT_DATE,
    expiry_date DATE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(price_book_id, product_id, effective_date)
);

CREATE INDEX idx_prices_price_book_id ON prices(price_book_id);
CREATE INDEX idx_prices_product_id ON prices(product_id);
CREATE INDEX idx_prices_dates ON prices(effective_date, expiry_date);

-- Product bundles
CREATE TABLE bundles (
    bundle_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id BIGINT NOT NULL REFERENCES tenants(tenant_id),
    bundle_name VARCHAR(255) NOT NULL,
    description TEXT,
    bundle_type VARCHAR(50) NOT NULL DEFAULT 'STANDARD',
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, bundle_name)
);

CREATE INDEX idx_bundles_tenant_id ON bundles(tenant_id);
CREATE INDEX idx_bundles_type ON bundles(bundle_type);
CREATE INDEX idx_bundles_active ON bundles(is_active);

-- Bundle products (many-to-many)
CREATE TABLE bundle_products (
    bundle_product_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bundle_id UUID NOT NULL REFERENCES bundles(bundle_id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(product_id),
    quantity INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(bundle_id, product_id)
);

CREATE INDEX idx_bundle_products_bundle_id ON bundle_products(bundle_id);
CREATE INDEX idx_bundle_products_product_id ON bundle_products(product_id);

-- =============================================================================
-- PAYMENT DOMAIN TABLES
-- =============================================================================

-- Payment methods
CREATE TABLE payment_methods (
    payment_method_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES customers(customer_id) ON DELETE CASCADE,
    tenant_id BIGINT NOT NULL REFERENCES tenants(tenant_id),
    method_type VARCHAR(50) NOT NULL,
    is_primary BOOLEAN NOT NULL DEFAULT false,
    is_active BOOLEAN NOT NULL DEFAULT true,
    external_reference VARCHAR(255),
    metadata_json TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_payment_methods_customer_id ON payment_methods(customer_id);
CREATE INDEX idx_payment_methods_tenant_id ON payment_methods(tenant_id);
CREATE INDEX idx_payment_methods_type ON payment_methods(method_type);
CREATE INDEX idx_payment_methods_active ON payment_methods(is_active);

-- =============================================================================
-- DATA INTEGRITY CONSTRAINTS
-- =============================================================================

-- Ensure only one primary address per customer
CREATE UNIQUE INDEX idx_customer_primary_address 
ON customer_addresses(customer_id) 
WHERE is_primary = true;

-- Ensure only one primary payment method per customer
CREATE UNIQUE INDEX idx_customer_primary_payment_method 
ON payment_methods(customer_id) 
WHERE is_primary = true;

-- =============================================================================
-- INITIAL DATA
-- =============================================================================

-- Insert default tenant
INSERT INTO tenants (tenant_id, tenant_name, tenant_domain, status, subscription_plan)
VALUES (1, 'Default Tenant', 'default.local', 'ACTIVE', 'ENTERPRISE');

-- Insert default customer segments
INSERT INTO customer_segments (segment_id, tenant_id, segment_name, description)
VALUES 
    ('550e8400-e29b-41d4-a716-446655440001', 1, 'US', 'United States market segment'),
    ('550e8400-e29b-41d4-a716-446655440002', 1, 'EMEA', 'Europe, Middle East, and Africa segment'),
    ('550e8400-e29b-41d4-a716-446655440003', 1, 'APAC', 'Asia-Pacific segment');

-- Insert default product family
INSERT INTO product_families (family_id, tenant_id, family_name, description)
VALUES ('550e8400-e29b-41d4-a716-446655440010', 1, 'Software Services', 'Software and cloud services');

-- Insert default price book
INSERT INTO price_books (price_book_id, tenant_id, price_book_name, description, currency)
VALUES ('550e8400-e29b-41d4-a716-446655440020', 1, 'Standard Pricing', 'Default pricing for all segments', 'USD');