-- V3__security_tables.sql
-- Security and user management tables for JWT authentication
-- Creates user, role, permission tables with multi-tenant support

-- =============================================================================
-- ROLES AND PERMISSIONS TABLES
-- =============================================================================

-- Roles table for role-based access control
CREATE TABLE roles (
    role_id BIGSERIAL PRIMARY KEY,
    name VARCHAR(30) NOT NULL UNIQUE,
    description VARCHAR(255),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_roles_name ON roles(name);

-- Permissions table for granular access control
CREATE TABLE permissions (
    permission_id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    description VARCHAR(255),
    resource VARCHAR(100),
    action VARCHAR(50),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_permissions_name ON permissions(name);

-- Role-Permission many-to-many mapping
CREATE TABLE role_permissions (
    role_permission_id BIGSERIAL PRIMARY KEY,
    role_id BIGINT NOT NULL REFERENCES roles(role_id) ON DELETE CASCADE,
    permission_id BIGINT NOT NULL REFERENCES permissions(permission_id) ON DELETE CASCADE,
    granted_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(role_id, permission_id)
);

CREATE INDEX idx_role_permissions_role_id ON role_permissions(role_id);
CREATE INDEX idx_role_permissions_permission_id ON role_permissions(permission_id);

-- =============================================================================
-- USER MANAGEMENT TABLES
-- =============================================================================

-- Users table with tenant association
CREATE TABLE users (
    user_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    tenant_id BIGINT NOT NULL REFERENCES tenants(tenant_id),
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING_ACTIVATION',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    last_modified_by VARCHAR(255),
    version BIGINT NOT NULL DEFAULT 0
);

CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_tenant_id ON users(tenant_id);
CREATE INDEX idx_users_status ON users(status);

-- User-Role many-to-many mapping
CREATE TABLE user_roles (
    user_role_id BIGSERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    role_id BIGINT NOT NULL REFERENCES roles(role_id) ON DELETE CASCADE,
    assigned_by UUID REFERENCES users(user_id),
    assigned_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, role_id)
);

CREATE INDEX idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX idx_user_roles_role_id ON user_roles(role_id);

-- User-Segment many-to-many mapping for segment-based access control
CREATE TABLE user_segments (
    user_segment_id BIGSERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    segment_id UUID NOT NULL REFERENCES customer_segments(segment_id) ON DELETE CASCADE,
    tenant_id BIGINT NOT NULL REFERENCES tenants(tenant_id),
    assigned_by UUID REFERENCES users(user_id),
    assigned_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, segment_id)
);

CREATE INDEX idx_user_segments_user_id ON user_segments(user_id);
CREATE INDEX idx_user_segments_segment_id ON user_segments(segment_id);
CREATE INDEX idx_user_segments_tenant_id ON user_segments(tenant_id);

-- =============================================================================
-- INITIAL SECURITY DATA
-- =============================================================================

-- Insert default roles
INSERT INTO roles (name, description) VALUES
('TENANT_ADMIN', 'Full access to all tenant data and segments'),
('SEGMENT_ADMIN', 'Access to specific segments within tenant'),
('USER', 'Read/write access to assigned segments'),
('READONLY', 'Read-only access to assigned segments');

-- Insert default permissions
INSERT INTO permissions (name, description, resource, action) VALUES
('READ_ALL_TENANT_DATA', 'Read access to all data within tenant', 'TENANT', 'READ'),
('WRITE_ALL_TENANT_DATA', 'Write access to all data within tenant', 'TENANT', 'WRITE'),
('READ_SEGMENT_DATA', 'Read access to specific segment data', 'SEGMENT', 'READ'),
('WRITE_SEGMENT_DATA', 'Write access to specific segment data', 'SEGMENT', 'WRITE'),
('MANAGE_USERS', 'Manage all users within tenant', 'USER', 'MANAGE'),
('MANAGE_SEGMENT_USERS', 'Manage users within specific segments', 'USER', 'MANAGE_SEGMENT'),
('MANAGE_SEGMENTS', 'Manage segments within tenant', 'SEGMENT', 'MANAGE');

-- Map roles to permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.role_id, p.permission_id
FROM roles r, permissions p
WHERE (r.name = 'TENANT_ADMIN' AND p.name IN ('READ_ALL_TENANT_DATA', 'WRITE_ALL_TENANT_DATA', 'MANAGE_USERS', 'MANAGE_SEGMENTS'))
   OR (r.name = 'SEGMENT_ADMIN' AND p.name IN ('READ_SEGMENT_DATA', 'WRITE_SEGMENT_DATA', 'MANAGE_SEGMENT_USERS'))
   OR (r.name = 'USER' AND p.name IN ('READ_SEGMENT_DATA', 'WRITE_SEGMENT_DATA'))
   OR (r.name = 'READONLY' AND p.name IN ('READ_SEGMENT_DATA'));

-- Default admin user creation is disabled for security.
-- Create admin users through the API or direct database operations in production.
-- Example admin user creation command (run manually with secure password):
-- INSERT INTO users (username, email, password_hash, tenant_id, status, created_by)
-- VALUES ('admin', '<EMAIL>', '<secure-bcrypt-hash>', 1, 'ACTIVE', 'SYSTEM');

-- Default admin user role and segment assignments are disabled for security.
-- After manually creating an admin user, assign roles and segments using these example commands:
--
-- Assign TENANT_ADMIN role to admin user:
-- INSERT INTO user_roles (user_id, role_id)
-- SELECT '<admin-user-uuid>', role_id FROM roles WHERE name = 'TENANT_ADMIN';
--
-- Assign segments to admin user:
-- INSERT INTO user_segments (user_id, segment_id, tenant_id, assigned_by)
-- SELECT '<admin-user-uuid>', segment_id, tenant_id, '<admin-user-uuid>'
-- FROM customer_segments WHERE tenant_id = <tenant-id>;

-- =============================================================================
-- ADDITIONAL CONSTRAINTS AND FUNCTIONS
-- =============================================================================

-- Function to automatically update last_modified_at
CREATE OR REPLACE FUNCTION update_last_modified_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.last_modified_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for automatic last_modified_at updates
CREATE TRIGGER trigger_users_last_modified_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_last_modified_at();

CREATE TRIGGER trigger_roles_last_modified_at
    BEFORE UPDATE ON roles
    FOR EACH ROW
    EXECUTE FUNCTION update_last_modified_at();

CREATE TRIGGER trigger_permissions_last_modified_at
    BEFORE UPDATE ON permissions
    FOR EACH ROW
    EXECUTE FUNCTION update_last_modified_at();

-- Add constraint to ensure users can only be assigned to segments within their tenant
ALTER TABLE user_segments 
ADD CONSTRAINT fk_user_segments_tenant_match 
CHECK (
    tenant_id = (SELECT tenant_id FROM users WHERE user_id = user_segments.user_id)
);