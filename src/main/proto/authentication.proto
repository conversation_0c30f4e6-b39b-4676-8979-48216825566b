syntax = "proto3";

option java_package = "com.avantiq.billing.infrastructure.grpc.proto";
option java_outer_classname = "AuthenticationProto";
option java_multiple_files = true;

package avantiq.billing.authentication;

/**
 * Authentication service for JWT-based login and user management.
 */
service AuthenticationService {
  // Authenticate user and return JWT token
  rpc Login(LoginRequest) returns (LoginResponse);
  
  // Refresh JWT token
  rpc RefreshToken(RefreshTokenRequest) returns (LoginResponse);
  
  // Logout user
  rpc Logout(LogoutRequest) returns (LogoutResponse);
  
  // Validate JWT token
  rpc ValidateToken(ValidateTokenRequest) returns (ValidateTokenResponse);
}

/**
 * Login request message.
 */
message LoginRequest {
  string username = 1;
  string password = 2;
  optional string tenant_domain = 3; // Optional tenant identification
}

/**
 * Login response message.
 */
message LoginResponse {
  bool success = 1;
  string message = 2;
  optional AuthenticationData auth_data = 3;
}

/**
 * Authentication data containing user info and token.
 */
message AuthenticationData {
  string token = 1;
  string user_id = 2;
  string username = 3;
  int64 tenant_id = 4;
  repeated string roles = 5;
  repeated string assigned_segments = 6;
  int64 expires_at = 7; // Unix timestamp
}

/**
 * Refresh token request message.
 */
message RefreshTokenRequest {
  // No body needed - uses current authenticated user context
}

/**
 * Logout request message.
 */
message LogoutRequest {
  // No body needed - uses current authenticated user context
}

/**
 * Logout response message.
 */
message LogoutResponse {
  bool success = 1;
  string message = 2;
}

/**
 * Validate token request message.
 */
message ValidateTokenRequest {
  string token = 1;
}

/**
 * Validate token response message.
 */
message ValidateTokenResponse {
  bool valid = 1;
  string message = 2;
  optional AuthenticationData auth_data = 3;
}