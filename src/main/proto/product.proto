syntax = "proto3";

package com.avantiq.billing.product.grpc;

option java_multiple_files = true;
option java_package = "com.avantiq.billing.product.grpc";
option java_outer_classname = "ProductServiceProto";

service ProductService {
  rpc CreateProduct (CreateProductRequest) returns (ProductResponse);
  rpc GetProduct (GetProductRequest) returns (ProductResponse);
  rpc ListProducts (ListProductsRequest) returns (ListProductsResponse);
  rpc UpdateProduct (UpdateProductRequest) returns (ProductResponse);
  rpc DeleteProduct (DeleteProductRequest) returns (DeleteProductResponse);
}

service ProductFamilyService {
  rpc CreateProductFamily (CreateProductFamilyRequest) returns (ProductFamilyResponse);
  rpc GetProductFamily (GetProductFamilyRequest) returns (ProductFamilyResponse);
  rpc ListProductFamilies (ListProductFamiliesRequest) returns (ListProductFamiliesResponse);
  rpc UpdateProductFamily (UpdateProductFamilyRequest) returns (ProductFamilyResponse);
  rpc DeleteProductFamily (DeleteProductFamilyRequest) returns (DeleteProductFamilyResponse);
}

service BundleService {
  rpc CreateBundle (CreateBundleRequest) returns (BundleResponse);
  rpc GetBundle (GetBundleRequest) returns (BundleResponse);
  rpc ListBundles (ListBundlesRequest) returns (ListBundlesResponse);
  rpc UpdateBundle (UpdateBundleRequest) returns (BundleResponse);
  rpc DeleteBundle (DeleteBundleRequest) returns (DeleteBundleResponse);
  rpc AddProductToBundle (AddProductToBundleRequest) returns (BundleProductResponse);
  rpc RemoveProductFromBundle (RemoveProductFromBundleRequest) returns (DeleteBundleProductResponse);
}

service PriceService {
  rpc CreatePrice (CreatePriceRequest) returns (PriceResponse);
  rpc GetPrice (GetPriceRequest) returns (PriceResponse);
  rpc ListPrices (ListPricesRequest) returns (ListPricesResponse);
  rpc UpdatePrice (UpdatePriceRequest) returns (PriceResponse);
  rpc DeletePrice (DeletePriceRequest) returns (DeletePriceResponse);
}

service PriceBookService {
  rpc CreatePriceBook (CreatePriceBookRequest) returns (PriceBookResponse);
  rpc GetPriceBook (GetPriceBookRequest) returns (PriceBookResponse);
  rpc ListPriceBooks (ListPriceBooksRequest) returns (ListPriceBooksResponse);
  rpc UpdatePriceBook (UpdatePriceBookRequest) returns (PriceBookResponse);
  rpc DeletePriceBook (DeletePriceBookRequest) returns (DeletePriceBookResponse);
}

// Enum definitions for pricing
enum PriceType {
  PRICE_TYPE_UNSPECIFIED = 0;
  RECURRING = 1;
  USAGE = 2;
  ONE_TIME = 3;
  COMMITMENT = 4;
}

enum PriceStrategy {
  PRICE_STRATEGY_UNSPECIFIED = 0;
  FLAT = 1;
  TIERED = 2;
  VOLUME = 3;
  OVERAGE = 4;
  RAMP = 5;
  PER_UNIT = 6;
}

// Product messages
message CreateProductRequest {
  string name = 1;
  string description = 2;
  string sku = 3;
  string tax_code = 4;
  string gl_code = 5;
  string status = 6;
  string visibility = 7;
  double price = 8;
  string product_family_id = 9;
}

message UpdateProductRequest {
  string id = 1;
  string name = 2;
  string description = 3;
  string sku = 4;
  string tax_code = 5;
  string gl_code = 6;
  string status = 7;
  string visibility = 8;
  double price = 9;
  string product_family_id = 10;
}

message GetProductRequest {
  string id = 1;
}

message DeleteProductRequest {
  string id = 1;
}

message DeleteProductResponse {
  bool success = 1;
}

message ListProductsRequest {}

message ProductResponse {
  string id = 1;
  string name = 2;
  string description = 3;
  string sku = 4;
  string tax_code = 5;
  string gl_code = 6;
  string status = 7;
  string visibility = 8;
  double price = 9;
  string product_family_id = 10;
  string created_at = 11;
  string updated_at = 12;
}

message ListProductsResponse {
  repeated ProductResponse products = 1;
}

// ProductFamily messages
message CreateProductFamilyRequest {
  string name = 1;
  string description = 2;
}

message UpdateProductFamilyRequest {
  string id = 1;
  string name = 2;
  string description = 3;
}

message GetProductFamilyRequest {
  string id = 1;
}

message DeleteProductFamilyRequest {
  string id = 1;
}

message DeleteProductFamilyResponse {
  bool success = 1;
}

message ListProductFamiliesRequest {}

message ProductFamilyResponse {
  string id = 1;
  string name = 2;
  string description = 3;
  string created_at = 4;
  string updated_at = 5;
}

message ListProductFamiliesResponse {
  repeated ProductFamilyResponse product_families = 1;
}

// Bundle messages
message CreateBundleRequest {
  string name = 1;
  string description = 2;
  string status = 3;
  string pricing_strategy = 4;
}

message UpdateBundleRequest {
  string id = 1;
  string name = 2;
  string description = 3;
  string status = 4;
  string pricing_strategy = 5;
}

message GetBundleRequest {
  string id = 1;
}

message DeleteBundleRequest {
  string id = 1;
}

message DeleteBundleResponse {
  bool success = 1;
}

message ListBundlesRequest {}

message BundleResponse {
  string id = 1;
  string name = 2;
  string description = 3;
  string status = 4;
  string pricing_strategy = 5;
  int64 version = 6;
  string created_at = 7;
  string updated_at = 8;
  repeated BundleProductResponse bundle_products = 9;
}

message ListBundlesResponse {
  repeated BundleResponse bundles = 1;
}

// BundleProduct messages
message AddProductToBundleRequest {
  string bundle_id = 1;
  string product_id = 2;
  int32 quantity = 3;
  bool optional_flag = 4;
}

message RemoveProductFromBundleRequest {
  string bundle_product_id = 1;
}

message DeleteBundleProductResponse {
  bool success = 1;
}

message BundleProductResponse {
  string id = 1;
  string bundle_id = 2;
  string product_id = 3;
  int32 quantity = 4;
  bool optional_flag = 5;
}

// Price messages
message CreatePriceRequest {
  string product_id = 1;
  string price_book_id = 2;
  string billing_frequency = 3;
  string currency = 4;
  string unit_of_measure = 5;
  string billing_strategy = 6;
  string proration_policy = 7;
  bool is_default = 8;
  bool is_grandfathered = 9;
  PriceType price_type = 10;
  PriceStrategy price_strategy = 11;
  string pricing = 12; // JSON pricing configuration
}

message UpdatePriceRequest {
  string id = 1;
  string product_id = 2;
  string price_book_id = 3;
  string billing_frequency = 4;
  string currency = 5;
  string unit_of_measure = 6;
  string billing_strategy = 7;
  string proration_policy = 8;
  bool is_default = 9;
  bool is_grandfathered = 10;
  PriceType price_type = 11;
  PriceStrategy price_strategy = 12;
  string pricing = 13; // JSON pricing configuration
}

message GetPriceRequest {
  string id = 1;
}

message DeletePriceRequest {
  string id = 1;
}

message DeletePriceResponse {
  bool success = 1;
}

message ListPricesRequest {
  string product_id = 1; // Optional filter by product
  string price_book_id = 2; // Optional filter by price book
}

message PriceResponse {
  string id = 1;
  string product_id = 2;
  string price_book_id = 3;
  string billing_frequency = 4;
  string currency = 5;
  string unit_of_measure = 6;
  string billing_strategy = 7;
  string proration_policy = 8;
  bool is_default = 9;
  bool is_grandfathered = 10;
  int64 version = 11;
  string created_at = 12;
  string updated_at = 13;
  PriceType price_type = 14;
  PriceStrategy price_strategy = 15;
  string pricing = 16; // JSON pricing configuration
}

message ListPricesResponse {
  repeated PriceResponse prices = 1;
}

// PriceBook messages
message CreatePriceBookRequest {
  string name = 1;
  string segment = 2;
  string currency = 3;
  string start_date = 4;
  string end_date = 5;
  string status = 6;
}

message UpdatePriceBookRequest {
  string id = 1;
  string name = 2;
  string segment = 3;
  string currency = 4;
  string start_date = 5;
  string end_date = 6;
  string status = 7;
}

message GetPriceBookRequest {
  string id = 1;
}

message DeletePriceBookRequest {
  string id = 1;
}

message DeletePriceBookResponse {
  bool success = 1;
}

message ListPriceBooksRequest {}

message PriceBookResponse {
  string id = 1;
  string name = 2;
  string segment = 3;
  string currency = 4;
  string start_date = 5;
  string end_date = 6;
  string status = 7;
}

message ListPriceBooksResponse {
  repeated PriceBookResponse price_books = 1;
}
