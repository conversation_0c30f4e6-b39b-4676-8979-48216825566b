syntax = "proto3";

package com.avantiq.billing.customer.grpc;

option java_multiple_files = true;
option java_package = "com.avantiq.billing.customer.grpc";
option java_outer_classname = "CustomerServiceProto";

service CustomerService {
  rpc CreateCustomer (CreateCustomerRequest) returns (CustomerResponse);
  rpc UpdateCustomer (UpdateCustomerRequest) returns (CustomerResponse);
  rpc LinkCustomerRelationship (LinkCustomerRelationshipRequest) returns (CustomerRelationshipResponse);
  rpc AddPaymentMethod (AddPaymentMethodRequest) returns (PaymentMethodResponse);
  rpc GetCustomer (GetCustomerRequest) returns (CustomerResponse);
  rpc ListSegments (ListSegmentsRequest) returns (ListSegmentsResponse);
  rpc CreateSegment (CreateSegmentRequest) returns (SegmentResponse);
  rpc AddContact (AddContactRequest) returns (ContactResponse);
  rpc ListContacts (ListContactsRequest) returns (ListContactsResponse);
  rpc UpdateContact (UpdateContactRequest) returns (ContactResponse);
  rpc GetCustomerByEmail (GetCustomerByEmailRequest) returns (CustomerResponse);
}

// --- Messages for Customer ---
message CreateCustomerRequest {
  string first_name = 1;
  string last_name = 2;
  string email = 3;
  string company_name = 4;
  string vat_number = 5;
  string country = 6;
  string segment_id = 7;
  repeated Address addresses = 8;
}

message UpdateCustomerRequest {
  string id = 1;
  string first_name = 2;
  string last_name = 3;
  string company_name = 4;
  string vat_number = 5;
  string country = 6;
  string segment_id = 7;
  string status = 8;
  repeated Address addresses = 9;
}

message GetCustomerRequest {
  string id = 1;
}

message GetCustomerByEmailRequest {
  string email = 1;
  bool deep = 2;
}

message CustomerResponse {
  Customer customer = 1;
  string error = 2;
}

message Customer {
  string id = 1;
  string first_name = 2;
  string last_name = 3;
  string email = 4;
  string company_name = 5;
  string vat_number = 6;
  string country = 7;
  string segment_id = 8;
  string status = 9;
  string created_at = 10;
  string updated_at = 11;
  repeated Address addresses = 12;
}

message Address {
  string street = 1;
  string city = 2;
  string state = 3;
  string postal_code = 4;
  string country = 5;
  enum AddressType {
    BILLING = 0;
    SHIPPING = 1;
  }
  AddressType address_type = 6;
}

// --- Messages for Customer Relationship ---
message LinkCustomerRelationshipRequest {
  string parent_id = 1;
  string child_id = 2;
  string type = 3; // 'paying' or 'reporting'
  int32 level = 4;
  string segment_id = 5;
}

message CustomerRelationshipResponse {
  string id = 1;
  string error = 2;
  bool success = 3; // Added success field
}

// --- Messages for Payment Method ---
message AddPaymentMethodRequest {
  string customer_id = 1;
  string type = 2;
  string details = 3;
  bool is_primary = 4;
}

message PaymentMethodResponse {
  string id = 1;
  string error = 2;
  bool success = 3; // Added success field
}

// --- Messages for Segments ---
message ListSegmentsRequest {}
message ListSegmentsResponse {
  repeated Segment segments = 1;
  string error = 2; // Added error field
}

message CreateSegmentRequest {
  string name = 1;
  string description = 2;
}

message SegmentResponse {
  Segment segment = 1;
  string error = 2;
  bool success = 3;
}

message Segment {
  string id = 1;
  string name = 2;
  string description = 3;
}

// --- Messages for Contacts ---
message AddContactRequest {
  string customer_id = 1;
  string first_name = 2;
  string last_name = 3;
  string email = 4;
  string contact_type = 5;
  bool is_default = 6;
}

message UpdateContactRequest {
  string customer_id = 1;
  string contact_id = 2;
  string first_name = 3;
  string last_name = 4;
  string email = 5;
  string contact_type = 6;
  bool is_default = 7;
}

message ListContactsRequest {
  string customer_id = 1;
}

message ListContactsResponse {
  repeated Contact contacts = 1;
  string error = 2; // Added error field
}

message ContactResponse {
  Contact contact = 1;
  string error = 2;
  bool success = 3; // Added success field
}

message Contact {
  string id = 1;
  string customer_id = 2;
  string first_name = 3;
  string last_name = 4;
  string email = 5;
  string contact_type = 6;
  bool is_default = 7;
  string created_at = 8;
}
