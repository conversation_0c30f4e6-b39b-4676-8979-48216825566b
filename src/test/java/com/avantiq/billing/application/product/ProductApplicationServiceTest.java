package com.avantiq.billing.application.product;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.avantiq.billing.domain.product.model.Product;
import com.avantiq.billing.domain.product.service.interfaces.ProductDomainService;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Comprehensive tests for ProductApplicationService. Tests application service orchestration,
 * validation, and error handling.
 */
@ExtendWith(MockitoExtension.class)
class ProductApplicationServiceTest {

  @Mock private ProductDomainService productDomainService;

  private ProductApplicationService productApplicationService;

  @BeforeEach
  void setUp() {
    productApplicationService = new ProductApplicationService(productDomainService);
  }

  @Test
  void shouldCreateProduct_WhenValidProduct_ThenReturnCreatedProduct() {
    // Given
    Product inputProduct = createTestProduct("Test Product", Product.STATUS_ACTIVE);
    Product createdProduct = createTestProduct("Test Product", Product.STATUS_ACTIVE);
    createdProduct.setId(UUID.randomUUID());
    createdProduct.setCreatedAt(LocalDateTime.now());
    createdProduct.setUpdatedAt(LocalDateTime.now());

    when(productDomainService.validateProduct(inputProduct)).thenReturn(true);
    when(productDomainService.createProduct(inputProduct)).thenReturn(createdProduct);

    // When
    Product result = productApplicationService.createProduct(inputProduct);

    // Then
    assertThat(result).isNotNull();
    assertThat(result.getId()).isNotNull();
    assertThat(result.getName()).isEqualTo("Test Product");
    assertThat(result.getStatus()).isEqualTo(Product.STATUS_ACTIVE);
    verify(productDomainService).validateProduct(inputProduct);
    verify(productDomainService).createProduct(inputProduct);
  }

  @Test
  void shouldThrowException_WhenCreateProductWithInvalidData_ThenValidationFails() {
    // Given
    Product invalidProduct = createTestProduct("", Product.STATUS_ACTIVE);
    when(productDomainService.validateProduct(invalidProduct)).thenReturn(false);

    // When & Then
    assertThatThrownBy(() -> productApplicationService.createProduct(invalidProduct))
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessage("Invalid product data");

    verify(productDomainService).validateProduct(invalidProduct);
    verify(productDomainService, never()).createProduct(any());
  }

  @Test
  void shouldUpdateProduct_WhenValidProduct_ThenReturnUpdatedProduct() {
    // Given
    Product inputProduct = createTestProduct("Updated Product", Product.STATUS_ACTIVE);
    inputProduct.setId(UUID.randomUUID());
    Product updatedProduct = createTestProduct("Updated Product", Product.STATUS_ACTIVE);
    updatedProduct.setId(inputProduct.getId());
    updatedProduct.setUpdatedAt(LocalDateTime.now());

    when(productDomainService.validateProduct(inputProduct)).thenReturn(true);
    when(productDomainService.updateProduct(inputProduct)).thenReturn(updatedProduct);

    // When
    Product result = productApplicationService.updateProduct(inputProduct);

    // Then
    assertThat(result).isNotNull();
    assertThat(result.getId()).isEqualTo(inputProduct.getId());
    assertThat(result.getName()).isEqualTo("Updated Product");
    assertThat(result.getUpdatedAt()).isNotNull();
    verify(productDomainService).validateProduct(inputProduct);
    verify(productDomainService).updateProduct(inputProduct);
  }

  @Test
  void shouldThrowException_WhenUpdateProductWithInvalidData_ThenValidationFails() {
    // Given
    Product invalidProduct = createTestProduct("", Product.STATUS_ACTIVE);
    when(productDomainService.validateProduct(invalidProduct)).thenReturn(false);

    // When & Then
    assertThatThrownBy(() -> productApplicationService.updateProduct(invalidProduct))
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessage("Invalid product data");

    verify(productDomainService).validateProduct(invalidProduct);
    verify(productDomainService, never()).updateProduct(any());
  }

  @Test
  void shouldDeleteProduct_WhenValidProductId_ThenCallDomainService() {
    // Given
    String productId = UUID.randomUUID().toString();

    // When
    productApplicationService.deleteProduct(productId);

    // Then
    verify(productDomainService).deleteProduct(productId);
  }

  @Test
  void shouldGetProductById_WhenProductExists_ThenReturnProduct() {
    // Given
    String productId = UUID.randomUUID().toString();
    Product existingProduct = createTestProduct("Existing Product", Product.STATUS_ACTIVE);
    existingProduct.setId(UUID.fromString(productId));

    when(productDomainService.getProductById(productId)).thenReturn(Optional.of(existingProduct));

    // When
    Optional<Product> result = productApplicationService.getProductById(productId);

    // Then
    assertThat(result).isPresent();
    assertThat(result.get().getId().toString()).isEqualTo(productId);
    assertThat(result.get().getName()).isEqualTo("Existing Product");
    verify(productDomainService).getProductById(productId);
  }

  @Test
  void shouldGetProductById_WhenProductDoesNotExist_ThenReturnEmpty() {
    // Given
    String productId = UUID.randomUUID().toString();
    when(productDomainService.getProductById(productId)).thenReturn(Optional.empty());

    // When
    Optional<Product> result = productApplicationService.getProductById(productId);

    // Then
    assertThat(result).isEmpty();
    verify(productDomainService).getProductById(productId);
  }

  @Test
  void shouldListAllProducts_WhenProductsExist_ThenReturnProductList() {
    // Given
    Product product1 = createTestProduct("Product 1", Product.STATUS_ACTIVE);
    product1.setId(UUID.randomUUID());
    Product product2 = createTestProduct("Product 2", Product.STATUS_DRAFT);
    product2.setId(UUID.randomUUID());
    List<Product> expectedProducts = Arrays.asList(product1, product2);

    when(productDomainService.listAllProducts()).thenReturn(expectedProducts);

    // When
    List<Product> result = productApplicationService.listAllProducts();

    // Then
    assertThat(result).isNotNull();
    assertThat(result).hasSize(2);
    assertThat(result.get(0).getName()).isEqualTo("Product 1");
    assertThat(result.get(1).getName()).isEqualTo("Product 2");
    verify(productDomainService).listAllProducts();
  }

  @Test
  void shouldListAllProducts_WhenNoProductsExist_ThenReturnEmptyList() {
    // Given
    when(productDomainService.listAllProducts()).thenReturn(Arrays.asList());

    // When
    List<Product> result = productApplicationService.listAllProducts();

    // Then
    assertThat(result).isNotNull();
    assertThat(result).isEmpty();
    verify(productDomainService).listAllProducts();
  }

  @Test
  void shouldPropagateException_WhenDomainServiceThrowsException() {
    // Given
    Product product = createTestProduct("Test Product", Product.STATUS_ACTIVE);
    when(productDomainService.validateProduct(product)).thenReturn(true);
    when(productDomainService.createProduct(product))
        .thenThrow(new RuntimeException("Database connection failed"));

    // When & Then
    assertThatThrownBy(() -> productApplicationService.createProduct(product))
        .isInstanceOf(RuntimeException.class)
        .hasMessage("Database connection failed");

    verify(productDomainService).validateProduct(product);
    verify(productDomainService).createProduct(product);
  }

  @Test
  void shouldHandleNullInputs_WhenCreateProductWithNull() {
    // Given
    Product nullProduct = null;
    when(productDomainService.validateProduct(nullProduct)).thenReturn(false);

    // When & Then
    assertThatThrownBy(() -> productApplicationService.createProduct(nullProduct))
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessage("Invalid product data");

    verify(productDomainService).validateProduct(nullProduct);
    verify(productDomainService, never()).createProduct(any());
  }

  @Test
  void shouldHandleNullInputs_WhenUpdateProductWithNull() {
    // Given
    Product nullProduct = null;
    when(productDomainService.validateProduct(nullProduct)).thenReturn(false);

    // When & Then
    assertThatThrownBy(() -> productApplicationService.updateProduct(nullProduct))
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessage("Invalid product data");

    verify(productDomainService).validateProduct(nullProduct);
    verify(productDomainService, never()).updateProduct(any());
  }

  @Test
  void shouldDeleteProduct_WhenProductIdIsNull_ThenDelegateToService() {
    // Given - deleteProduct doesn't validate, just delegates
    String nullProductId = null;

    // When
    productApplicationService.deleteProduct(nullProductId);

    // Then
    verify(productDomainService).deleteProduct(nullProductId);
  }

  @Test
  void shouldGetProductById_WhenProductIdIsNull_ThenDelegateToService() {
    // Given - getProductById doesn't validate, just delegates
    String nullProductId = null;
    when(productDomainService.getProductById(nullProductId)).thenReturn(Optional.empty());

    // When
    Optional<Product> result = productApplicationService.getProductById(nullProductId);

    // Then
    assertThat(result).isEmpty();
    verify(productDomainService).getProductById(nullProductId);
  }

  @Test
  void shouldValidateProductBeforeCreate_WhenMultipleValidationScenarios() {
    // Given
    Product validProduct = createTestProduct("Valid Product", Product.STATUS_ACTIVE);
    Product createdProduct = createTestProduct("Valid Product", Product.STATUS_ACTIVE);
    createdProduct.setId(UUID.randomUUID());

    when(productDomainService.validateProduct(validProduct)).thenReturn(true);
    when(productDomainService.createProduct(validProduct)).thenReturn(createdProduct);

    // When
    Product result = productApplicationService.createProduct(validProduct);

    // Then
    assertThat(result).isNotNull();
    assertThat(result.getName()).isEqualTo("Valid Product");
    verify(productDomainService).validateProduct(validProduct);
    verify(productDomainService).createProduct(validProduct);
  }

  @Test
  void shouldValidateProductBeforeUpdate_WhenMultipleValidationScenarios() {
    // Given
    Product validProduct = createTestProduct("Updated Product", Product.STATUS_INACTIVE);
    validProduct.setId(UUID.randomUUID());
    Product updatedProduct = createTestProduct("Updated Product", Product.STATUS_INACTIVE);
    updatedProduct.setId(validProduct.getId());

    when(productDomainService.validateProduct(validProduct)).thenReturn(true);
    when(productDomainService.updateProduct(validProduct)).thenReturn(updatedProduct);

    // When
    Product result = productApplicationService.updateProduct(validProduct);

    // Then
    assertThat(result).isNotNull();
    assertThat(result.getName()).isEqualTo("Updated Product");
    assertThat(result.getStatus()).isEqualTo(Product.STATUS_INACTIVE);
    verify(productDomainService).validateProduct(validProduct);
    verify(productDomainService).updateProduct(validProduct);
  }

  /** Helper method to create test Product objects. */
  private Product createTestProduct(String name, String status) {
    Product product = new Product();
    product.setName(name);
    product.setDescription("Test product description");
    product.setPrice(BigDecimal.valueOf(99.99));
    product.setSku("TEST-SKU-001");
    product.setTaxCode("TAX001");
    product.setGlCode("GL001");
    product.setStatus(status);
    product.setVisibility(Product.VISIBILITY_PUBLIC);
    product.setTenantId(1L);
    return product;
  }
}
