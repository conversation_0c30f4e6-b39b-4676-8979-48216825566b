package com.avantiq.billing.application.product;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.avantiq.billing.domain.product.model.Bundle;
import com.avantiq.billing.domain.product.model.BundleProduct;
import com.avantiq.billing.domain.product.service.interfaces.BundleService;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Comprehensive tests for BundleApplicationService. Tests application service orchestration,
 * validation, and error handling.
 */
@ExtendWith(MockitoExtension.class)
class BundleApplicationServiceTest {

  @Mock private BundleService bundleService;

  private BundleApplicationService bundleApplicationService;

  @BeforeEach
  void setUp() {
    bundleApplicationService = new BundleApplicationService(bundleService);
  }

  @Test
  void shouldCreateBundle_WhenValidBundle_ThenReturnCreatedBundle() {
    // Given
    Bundle inputBundle = createTestBundle("Test Bundle", "DRAFT", "FIXED");
    Bundle createdBundle = createTestBundle("Test Bundle", "DRAFT", "FIXED");
    createdBundle.setBundleId(UUID.randomUUID());
    createdBundle.setCreatedAt(LocalDateTime.now());
    createdBundle.setUpdatedAt(LocalDateTime.now());
    createdBundle.setVersion(1L);

    when(bundleService.createBundle(inputBundle)).thenReturn(createdBundle);

    // When
    Bundle result = bundleApplicationService.createBundle(inputBundle);

    // Then
    assertThat(result).isNotNull();
    assertThat(result.getBundleId()).isNotNull();
    assertThat(result.getName()).isEqualTo("Test Bundle");
    assertThat(result.getStatus()).isEqualTo("DRAFT");
    assertThat(result.getPricingStrategy()).isEqualTo("FIXED");
    assertThat(result.getVersion()).isEqualTo(1L);
    assertThat(result.getCreatedAt()).isNotNull();
    assertThat(result.getUpdatedAt()).isNotNull();

    verify(bundleService).createBundle(inputBundle);
  }

  @Test
  void shouldUpdateBundle_WhenValidBundle_ThenReturnUpdatedBundle() {
    // Given
    Bundle existingBundle = createTestBundle("Original Bundle", "DRAFT", "FIXED");
    existingBundle.setBundleId(UUID.randomUUID());
    existingBundle.setVersion(1L);

    Bundle updatedBundle = createTestBundle("Updated Bundle", "ACTIVE", "TIERED");
    updatedBundle.setBundleId(existingBundle.getBundleId());
    updatedBundle.setVersion(2L);
    updatedBundle.setUpdatedAt(LocalDateTime.now());

    when(bundleService.updateBundle(existingBundle)).thenReturn(updatedBundle);

    // When
    Bundle result = bundleApplicationService.updateBundle(existingBundle);

    // Then
    assertThat(result).isNotNull();
    assertThat(result.getBundleId()).isEqualTo(existingBundle.getBundleId());
    assertThat(result.getName()).isEqualTo("Updated Bundle");
    assertThat(result.getStatus()).isEqualTo("ACTIVE");
    assertThat(result.getPricingStrategy()).isEqualTo("TIERED");
    assertThat(result.getVersion()).isEqualTo(2L);

    verify(bundleService).updateBundle(existingBundle);
  }

  @Test
  void shouldDeleteBundle_WhenValidBundleId_ThenCallDomainService() {
    // Given
    String bundleId = UUID.randomUUID().toString();

    // When
    bundleApplicationService.deleteBundle(bundleId);

    // Then
    verify(bundleService).deleteBundle(bundleId);
  }

  @Test
  void shouldGetBundleById_WhenBundleExists_ThenReturnBundle() {
    // Given
    String bundleId = UUID.randomUUID().toString();
    Bundle existingBundle = createTestBundle("Test Bundle", "ACTIVE", "FIXED");
    existingBundle.setBundleId(UUID.fromString(bundleId));

    when(bundleService.getBundleById(bundleId)).thenReturn(Optional.of(existingBundle));

    // When
    Optional<Bundle> result = bundleApplicationService.getBundleById(bundleId);

    // Then
    assertThat(result).isPresent();
    assertThat(result.get().getBundleId().toString()).isEqualTo(bundleId);
    assertThat(result.get().getName()).isEqualTo("Test Bundle");

    verify(bundleService).getBundleById(bundleId);
  }

  @Test
  void shouldGetBundleById_WhenBundleNotExists_ThenReturnEmpty() {
    // Given
    String bundleId = UUID.randomUUID().toString();
    when(bundleService.getBundleById(bundleId)).thenReturn(Optional.empty());

    // When
    Optional<Bundle> result = bundleApplicationService.getBundleById(bundleId);

    // Then
    assertThat(result).isEmpty();
    verify(bundleService).getBundleById(bundleId);
  }

  @Test
  void shouldListAllBundles_WhenBundlesExist_ThenReturnBundleList() {
    // Given
    Bundle bundle1 = createTestBundle("Bundle 1", "ACTIVE", "FIXED");
    bundle1.setBundleId(UUID.randomUUID());
    Bundle bundle2 = createTestBundle("Bundle 2", "DRAFT", "TIERED");
    bundle2.setBundleId(UUID.randomUUID());

    List<Bundle> expectedBundles = Arrays.asList(bundle1, bundle2);
    when(bundleService.listAllBundles()).thenReturn(expectedBundles);

    // When
    List<Bundle> result = bundleApplicationService.listAllBundles();

    // Then
    assertThat(result).hasSize(2);
    assertThat(result).contains(bundle1, bundle2);

    verify(bundleService).listAllBundles();
  }

  @Test
  void shouldListAllBundles_WhenNoBundlesExist_ThenReturnEmptyList() {
    // Given
    when(bundleService.listAllBundles()).thenReturn(Arrays.asList());

    // When
    List<Bundle> result = bundleApplicationService.listAllBundles();

    // Then
    assertThat(result).isEmpty();
    verify(bundleService).listAllBundles();
  }

  @Test
  void shouldAddProductToBundle_WhenValidInputs_ThenReturnBundleProduct() {
    // Given
    String bundleId = UUID.randomUUID().toString();
    String productId = UUID.randomUUID().toString();
    int quantity = 5;
    boolean optionalFlag = true;

    BundleProduct expectedBundleProduct =
        createTestBundleProduct(bundleId, productId, quantity, optionalFlag);
    when(bundleService.addProductToBundle(bundleId, productId, quantity, optionalFlag))
        .thenReturn(expectedBundleProduct);

    // When
    BundleProduct result =
        bundleApplicationService.addProductToBundle(bundleId, productId, quantity, optionalFlag);

    // Then
    assertThat(result).isNotNull();
    assertThat(result.getBundleId().toString()).isEqualTo(bundleId);
    assertThat(result.getProductId().toString()).isEqualTo(productId);
    assertThat(result.getQuantity()).isEqualTo(quantity);
    assertThat(result.isOptionalFlag()).isEqualTo(optionalFlag);

    verify(bundleService).addProductToBundle(bundleId, productId, quantity, optionalFlag);
  }

  @Test
  void shouldAddProductToBundle_WhenZeroQuantity_ThenStillCallDomainService() {
    // Given
    String bundleId = UUID.randomUUID().toString();
    String productId = UUID.randomUUID().toString();
    int quantity = 0;
    boolean optionalFlag = false;

    BundleProduct expectedBundleProduct =
        createTestBundleProduct(bundleId, productId, quantity, optionalFlag);
    when(bundleService.addProductToBundle(bundleId, productId, quantity, optionalFlag))
        .thenReturn(expectedBundleProduct);

    // When
    BundleProduct result =
        bundleApplicationService.addProductToBundle(bundleId, productId, quantity, optionalFlag);

    // Then
    assertThat(result).isNotNull();
    assertThat(result.getQuantity()).isEqualTo(0);
    verify(bundleService).addProductToBundle(bundleId, productId, quantity, optionalFlag);
  }

  @Test
  void shouldRemoveProductFromBundle_WhenValidBundleProductId_ThenCallDomainService() {
    // Given
    String bundleProductId = UUID.randomUUID().toString();

    // When
    bundleApplicationService.removeProductFromBundle(bundleProductId);

    // Then
    verify(bundleService).removeProductFromBundle(bundleProductId);
  }

  @Test
  void shouldPropagateException_WhenDomainServiceThrowsException() {
    // Given
    Bundle invalidBundle = createTestBundle("", "INVALID", "UNKNOWN");
    RuntimeException domainException = new IllegalArgumentException("Invalid bundle data");
    when(bundleService.createBundle(invalidBundle)).thenThrow(domainException);

    // When/Then
    assertThatThrownBy(() -> bundleApplicationService.createBundle(invalidBundle))
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessage("Invalid bundle data");

    verify(bundleService).createBundle(invalidBundle);
  }

  @Test
  void shouldHandleNullInputs_WhenCreateBundleWithNull() {
    // Given
    when(bundleService.createBundle(null))
        .thenThrow(new IllegalArgumentException("Bundle cannot be null"));

    // When/Then
    assertThatThrownBy(() -> bundleApplicationService.createBundle(null))
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessage("Bundle cannot be null");

    verify(bundleService).createBundle(null);
  }

  @Test
  void shouldHandleNullInputs_WhenGetBundleByIdWithNull() {
    // Given
    when(bundleService.getBundleById(null))
        .thenThrow(new IllegalArgumentException("Bundle ID cannot be null"));

    // When/Then
    assertThatThrownBy(() -> bundleApplicationService.getBundleById(null))
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessage("Bundle ID cannot be null");

    verify(bundleService).getBundleById(null);
  }

  @Test
  void shouldHandleEmptyInputs_WhenGetBundleByIdWithEmptyString() {
    // Given
    String emptyBundleId = "";
    when(bundleService.getBundleById(emptyBundleId))
        .thenThrow(new IllegalArgumentException("Bundle ID cannot be empty"));

    // When/Then
    assertThatThrownBy(() -> bundleApplicationService.getBundleById(emptyBundleId))
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessage("Bundle ID cannot be empty");

    verify(bundleService).getBundleById(emptyBundleId);
  }

  // Helper methods for test data creation
  private Bundle createTestBundle(String name, String status, String pricingStrategy) {
    return new Bundle(
        null, // Will be set by service
        name,
        "Test description for " + name,
        status,
        pricingStrategy,
        0L, // Will be set by service
        null, // Will be set by service
        null // Will be set by service
        );
  }

  private BundleProduct createTestBundleProduct(
      String bundleId, String productId, int quantity, boolean optional) {
    return new BundleProduct(
        UUID.randomUUID(), // id
        UUID.fromString(bundleId),
        UUID.fromString(productId),
        quantity,
        optional);
  }
}
