package com.avantiq.billing.application.security;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.avantiq.billing.application.security.AuthenticationApplicationService.AuthenticationResult;
import com.avantiq.billing.application.security.AuthenticationApplicationService.LoginRequest;
import com.avantiq.billing.domain.security.model.Role;
import com.avantiq.billing.domain.security.model.User;
import com.avantiq.billing.domain.security.model.UserPrincipal;
import com.avantiq.billing.domain.security.model.UserStatus;
import com.avantiq.billing.domain.security.repository.UserRepository;
import com.avantiq.billing.infrastructure.security.jwt.JwtTokenProvider;
import java.util.Date;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * Comprehensive tests for AuthenticationApplicationService. Tests authentication orchestration,
 * validation, and error handling.
 */
@ExtendWith(MockitoExtension.class)
class AuthenticationApplicationServiceTest {

  @Mock private JwtTokenProvider jwtTokenProvider;

  @Mock private PasswordEncoder passwordEncoder;

  @Mock private UserRepository userRepository;

  private AuthenticationApplicationService authenticationApplicationService;

  @BeforeEach
  void setUp() {
    authenticationApplicationService =
        new AuthenticationApplicationService(jwtTokenProvider, passwordEncoder, userRepository);
  }

  @Test
  void shouldLogin_WhenValidCredentials_ThenReturnAuthenticationResult() {
    // Given
    String username = "testuser";
    String password = "password123";
    String encodedPassword = "$2a$12$LQxKSVj3lBT4sTYWW6DRkOIbfz/6JEqB5LG7j5MqZzJr9XGbHvqsS";
    String jwtToken = "jwt.token.value";

    LoginRequest loginRequest =
        LoginRequest.builder().username(username).password(password).build();

    User user = createTestUser(username, encodedPassword, UserStatus.ACTIVE);
    UserPrincipal userPrincipal = UserPrincipal.fromUser(user);
    Date expirationDate = new Date(System.currentTimeMillis() + 86400000L);

    when(userRepository.findByUsername(username)).thenReturn(Optional.of(user));
    when(passwordEncoder.matches(password, encodedPassword)).thenReturn(true);
    when(jwtTokenProvider.generateToken(any(UserPrincipal.class))).thenReturn(jwtToken);
    when(jwtTokenProvider.getExpirationDateFromToken(jwtToken)).thenReturn(expirationDate);

    // When
    AuthenticationResult result = authenticationApplicationService.login(loginRequest);

    // Then
    assertThat(result).isNotNull();
    assertThat(result.getToken()).isEqualTo(jwtToken);
    assertThat(result.getUserId()).isEqualTo(user.getUserId());
    assertThat(result.getUsername()).isEqualTo(username);
    assertThat(result.getTenantId()).isEqualTo(user.getTenantId());
    assertThat(result.isSuccess()).isTrue();
    assertThat(result.getExpiresAt()).isNotNull();

    verify(userRepository).findByUsername(username);
    verify(passwordEncoder).matches(password, encodedPassword);
    verify(jwtTokenProvider).generateToken(any(UserPrincipal.class));
  }

  @Test
  void shouldThrowBadCredentialsException_WhenUserNotFound() {
    // Given
    String username = "nonexistent";
    String password = "password123";

    LoginRequest loginRequest =
        LoginRequest.builder().username(username).password(password).build();

    when(userRepository.findByUsername(username)).thenReturn(Optional.empty());

    // When & Then
    assertThatThrownBy(() -> authenticationApplicationService.login(loginRequest))
        .isInstanceOf(BadCredentialsException.class)
        .hasMessage("Invalid username or password");

    verify(userRepository).findByUsername(username);
    verify(passwordEncoder, never()).matches(anyString(), anyString());
    verify(jwtTokenProvider, never()).generateToken(any());
  }

  @Test
  void shouldThrowBadCredentialsException_WhenPasswordDoesNotMatch() {
    // Given
    String username = "testuser";
    String password = "wrongpassword";
    String encodedPassword = "$2a$12$LQxKSVj3lBT4sTYWW6DRkOIbfz/6JEqB5LG7j5MqZzJr9XGbHvqsS";

    LoginRequest loginRequest =
        LoginRequest.builder().username(username).password(password).build();

    User user = createTestUser(username, encodedPassword, UserStatus.ACTIVE);

    when(userRepository.findByUsername(username)).thenReturn(Optional.of(user));
    when(passwordEncoder.matches(password, encodedPassword)).thenReturn(false);

    // When & Then
    assertThatThrownBy(() -> authenticationApplicationService.login(loginRequest))
        .isInstanceOf(BadCredentialsException.class)
        .hasMessage("Invalid username or password");

    verify(userRepository).findByUsername(username);
    verify(passwordEncoder).matches(password, encodedPassword);
    verify(jwtTokenProvider, never()).generateToken(any());
  }

  @Test
  void shouldThrowBadCredentialsException_WhenUserIsInactive() {
    // Given
    String username = "inactiveuser";
    String password = "password123";
    String encodedPassword = "$2a$12$LQxKSVj3lBT4sTYWW6DRkOIbfz/6JEqB5LG7j5MqZzJr9XGbHvqsS";

    LoginRequest loginRequest =
        LoginRequest.builder().username(username).password(password).build();

    User inactiveUser = createTestUser(username, encodedPassword, UserStatus.INACTIVE);

    when(userRepository.findByUsername(username)).thenReturn(Optional.of(inactiveUser));

    // When & Then
    assertThatThrownBy(() -> authenticationApplicationService.login(loginRequest))
        .isInstanceOf(BadCredentialsException.class)
        .hasMessage("Invalid username or password");

    verify(userRepository).findByUsername(username);
    verify(passwordEncoder, never()).matches(anyString(), anyString());
    verify(jwtTokenProvider, never()).generateToken(any());
  }

  @Test
  void shouldLogout_WhenCalled_ThenClearSecurityContext() {
    // When
    authenticationApplicationService.logout();

    // Then - Should complete without throwing exceptions
    // Note: Testing SecurityContextHolder.clearContext() is challenging in unit tests
    // as it's a static method. In practice, this would be tested in integration tests.
  }

  @Test
  void shouldThrowBadCredentialsException_WhenRefreshTokenWithNoAuthenticatedUser() {
    // When & Then
    assertThatThrownBy(() -> authenticationApplicationService.refreshToken())
        .isInstanceOf(BadCredentialsException.class)
        .hasMessage("No authenticated user");
  }

  @Test
  void shouldHandleNullUsername_WhenLoginWithNullUsername() {
    // Given
    LoginRequest loginRequest =
        LoginRequest.builder().username(null).password("password123").build();

    when(userRepository.findByUsername(null)).thenReturn(Optional.empty());

    // When & Then
    assertThatThrownBy(() -> authenticationApplicationService.login(loginRequest))
        .isInstanceOf(BadCredentialsException.class)
        .hasMessage("Invalid username or password");

    verify(userRepository).findByUsername(null);
    verify(passwordEncoder, never()).matches(anyString(), anyString());
  }

  @Test
  void shouldHandleNullPassword_WhenLoginWithNullPassword() {
    // Given
    String username = "testuser";
    String encodedPassword = "$2a$12$LQxKSVj3lBT4sTYWW6DRkOIbfz/6JEqB5LG7j5MqZzJr9XGbHvqsS";

    LoginRequest loginRequest = LoginRequest.builder().username(username).password(null).build();

    User user = createTestUser(username, encodedPassword, UserStatus.ACTIVE);

    when(userRepository.findByUsername(username)).thenReturn(Optional.of(user));
    when(passwordEncoder.matches(null, encodedPassword)).thenReturn(false);

    // When & Then
    assertThatThrownBy(() -> authenticationApplicationService.login(loginRequest))
        .isInstanceOf(BadCredentialsException.class)
        .hasMessage("Invalid username or password");

    verify(userRepository).findByUsername(username);
    verify(passwordEncoder).matches(null, encodedPassword);
  }

  @Test
  void shouldPropagateException_WhenRepositoryThrowsException() {
    // Given
    String username = "testuser";
    String password = "password123";

    LoginRequest loginRequest =
        LoginRequest.builder().username(username).password(password).build();

    when(userRepository.findByUsername(username))
        .thenThrow(new RuntimeException("Database connection failed"));

    // When & Then
    assertThatThrownBy(() -> authenticationApplicationService.login(loginRequest))
        .isInstanceOf(BadCredentialsException.class)
        .hasMessage("Invalid username or password");

    verify(userRepository).findByUsername(username);
    verify(passwordEncoder, never()).matches(anyString(), anyString());
  }

  @Test
  void shouldPropagateException_WhenJwtTokenProviderThrowsException() {
    // Given
    String username = "testuser";
    String password = "password123";
    String encodedPassword = "$2a$12$LQxKSVj3lBT4sTYWW6DRkOIbfz/6JEqB5LG7j5MqZzJr9XGbHvqsS";

    LoginRequest loginRequest =
        LoginRequest.builder().username(username).password(password).build();

    User user = createTestUser(username, encodedPassword, UserStatus.ACTIVE);

    when(userRepository.findByUsername(username)).thenReturn(Optional.of(user));
    when(passwordEncoder.matches(password, encodedPassword)).thenReturn(true);
    when(jwtTokenProvider.generateToken(any(UserPrincipal.class)))
        .thenThrow(new RuntimeException("JWT generation failed"));

    // When & Then
    assertThatThrownBy(() -> authenticationApplicationService.login(loginRequest))
        .isInstanceOf(BadCredentialsException.class)
        .hasMessage("Invalid username or password");

    verify(userRepository).findByUsername(username);
    verify(passwordEncoder).matches(password, encodedPassword);
    verify(jwtTokenProvider).generateToken(any(UserPrincipal.class));
  }

  @Test
  void shouldLogin_WhenUserWithDifferentRoles_ThenReturnCorrectResult() {
    // Given
    String username = "admin";
    String password = "adminpass";
    String encodedPassword = "$2a$12$LQxKSVj3lBT4sTYWW6DRkOIbfz/6JEqB5LG7j5MqZzJr9XGbHvqsS";
    String jwtToken = "admin.jwt.token";

    LoginRequest loginRequest =
        LoginRequest.builder().username(username).password(password).build();

    User adminUser =
        createTestUserWithRole(
            username, encodedPassword, UserStatus.ACTIVE, Set.of(Role.TENANT_ADMIN));
    Date expirationDate = new Date(System.currentTimeMillis() + 86400000L);

    when(userRepository.findByUsername(username)).thenReturn(Optional.of(adminUser));
    when(passwordEncoder.matches(password, encodedPassword)).thenReturn(true);
    when(jwtTokenProvider.generateToken(any(UserPrincipal.class))).thenReturn(jwtToken);
    when(jwtTokenProvider.getExpirationDateFromToken(jwtToken)).thenReturn(expirationDate);

    // When
    AuthenticationResult result = authenticationApplicationService.login(loginRequest);

    // Then
    assertThat(result).isNotNull();
    assertThat(result.getToken()).isEqualTo(jwtToken);
    assertThat(result.getUsername()).isEqualTo(username);
    assertThat(result.isSuccess()).isTrue();

    verify(userRepository).findByUsername(username);
    verify(passwordEncoder).matches(password, encodedPassword);
    verify(jwtTokenProvider).generateToken(any(UserPrincipal.class));
  }

  @Test
  void shouldLogin_WhenUserWithTenantDomain_ThenReturnCorrectResult() {
    // Given
    String username = "testuser";
    String password = "password123";
    String encodedPassword = "$2a$12$LQxKSVj3lBT4sTYWW6DRkOIbfz/6JEqB5LG7j5MqZzJr9XGbHvqsS";
    String jwtToken = "jwt.token.value";
    String tenantDomain = "company.com";

    LoginRequest loginRequest =
        LoginRequest.builder()
            .username(username)
            .password(password)
            .tenantDomain(tenantDomain)
            .build();

    User user = createTestUser(username, encodedPassword, UserStatus.ACTIVE);
    Date expirationDate = new Date(System.currentTimeMillis() + 86400000L);

    when(userRepository.findByUsername(username)).thenReturn(Optional.of(user));
    when(passwordEncoder.matches(password, encodedPassword)).thenReturn(true);
    when(jwtTokenProvider.generateToken(any(UserPrincipal.class))).thenReturn(jwtToken);
    when(jwtTokenProvider.getExpirationDateFromToken(jwtToken)).thenReturn(expirationDate);

    // When
    AuthenticationResult result = authenticationApplicationService.login(loginRequest);

    // Then
    assertThat(result).isNotNull();
    assertThat(result.getToken()).isEqualTo(jwtToken);
    assertThat(result.getUsername()).isEqualTo(username);
    assertThat(result.getTenantId()).isEqualTo(user.getTenantId());
    assertThat(result.isSuccess()).isTrue();

    verify(userRepository).findByUsername(username);
    verify(passwordEncoder).matches(password, encodedPassword);
    verify(jwtTokenProvider).generateToken(any(UserPrincipal.class));
  }

  /** Helper method to create test User objects. */
  private User createTestUser(String username, String passwordHash, UserStatus status) {
    return createTestUserWithRole(username, passwordHash, status, Set.of(Role.USER));
  }

  /** Helper method to create test User objects with specific roles. */
  private User createTestUserWithRole(
      String username, String passwordHash, UserStatus status, Set<Role> roles) {
    return User.builder()
        .userId(UUID.randomUUID())
        .username(username)
        .email(username + "@example.com")
        .passwordHash(passwordHash)
        .tenantId(1L)
        .roles(roles)
        .assignedSegments(Set.of(UUID.randomUUID()))
        .status(status)
        .metadata(
            new com.avantiq.billing.domain.common.Metadata(
                UUID.randomUUID(), UUID.randomUUID(), "test-key", "test-value"))
        .build();
  }
}
