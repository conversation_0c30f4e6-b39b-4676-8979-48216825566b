package com.avantiq.billing.domain.product.valueobject.pricing;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.avantiq.billing.domain.product.model.Price;
import java.math.BigDecimal;
import java.util.Arrays;
import org.junit.jupiter.api.Test;

class VolumePricingTest {

  @Test
  void shouldCalculateVolumePricing() {
    // Given
    VolumePricing pricing =
        VolumePricing.builder()
            .brackets(
                Arrays.asList(
                    VolumePricing.VolumeBracket.builder()
                        .minQuantity(BigDecimal.ZERO)
                        .maxQuantity(new BigDecimal("10"))
                        .unitPrice(new BigDecimal("10.00"))
                        .build(),
                    VolumePricing.VolumeBracket.builder()
                        .minQuantity(new BigDecimal("11"))
                        .maxQuantity(new BigDecimal("50"))
                        .unitPrice(new BigDecimal("8.00"))
                        .build(),
                    VolumePricing.VolumeBracket.builder()
                        .minQuantity(new BigDecimal("51"))
                        .maxQuantity(null)
                        .unitPrice(new BigDecimal("6.00"))
                        .build()))
            .build();

    // When - Test various quantities
    BigDecimal amount5 = pricing.calculateAmount(new BigDecimal("5"));
    BigDecimal amount25 = pricing.calculateAmount(new BigDecimal("25"));
    BigDecimal amount75 = pricing.calculateAmount(new BigDecimal("75"));

    // Then - All units at bracket rate
    // 5 units: 5 × $10 = $50
    assertThat(amount5).isEqualByComparingTo("50.00");

    // 25 units: 25 × $8 = $200 (all at second bracket rate)
    assertThat(amount25).isEqualByComparingTo("200.00");

    // 75 units: 75 × $6 = $450 (all at third bracket rate)
    assertThat(amount75).isEqualByComparingTo("450.00");
  }

  @Test
  void shouldHandleExactBracketBoundaries() {
    // Given
    VolumePricing pricing = createStandardVolumePricing();

    // When
    BigDecimal amount10 = pricing.calculateAmount(new BigDecimal("10"));
    BigDecimal amount11 = pricing.calculateAmount(new BigDecimal("11"));
    BigDecimal amount50 = pricing.calculateAmount(new BigDecimal("50"));
    BigDecimal amount51 = pricing.calculateAmount(new BigDecimal("51"));

    // Then
    assertThat(amount10).isEqualByComparingTo("100.00"); // 10 × $10
    assertThat(amount11).isEqualByComparingTo("88.00"); // 11 × $8
    assertThat(amount50).isEqualByComparingTo("400.00"); // 50 × $8
    assertThat(amount51).isEqualByComparingTo("306.00"); // 51 × $6
  }

  @Test
  void shouldThrowExceptionForNoApplicableBracket() {
    // Given - Brackets that don't start at 0
    VolumePricing pricing =
        VolumePricing.builder()
            .brackets(
                Arrays.asList(
                    VolumePricing.VolumeBracket.builder()
                        .minQuantity(new BigDecimal("10"))
                        .maxQuantity(new BigDecimal("50"))
                        .unitPrice(new BigDecimal("8.00"))
                        .build()))
            .build();

    // When/Then
    assertThatThrownBy(() -> pricing.calculateAmount(new BigDecimal("5")))
        .isInstanceOf(IllegalStateException.class)
        .hasMessage("No applicable volume bracket found for quantity: 5");
  }

  @Test
  void shouldValidateBracketGaps() {
    // Given
    VolumePricing pricing =
        VolumePricing.builder()
            .brackets(
                Arrays.asList(
                    VolumePricing.VolumeBracket.builder()
                        .minQuantity(BigDecimal.ZERO)
                        .maxQuantity(new BigDecimal("10"))
                        .unitPrice(new BigDecimal("10.00"))
                        .build(),
                    VolumePricing.VolumeBracket.builder()
                        .minQuantity(new BigDecimal("12")) // Gap!
                        .maxQuantity(null)
                        .unitPrice(new BigDecimal("8.00"))
                        .build()))
            .build();

    // When/Then
    assertThatThrownBy(() -> pricing.validate())
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessage("Gap or overlap between brackets at position 0");
  }

  @Test
  void shouldReturnCorrectStrategy() {
    // Given
    VolumePricing pricing = createStandardVolumePricing();

    // When
    Price.PriceStrategy strategy = pricing.getStrategy();

    // Then
    assertThat(strategy).isEqualTo(Price.PriceStrategy.VOLUME);
  }

  @Test
  void shouldCompareToTieredPricing() {
    // Given - Same bracket structure
    VolumePricing volumePricing = createStandardVolumePricing();

    // When - Calculate for 75 units
    BigDecimal volumeAmount = volumePricing.calculateAmount(new BigDecimal("75"));

    // Then
    // Volume: 75 × $6 = $450
    assertThat(volumeAmount).isEqualByComparingTo("450.00");

    // Note: Tiered pricing would be $570 for same quantity
    // This demonstrates volume pricing is cheaper for larger quantities
  }

  @Test
  void shouldValidateFirstBracketStartsAtZero() {
    // Given
    VolumePricing pricing =
        VolumePricing.builder()
            .brackets(
                Arrays.asList(
                    VolumePricing.VolumeBracket.builder()
                        .minQuantity(new BigDecimal("1")) // Should start at 0
                        .maxQuantity(new BigDecimal("10"))
                        .unitPrice(new BigDecimal("10.00"))
                        .build()))
            .build();

    // When/Then
    assertThatThrownBy(() -> pricing.validate())
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessage("First bracket must start at quantity 0");
  }

  private VolumePricing createStandardVolumePricing() {
    return VolumePricing.builder()
        .brackets(
            Arrays.asList(
                VolumePricing.VolumeBracket.builder()
                    .minQuantity(BigDecimal.ZERO)
                    .maxQuantity(new BigDecimal("10"))
                    .unitPrice(new BigDecimal("10.00"))
                    .label("Standard")
                    .build(),
                VolumePricing.VolumeBracket.builder()
                    .minQuantity(new BigDecimal("11"))
                    .maxQuantity(new BigDecimal("50"))
                    .unitPrice(new BigDecimal("8.00"))
                    .label("Volume discount")
                    .build(),
                VolumePricing.VolumeBracket.builder()
                    .minQuantity(new BigDecimal("51"))
                    .maxQuantity(null)
                    .unitPrice(new BigDecimal("6.00"))
                    .label("Enterprise discount")
                    .build()))
        .build();
  }
}
