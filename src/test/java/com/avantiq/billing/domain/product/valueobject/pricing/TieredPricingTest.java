package com.avantiq.billing.domain.product.valueobject.pricing;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.avantiq.billing.domain.product.model.Price;
import com.avantiq.billing.domain.product.valueobject.PricingSummary;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import org.junit.jupiter.api.Test;

class TieredPricingTest {

  @Test
  void shouldCalculateTieredPricing() {
    // Given
    TieredPricing pricing =
        TieredPricing.builder()
            .tiers(
                Arrays.asList(
                    TieredPricing.Tier.builder()
                        .minQuantity(BigDecimal.ZERO)
                        .maxQuantity(new BigDecimal("10"))
                        .unitPrice(new BigDecimal("10.00"))
                        .label("Starter")
                        .build(),
                    TieredPricing.Tier.builder()
                        .minQuantity(new BigDecimal("11"))
                        .maxQuantity(new BigDecimal("50"))
                        .unitPrice(new BigDecimal("8.00"))
                        .label("Growth")
                        .build(),
                    TieredPricing.Tier.builder()
                        .minQuantity(new BigDecimal("51"))
                        .maxQuantity(null) // Unlimited
                        .unitPrice(new BigDecimal("6.00"))
                        .label("Enterprise")
                        .build()))
            .build();

    // When - Test various quantities
    BigDecimal amount5 = pricing.calculateAmount(new BigDecimal("5"));
    BigDecimal amount25 = pricing.calculateAmount(new BigDecimal("25"));
    BigDecimal amount75 = pricing.calculateAmount(new BigDecimal("75"));

    // Then
    // 5 units: 5 × $10 = $50
    assertThat(amount5).isEqualByComparingTo("50.00");

    // 25 units: (10 × $10) + (15 × $8) = $100 + $120 = $220
    assertThat(amount25).isEqualByComparingTo("220.00");

    // 75 units: (10 × $10) + (40 × $8) + (25 × $6) = $100 + $320 + $150 = $570
    assertThat(amount75).isEqualByComparingTo("570.00");
  }

  @Test
  void shouldHandleExactTierBoundaries() {
    // Given
    TieredPricing pricing = createStandardTieredPricing();

    // When
    BigDecimal amount10 = pricing.calculateAmount(new BigDecimal("10"));
    BigDecimal amount11 = pricing.calculateAmount(new BigDecimal("11"));
    BigDecimal amount50 = pricing.calculateAmount(new BigDecimal("50"));
    BigDecimal amount51 = pricing.calculateAmount(new BigDecimal("51"));

    // Then
    // 10 units: 10 × $10 = $100
    assertThat(amount10).isEqualByComparingTo("100.00");

    // 11 units: (10 × $10) + (1 × $8) = $108
    assertThat(amount11).isEqualByComparingTo("108.00");

    // 50 units: (10 × $10) + (40 × $8) = $420
    assertThat(amount50).isEqualByComparingTo("420.00");

    // 51 units: (10 × $10) + (40 × $8) + (1 × $6) = $426
    assertThat(amount51).isEqualByComparingTo("426.00");
  }

  @Test
  void shouldValidateEmptyTiers() {
    // Given
    TieredPricing pricing = TieredPricing.builder().tiers(Collections.emptyList()).build();

    // When/Then
    assertThatThrownBy(() -> pricing.validate())
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessage("Tiers cannot be empty");
  }

  @Test
  void shouldValidateTierGaps() {
    // Given - Tiers with a gap between 10 and 12
    TieredPricing pricing =
        TieredPricing.builder()
            .tiers(
                Arrays.asList(
                    TieredPricing.Tier.builder()
                        .minQuantity(BigDecimal.ZERO)
                        .maxQuantity(new BigDecimal("10"))
                        .unitPrice(new BigDecimal("10.00"))
                        .build(),
                    TieredPricing.Tier.builder()
                        .minQuantity(new BigDecimal("12")) // Gap!
                        .maxQuantity(null)
                        .unitPrice(new BigDecimal("8.00"))
                        .build()))
            .build();

    // When/Then
    assertThatThrownBy(() -> pricing.validate())
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessage("Gap or overlap between tiers at position 0");
  }

  @Test
  void shouldValidateTierOverlaps() {
    // Given - Tiers with overlap
    TieredPricing pricing =
        TieredPricing.builder()
            .tiers(
                Arrays.asList(
                    TieredPricing.Tier.builder()
                        .minQuantity(BigDecimal.ZERO)
                        .maxQuantity(new BigDecimal("10"))
                        .unitPrice(new BigDecimal("10.00"))
                        .build(),
                    TieredPricing.Tier.builder()
                        .minQuantity(new BigDecimal("10")) // Overlap!
                        .maxQuantity(null)
                        .unitPrice(new BigDecimal("8.00"))
                        .build()))
            .build();

    // When/Then
    assertThatThrownBy(() -> pricing.validate())
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessage("Gap or overlap between tiers at position 0");
  }

  @Test
  void shouldValidateUnlimitedMaxNotLast() {
    // Given - Non-last tier with unlimited max
    TieredPricing pricing =
        TieredPricing.builder()
            .tiers(
                Arrays.asList(
                    TieredPricing.Tier.builder()
                        .minQuantity(BigDecimal.ZERO)
                        .maxQuantity(null) // Should only be on last tier
                        .unitPrice(new BigDecimal("10.00"))
                        .build(),
                    TieredPricing.Tier.builder()
                        .minQuantity(new BigDecimal("11"))
                        .maxQuantity(new BigDecimal("50"))
                        .unitPrice(new BigDecimal("8.00"))
                        .build()))
            .build();

    // When/Then
    assertThatThrownBy(() -> pricing.validate())
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessage("Only the last tier can have unlimited max quantity");
  }

  @Test
  void shouldReturnCorrectStrategy() {
    // Given
    TieredPricing pricing = createStandardTieredPricing();

    // When
    Price.PriceStrategy strategy = pricing.getStrategy();

    // Then
    assertThat(strategy).isEqualTo(Price.PriceStrategy.TIERED);
  }

  @Test
  void shouldProvideSummary() {
    // Given
    TieredPricing pricing = createStandardTieredPricing();

    // When
    PricingSummary summary = pricing.summarize();

    // Then
    assertThat(summary.getDescription()).isEqualTo("Tiered pricing");
    assertThat(summary.getFormula()).isEqualTo("Cumulative tiers");
    assertThat(summary.getDetails()).contains("0-10: $10.00/unit (Starter)");
    assertThat(summary.getDetails()).contains("11-50: $8.00/unit (Growth)");
    assertThat(summary.getDetails()).contains("51+: $6.00/unit (Enterprise)");
  }

  @Test
  void shouldHandleZeroQuantity() {
    // Given
    TieredPricing pricing = createStandardTieredPricing();

    // When
    BigDecimal amount = pricing.calculateAmount(BigDecimal.ZERO);

    // Then
    assertThat(amount).isEqualByComparingTo("0.00");
  }

  private TieredPricing createStandardTieredPricing() {
    return TieredPricing.builder()
        .tiers(
            Arrays.asList(
                TieredPricing.Tier.builder()
                    .minQuantity(BigDecimal.ZERO)
                    .maxQuantity(new BigDecimal("10"))
                    .unitPrice(new BigDecimal("10.00"))
                    .label("Starter")
                    .build(),
                TieredPricing.Tier.builder()
                    .minQuantity(new BigDecimal("11"))
                    .maxQuantity(new BigDecimal("50"))
                    .unitPrice(new BigDecimal("8.00"))
                    .label("Growth")
                    .build(),
                TieredPricing.Tier.builder()
                    .minQuantity(new BigDecimal("51"))
                    .maxQuantity(null)
                    .unitPrice(new BigDecimal("6.00"))
                    .label("Enterprise")
                    .build()))
        .build();
  }
}
