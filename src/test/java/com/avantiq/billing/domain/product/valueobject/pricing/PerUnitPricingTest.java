package com.avantiq.billing.domain.product.valueobject.pricing;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.avantiq.billing.domain.product.model.Price;
import com.avantiq.billing.domain.product.valueobject.PricingSummary;
import java.math.BigDecimal;
import java.math.RoundingMode;
import org.junit.jupiter.api.Test;

class PerUnitPricingTest {

  @Test
  void shouldCalculateSimplePerUnitPricing() {
    // Given
    PerUnitPricing pricing =
        PerUnitPricing.builder()
            .pricePerUnit(new BigDecimal("0.001"))
            .pricingUnit("request")
            .build();

    // When
    BigDecimal amount = pricing.calculateAmount(new BigDecimal("5000"));

    // Then
    assertThat(amount).isEqualByComparingTo("5.000");
  }

  @Test
  void shouldCalculateBlockPricing() {
    // Given - $0.50 per million requests
    PerUnitPricing pricing =
        PerUnitPricing.builder()
            .pricePerUnit(new BigDecimal("0.50"))
            .pricingUnit("request")
            .blockSize(new BigDecimal("1000000"))
            .blockRounding(RoundingMode.CEILING)
            .build();

    // When
    BigDecimal amount1M = pricing.calculateAmount(new BigDecimal("1000000"));
    BigDecimal amount1_5M = pricing.calculateAmount(new BigDecimal("1500000"));
    BigDecimal amount2_1M = pricing.calculateAmount(new BigDecimal("2100000"));

    // Then
    assertThat(amount1M).isEqualByComparingTo("0.50"); // 1 block
    assertThat(amount1_5M).isEqualByComparingTo("1.00"); // 2 blocks (ceiling)
    assertThat(amount2_1M).isEqualByComparingTo("1.50"); // 3 blocks (ceiling)
  }

  @Test
  void shouldRespectBlockRoundingMode() {
    // Given - Different rounding modes
    PerUnitPricing ceilingPricing =
        PerUnitPricing.builder()
            .pricePerUnit(new BigDecimal("1.00"))
            .pricingUnit("chunk")
            .blockSize(new BigDecimal("100"))
            .blockRounding(RoundingMode.CEILING)
            .build();

    PerUnitPricing floorPricing =
        PerUnitPricing.builder()
            .pricePerUnit(new BigDecimal("1.00"))
            .pricingUnit("chunk")
            .blockSize(new BigDecimal("100"))
            .blockRounding(RoundingMode.FLOOR)
            .build();

    // When - 250 units
    BigDecimal ceilingAmount = ceilingPricing.calculateAmount(new BigDecimal("250"));
    BigDecimal floorAmount = floorPricing.calculateAmount(new BigDecimal("250"));

    // Then
    assertThat(ceilingAmount).isEqualByComparingTo("3.00"); // 3 blocks
    assertThat(floorAmount).isEqualByComparingTo("2.00"); // 2 blocks
  }

  @Test
  void shouldHandleZeroQuantity() {
    // Given
    PerUnitPricing pricing =
        PerUnitPricing.builder()
            .pricePerUnit(new BigDecimal("0.50"))
            .pricingUnit("GB")
            .blockSize(new BigDecimal("1000"))
            .build();

    // When
    BigDecimal amount = pricing.calculateAmount(BigDecimal.ZERO);

    // Then
    assertThat(amount).isEqualByComparingTo("0");
  }

  @Test
  void shouldValidateNegativePricePerUnit() {
    // Given
    PerUnitPricing pricing =
        PerUnitPricing.builder()
            .pricePerUnit(new BigDecimal("-1.00"))
            .pricingUnit("request")
            .build();

    // When/Then
    assertThatThrownBy(() -> pricing.validate())
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessage("Price per unit must be non-negative");
  }

  @Test
  void shouldValidateEmptyPricingUnit() {
    // Given
    PerUnitPricing pricing =
        PerUnitPricing.builder().pricePerUnit(new BigDecimal("1.00")).pricingUnit("").build();

    // When/Then
    assertThatThrownBy(() -> pricing.validate())
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessage("Pricing unit must be specified");
  }

  @Test
  void shouldValidateNegativeBlockSize() {
    // Given
    PerUnitPricing pricing =
        PerUnitPricing.builder()
            .pricePerUnit(new BigDecimal("1.00"))
            .pricingUnit("request")
            .blockSize(new BigDecimal("-1000"))
            .build();

    // When/Then
    assertThatThrownBy(() -> pricing.validate())
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessage("Block size must be positive if specified");
  }

  @Test
  void shouldReturnCorrectStrategy() {
    // Given
    PerUnitPricing pricing =
        PerUnitPricing.builder()
            .pricePerUnit(new BigDecimal("0.001"))
            .pricingUnit("request")
            .build();

    // When
    Price.PriceStrategy strategy = pricing.getStrategy();

    // Then
    assertThat(strategy).isEqualTo(Price.PriceStrategy.PER_UNIT);
  }

  @Test
  void shouldProvideSummaryForSimplePricing() {
    // Given
    PerUnitPricing pricing =
        PerUnitPricing.builder().pricePerUnit(new BigDecimal("0.10")).pricingUnit("GB").build();

    // When
    PricingSummary summary = pricing.summarize();

    // Then
    assertThat(summary.getDescription()).isEqualTo("$0.10 per GB");
    assertThat(summary.getFormula()).isEqualTo("quantity × $0.10");
  }

  @Test
  void shouldProvideSummaryForBlockPricing() {
    // Given
    PerUnitPricing pricing =
        PerUnitPricing.builder()
            .pricePerUnit(new BigDecimal("0.50"))
            .pricingUnit("request")
            .blockSize(new BigDecimal("1000000"))
            .build();

    // When
    PricingSummary summary = pricing.summarize();

    // Then
    assertThat(summary.getDescription()).isEqualTo("$0.50 per million request");
    assertThat(summary.getFormula()).isEqualTo("ceil(quantity / 1000000) × $0.50");
    assertThat(summary.getDetails()).isEqualTo("Charged in blocks of million");
  }

  @Test
  void shouldHandleCommonBlockSizes() {
    // Given - Per thousand
    PerUnitPricing perThousand =
        PerUnitPricing.builder()
            .pricePerUnit(new BigDecimal("5.00"))
            .pricingUnit("API calls")
            .blockSize(new BigDecimal("1000"))
            .build();

    // When
    PricingSummary summary = perThousand.summarize();

    // Then
    assertThat(summary.getDescription()).isEqualTo("$5.00 per thousand API calls");
  }
}
