package com.avantiq.billing.domain.product.valueobject.pricing;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.avantiq.billing.domain.product.model.Price;
import com.avantiq.billing.domain.product.valueobject.PricingSummary;
import java.math.BigDecimal;
import org.junit.jupiter.api.Test;

class FlatPricingTest {

  @Test
  void shouldCalculateFlatPricing() {
    // Given
    FlatPricing pricing = FlatPricing.builder().unitPrice(new BigDecimal("10.00")).build();

    // When
    BigDecimal amount = pricing.calculateAmount(new BigDecimal("5"));

    // Then
    assertThat(amount).isEqualByComparingTo("50.00");
  }

  @Test
  void shouldHandleZeroQuantity() {
    // Given
    FlatPricing pricing = FlatPricing.builder().unitPrice(new BigDecimal("10.00")).build();

    // When
    BigDecimal amount = pricing.calculateAmount(BigDecimal.ZERO);

    // Then
    assertThat(amount).isEqualByComparingTo("0.00");
  }

  @Test
  void shouldThrowExceptionForNegativeQuantity() {
    // Given
    FlatPricing pricing = FlatPricing.builder().unitPrice(new BigDecimal("10.00")).build();

    // When/Then
    assertThatThrownBy(() -> pricing.calculateAmount(new BigDecimal("-1")))
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessage("Quantity must be non-negative");
  }

  @Test
  void shouldThrowExceptionForNullQuantity() {
    // Given
    FlatPricing pricing = FlatPricing.builder().unitPrice(new BigDecimal("10.00")).build();

    // When/Then
    assertThatThrownBy(() -> pricing.calculateAmount(null))
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessage("Quantity must be non-negative");
  }

  @Test
  void shouldValidateNegativeUnitPrice() {
    // Given
    FlatPricing pricing = FlatPricing.builder().unitPrice(new BigDecimal("-10.00")).build();

    // When/Then
    assertThatThrownBy(() -> pricing.validate())
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessage("Unit price must be non-negative");
  }

  @Test
  void shouldReturnCorrectStrategy() {
    // Given
    FlatPricing pricing = FlatPricing.builder().unitPrice(new BigDecimal("10.00")).build();

    // When
    Price.PriceStrategy strategy = pricing.getStrategy();

    // Then
    assertThat(strategy).isEqualTo(Price.PriceStrategy.FLAT);
  }

  @Test
  void shouldProvideSummary() {
    // Given
    FlatPricing pricing = FlatPricing.builder().unitPrice(new BigDecimal("10.00")).build();

    // When
    PricingSummary summary = pricing.summarize();

    // Then
    assertThat(summary.getDescription()).isEqualTo("$10.00 per unit");
    assertThat(summary.getFormula()).isEqualTo("quantity × $10.00");
  }

  @Test
  void shouldHandleDecimalQuantities() {
    // Given
    FlatPricing pricing = FlatPricing.builder().unitPrice(new BigDecimal("10.50")).build();

    // When
    BigDecimal amount = pricing.calculateAmount(new BigDecimal("2.5"));

    // Then
    assertThat(amount).isEqualByComparingTo("26.25");
  }
}
