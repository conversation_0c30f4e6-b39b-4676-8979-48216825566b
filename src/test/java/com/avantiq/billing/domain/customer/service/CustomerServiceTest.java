package com.avantiq.billing.domain.customer.service;

import com.avantiq.billing.domain.customer.model.Customer;
import com.avantiq.billing.domain.customer.repository.CustomerRepository;
import com.avantiq.billing.domain.customer.service.impl.CustomerServiceImpl;
import com.avantiq.billing.domain.customer.service.interfaces.CustomerService;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class CustomerServiceTest {
  private final CustomerRepository repository = Mockito.mock(CustomerRepository.class);
  private final CustomerService service;

  public CustomerServiceTest() {
    this.service = new CustomerServiceImpl(repository);
  }

  @Test
  void testFindById() {
    UUID id = UUID.randomUUID();
    Mockito.when(repository.findById(id)).thenReturn(Optional.of(new Customer()));
    Optional<Customer> found = service.findById(id);
    Assertions.assertTrue(found.isPresent(), "Expected customer to be found by ID");
  }

  @Test
  void testFindByEmail() {
    Mockito.when(repository.findByEmail("<EMAIL>"))
        .thenReturn(Optional.of(new Customer()));
    Optional<Customer> found = service.findByEmail("<EMAIL>");
    Assertions.assertTrue(found.isPresent(), "Expected customer to be found by email");
  }

  @Test
  void testSave() {
    Customer customer = new Customer();
    Mockito.when(repository.save(customer)).thenReturn(customer);
    Customer saved = service.save(customer);
    Assertions.assertNotNull(saved, "Expected customer to be saved successfully");
  }

  @Test
  void testExistsByEmail() {
    Mockito.when(repository.findByEmail("<EMAIL>"))
        .thenReturn(Optional.of(new Customer()));
    boolean exists = service.existsByEmail("<EMAIL>");
    Assertions.assertTrue(exists, "Expected customer to exist by email");
  }
}
