package com.avantiq.billing.domain.customer.service;

import com.avantiq.billing.domain.customer.model.CustomerNote;
import com.avantiq.billing.domain.customer.repository.CustomerNoteRepository;
import com.avantiq.billing.domain.customer.service.impl.CustomerNoteServiceImpl;
import com.avantiq.billing.domain.customer.service.interfaces.CustomerNoteService;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class CustomerNoteServiceTest {
  private final CustomerNoteRepository repository = Mockito.mock(CustomerNoteRepository.class);
  private final CustomerNoteService service;

  public CustomerNoteServiceTest() {
    this.service = new CustomerNoteServiceImpl(repository);
  }

  @Test
  void testCreateNote() {
    CustomerNote note = new CustomerNote();
    Mockito.when(repository.save(note)).thenReturn(note);
    CustomerNote saved = service.createNote(note);
    Assertions.assertNotNull(saved, "Expected note to be created successfully");
  }

  @Test
  void testUpdateNote() {
    CustomerNote note = new CustomerNote();
    Mockito.when(repository.save(note)).thenReturn(note);
    CustomerNote updated = service.updateNote(note);
    Assertions.assertNotNull(updated, "Expected note to be updated successfully");
  }

  @Test
  void testDeleteNote() {
    UUID id = UUID.randomUUID();
    Mockito.doNothing().when(repository).deleteById(id);
    boolean deleted = service.deleteNote(id);
    Assertions.assertTrue(deleted, "Expected note to be deleted successfully");
  }

  @Test
  void testGetNoteById() {
    UUID id = UUID.randomUUID();
    Mockito.when(repository.findById(id)).thenReturn(Optional.of(new CustomerNote()));
    Optional<CustomerNote> found = service.getNoteById(id);
    Assertions.assertTrue(found.isPresent(), "Expected note to be found by ID");
  }

  @Test
  void testGetNotesByCustomerId() {
    UUID customerId = UUID.randomUUID();
    Mockito.when(repository.findByCustomerId(customerId)).thenReturn(List.of(new CustomerNote()));
    List<CustomerNote> notes = service.getNotesByCustomerId(customerId);
    Assertions.assertFalse(notes.isEmpty(), "Expected notes list to be non-empty");
  }
}
