package com.avantiq.billing.domain.customer.service;

import com.avantiq.billing.domain.customer.model.CustomerSegment;
import com.avantiq.billing.domain.customer.repository.CustomerSegmentRepository;
import com.avantiq.billing.domain.customer.service.impl.CustomerSegmentServiceImpl;
import com.avantiq.billing.domain.customer.service.interfaces.CustomerSegmentService;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

class CustomerSegmentServiceTest {
  private final CustomerSegmentRepository repository =
      Mockito.mock(CustomerSegmentRepository.class);
  private final CustomerSegmentService service;

  public CustomerSegmentServiceTest() {
    this.service = new CustomerSegmentServiceImpl(repository);
  }

  @Test
  void testFindAll() {
    Mockito.when(repository.findAll()).thenReturn(List.of(new CustomerSegment()));
    List<CustomerSegment> result = service.findAll();
    Assertions.assertFalse(result.isEmpty(), "Expected non-empty list of customer segments");
  }

  @Test
  void testFindAllPaginated() {
    Page<CustomerSegment> page = new PageImpl<>(List.of(new CustomerSegment()));
    Mockito.when(repository.findAll(PageRequest.of(0, 10))).thenReturn(page);
    Page<CustomerSegment> result = service.findAll(PageRequest.of(0, 10));
    Assertions.assertFalse(result.isEmpty(), "Expected paginated result to be non-empty");
  }

  @Test
  void testFindById() {
    UUID id = UUID.randomUUID();
    Mockito.when(repository.findById(id)).thenReturn(Optional.of(new CustomerSegment()));
    Optional<CustomerSegment> found = service.findById(id);
    Assertions.assertTrue(found.isPresent(), "Expected segment to be found by ID");
  }
}
