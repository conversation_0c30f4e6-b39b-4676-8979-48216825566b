package com.avantiq.billing.domain.customer.service;

import com.avantiq.billing.domain.customer.model.Contact;
import com.avantiq.billing.domain.customer.repository.ContactRepository;
import com.avantiq.billing.domain.customer.service.impl.ContactServiceImpl;
import com.avantiq.billing.domain.customer.service.interfaces.ContactService;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class ContactServiceTest {
  private final ContactRepository repository = Mockito.mock(ContactRepository.class);
  private final ContactService service;

  public ContactServiceTest() {
    this.service = new ContactServiceImpl(repository);
  }

  @Test
  void testFindByCustomerId() {
    UUID customerId = UUID.randomUUID();
    Mockito.when(repository.findByCustomerId(customerId)).thenReturn(List.of(new Contact()));
    List<Contact> result = service.findByCustomerId(customerId);
    Assertions.assertFalse(result.isEmpty());
  }

  @Test
  void testFindById() {
    UUID id = UUID.randomUUID();
    Mockito.when(repository.findById(id)).thenReturn(Optional.of(new Contact()));
    Optional<Contact> found = service.findById(id);
    Assertions.assertTrue(found.isPresent());
  }

  @Test
  void testSave() {
    Contact contact = new Contact();
    Mockito.when(repository.save(contact)).thenReturn(contact);
    Contact saved = service.save(contact);
    Assertions.assertNotNull(saved);
  }
}
