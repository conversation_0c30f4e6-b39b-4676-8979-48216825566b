package com.avantiq.billing.domain.customer.model;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.UUID;
import org.junit.jupiter.api.Test;

class AddressTest {

  /**
   * Test class for the Address domain object. Focuses on testing the isBillingAddress method, which
   * determines if the Address is of type BILLING.
   */
  @Test
  void testIsBillingAddressWhenAddressTypeIsBilling() {
    // Arrange
    Address address =
        new Address(
            UUID.randomUUID(),
            "123 Main St",
            "Sample City",
            "Sample State",
            "12345",
            "Sample Country",
            Address.AddressType.BILLING,
            null); // tenantId

    // Act
    boolean result = address.isBillingAddress();

    // Assert
    assertTrue(result, "Expected the address to be recognized as a billing address.");
  }

  @Test
  void testIsBillingAddressWhenAddressTypeIsShipping() {
    // Arrange
    Address address =
        new Address(
            UUID.randomUUID(),
            "123 Main St",
            "Sample City",
            "Sample State",
            "12345",
            "Sample Country",
            Address.AddressType.SHIPPING,
            null); // tenantId

    // Act
    boolean result = address.isBillingAddress();

    // Assert
    assertFalse(result, "Expected the address not to be recognized as a billing address.");
  }

  @Test
  void testIsBillingAddressWhenAddressTypeIsNull() {
    // Arrange
    Address address =
        new Address(
            UUID.randomUUID(),
            "123 Main St",
            "Sample City",
            "Sample State",
            "12345",
            "Sample Country",
            null,
            null); // tenantId

    // Act
    boolean result = address.isBillingAddress();

    // Assert
    assertFalse(
        result,
        "Expected the address not to be recognized as a billing address when type is null.");
  }

  @Test
  void testIsShippingAddressWhenAddressTypeIsShipping() {
    // Arrange
    Address address =
        new Address(
            UUID.randomUUID(),
            "123 Main St",
            "Sample City",
            "Sample State",
            "12345",
            "Sample Country",
            Address.AddressType.SHIPPING,
            null); // tenantId

    // Act
    boolean result = address.isShippingAddress();

    // Assert
    assertTrue(result, "Expected the address to be recognized as a shipping address.");
  }

  @Test
  void testIsShippingAddressWhenAddressTypeIsBilling() {
    // Arrange
    Address address =
        new Address(
            UUID.randomUUID(),
            "123 Main St",
            "Sample City",
            "Sample State",
            "12345",
            "Sample Country",
            Address.AddressType.BILLING,
            null); // tenantId

    // Act
    boolean result = address.isShippingAddress();

    // Assert
    assertFalse(result, "Expected the address not to be recognized as a shipping address.");
  }

  @Test
  void testIsShippingAddressWhenAddressTypeIsNull() {
    // Arrange
    Address address =
        new Address(
            UUID.randomUUID(),
            "123 Main St",
            "Sample City",
            "Sample State",
            "12345",
            "Sample Country",
            null,
            null); // tenantId

    // Act
    boolean result = address.isShippingAddress();

    // Assert
    assertFalse(
        result,
        "Expected the address not to be recognized as a shipping address when type is null.");
  }
}
