package com.avantiq.billing.infrastructure.persistence.customer.repository;

import com.avantiq.billing.infrastructure.persistence.customer.entity.ContactJpaEntity;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;

@DataJpaTest
class ContactJpaRepositoryTest {
  @Autowired private ContactJpaRepository repository;

  @Test
  void testSaveAndFindById() {
    ContactJpaEntity contact = new ContactJpaEntity();
    UUID id = UUID.randomUUID();
    contact.setId(id);
    contact.setCustomerId(UUID.randomUUID());
    contact.setFirstName("Alice");
    contact.setLastName("Smith");
    contact.setEmail("<EMAIL>");
    contact.setContactType("primary");
    contact.setDefault(true);
    repository.save(contact);
    ContactJpaEntity found = repository.findById(id).orElse(null);
    Assertions.assertAll(
        () -> {
          Assertions.assertNotNull(found, "Contact should not be null");
          Assertions.assertEquals("Alice", found.getFirstName(), "First name should match");
        });
  }

  @Test
  void testFindByCustomerId() {
    UUID customerId = UUID.randomUUID();
    ContactJpaEntity contact = new ContactJpaEntity();
    contact.setId(UUID.randomUUID());
    contact.setCustomerId(customerId);
    contact.setFirstName("Bob");
    contact.setLastName("Jones");
    contact.setEmail("<EMAIL>");
    contact.setContactType("secondary");
    contact.setDefault(false);
    repository.save(contact);
    List<ContactJpaEntity> contacts = repository.findByCustomerId(customerId);
    Assertions.assertFalse(contacts.isEmpty(), "Contacts list should not be empty");
  }
}
