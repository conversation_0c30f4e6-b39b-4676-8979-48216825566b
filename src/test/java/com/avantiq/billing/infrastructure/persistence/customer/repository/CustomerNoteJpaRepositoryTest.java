package com.avantiq.billing.infrastructure.persistence.customer.repository;

import com.avantiq.billing.infrastructure.persistence.customer.entity.CustomerNoteJpaEntity;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;

@DataJpaTest
class CustomerNoteJpaRepositoryTest {
  @Autowired private CustomerNoteJpaRepository repository;

  @Test
  void testSaveAndFindById() {
    CustomerNoteJpaEntity note = new CustomerNoteJpaEntity();
    note.setCustomerId(UUID.randomUUID());
    note.setNotes("Test note");
    note.setCreatedAt(LocalDateTime.now());
    note.setCreatedBy("tester");
    CustomerNoteJpaEntity saved = repository.save(note);
    UUID generatedId = saved.getId();
    CustomerNoteJpaEntity found = repository.findById(generatedId).orElse(null);
    Assertions.assertAll(
        () -> {
          Assertions.assertNotNull(found, "Note should not be null");
          Assertions.assertEquals("Test note", found.getNotes(), "Notes should match");
          Assertions.assertEquals(saved.getId(), found.getId(), "IDs should match");
        });
  }

  @Test
  void testFindByCustomerId() {
    UUID customerId = UUID.randomUUID();
    CustomerNoteJpaEntity note = new CustomerNoteJpaEntity();
    note.setCustomerId(customerId);
    note.setNotes("Another note");
    note.setCreatedAt(LocalDateTime.now());
    note.setCreatedBy("tester");
    repository.save(note);
    List<CustomerNoteJpaEntity> notes = repository.findByCustomerId(customerId);
    Assertions.assertFalse(notes.isEmpty(), "Notes list should not be empty");
  }
}
