package com.avantiq.billing.infrastructure.persistence.customer.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.avantiq.billing.domain.customer.model.Address;
import com.avantiq.billing.infrastructure.persistence.customer.entity.AddressJpaEntity;
import java.util.UUID;
import org.junit.jupiter.api.Test;

class AddressMapperTest {

  @Test
  void shouldReturnDomainModel_WhenEntityIsValid() {
    UUID id = UUID.randomUUID();
    AddressJpaEntity.AddressType addressType = AddressJpaEntity.AddressType.BILLING;

    AddressJpaEntity entity = new AddressJpaEntity();
    entity.setId(id);
    entity.setStreet("123 Main St");
    entity.setCity("Test City");
    entity.setState("TS");
    entity.setPostalCode("12345");
    entity.setCountry("Test Country");
    entity.setAddressType(addressType);

    Address domain = AddressMapper.toDomain(entity);

    assertNotNull(domain);
    assertEquals(entity.getId(), domain.getId());
    assertEquals(entity.getStreet(), domain.getStreet());
    assertEquals(entity.getCity(), domain.getCity());
    assertEquals(entity.getState(), domain.getState());
    assertEquals(entity.getPostalCode(), domain.getPostalCode());
    assertEquals(entity.getCountry(), domain.getCountry());
    assertEquals(Address.AddressType.BILLING, domain.getAddressType());
  }

  @Test
  void shouldReturnDomainModel_WhenEntityHasShippingAddressType() {
    UUID id = UUID.randomUUID();

    AddressJpaEntity entity = new AddressJpaEntity();
    entity.setId(id);
    entity.setStreet("456 Elm St");
    entity.setCity("Sample City");
    entity.setState("SC");
    entity.setPostalCode("67890");
    entity.setCountry("Sample Country");
    entity.setAddressType(AddressJpaEntity.AddressType.SHIPPING);

    Address domain = AddressMapper.toDomain(entity);

    assertNotNull(domain);
    assertEquals(entity.getId(), domain.getId());
    assertEquals(entity.getStreet(), domain.getStreet());
    assertEquals(entity.getCity(), domain.getCity());
    assertEquals(entity.getState(), domain.getState());
    assertEquals(entity.getPostalCode(), domain.getPostalCode());
    assertEquals(entity.getCountry(), domain.getCountry());
    assertEquals(Address.AddressType.SHIPPING, domain.getAddressType());
  }

  @Test
  void shouldReturnEntity_WhenDomainModelIsValid() {
    UUID id = UUID.randomUUID();
    Address.AddressType addressType = Address.AddressType.BILLING;

    Address domain = new Address();
    domain.setId(id);
    domain.setStreet("789 Maple St");
    domain.setCity("Example City");
    domain.setState("EC");
    domain.setPostalCode("11111");
    domain.setCountry("Example Country");
    domain.setAddressType(addressType);

    AddressJpaEntity entity = AddressMapper.toEntity(domain);

    assertNotNull(entity);
    assertEquals(domain.getId(), entity.getId());
    assertEquals(domain.getStreet(), entity.getStreet());
    assertEquals(domain.getCity(), entity.getCity());
    assertEquals(domain.getState(), entity.getState());
    assertEquals(domain.getPostalCode(), entity.getPostalCode());
    assertEquals(domain.getCountry(), entity.getCountry());
    assertEquals(AddressJpaEntity.AddressType.BILLING, entity.getAddressType());
  }

  @Test
  void shouldReturnEntity_WhenDomainModelHasShippingAddressType() {
    UUID id = UUID.randomUUID();

    Address domain = new Address();
    domain.setId(id);
    domain.setStreet("123 Oak St");
    domain.setCity("Another City");
    domain.setState("AC");
    domain.setPostalCode("22222");
    domain.setCountry("Another Country");
    domain.setAddressType(Address.AddressType.SHIPPING);

    AddressJpaEntity entity = AddressMapper.toEntity(domain);

    assertNotNull(entity);
    assertEquals(domain.getId(), entity.getId());
    assertEquals(domain.getStreet(), entity.getStreet());
    assertEquals(domain.getCity(), entity.getCity());
    assertEquals(domain.getState(), entity.getState());
    assertEquals(domain.getPostalCode(), entity.getPostalCode());
    assertEquals(domain.getCountry(), entity.getCountry());
    assertEquals(AddressJpaEntity.AddressType.SHIPPING, entity.getAddressType());
  }
}
