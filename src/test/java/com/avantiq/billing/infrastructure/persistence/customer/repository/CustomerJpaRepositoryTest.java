package com.avantiq.billing.infrastructure.persistence.customer.repository;

import com.avantiq.billing.infrastructure.persistence.customer.entity.AddressJpaEntity;
import com.avantiq.billing.infrastructure.persistence.customer.entity.CustomerJpaEntity;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.dao.DataIntegrityViolationException;

@DataJpaTest
class CustomerJpaRepositoryTest {
  @Autowired private CustomerJpaRepository customerRepository;

  @Test
  void testCreateNewCustomer() {
    CustomerJpaEntity customer = new CustomerJpaEntity();
    customer.setFirstName("John");
    customer.setLastName("Doe");
    customer.setEmail("<EMAIL>");
    customer.setCompanyName("Acme Corp");
    customer.setVatNumber("VAT123456");
    customer.setCountry("USA");
    customer.setSegmentId(UUID.randomUUID());
    customer.setStatus(CustomerJpaEntity.Status.ACTIVE);
    AddressJpaEntity address = new AddressJpaEntity();
    address.setStreet("123 Main St");
    address.setCity("Springfield");
    address.setState("IL");
    address.setPostalCode("62701");
    address.setCountry("USA");
    address.setAddressType(AddressJpaEntity.AddressType.BILLING);
    address.setCustomer(customer);
    List<AddressJpaEntity> addresses = new ArrayList<>();
    addresses.add(address);
    customer.setAddresses(addresses);
    CustomerJpaEntity saved = customerRepository.save(customer);
    Assertions.assertAll(
        () -> Assertions.assertNotNull(saved.getId(), "Customer ID should be generated"),
        () -> Assertions.assertEquals("John", saved.getFirstName(), "First name should match"),
        () -> Assertions.assertEquals("Doe", saved.getLastName(), "Last name should match"),
        () ->
            Assertions.assertEquals("<EMAIL>", saved.getEmail(), "Email should match"),
        () ->
            Assertions.assertEquals(
                "Acme Corp", saved.getCompanyName(), "Company name should match"),
        () ->
            Assertions.assertEquals(
                CustomerJpaEntity.Status.ACTIVE, saved.getStatus(), "Status should be ACTIVE"),
        () -> Assertions.assertEquals(1, saved.getAddresses().size(), "Should have one address"),
        () -> {
          AddressJpaEntity savedAddress = saved.getAddresses().getFirst();
          Assertions.assertEquals("123 Main St", savedAddress.getStreet(), "Street should match");
          Assertions.assertEquals("Springfield", savedAddress.getCity(), "City should match");
          Assertions.assertEquals("IL", savedAddress.getState(), "State should match");
          Assertions.assertEquals("62701", savedAddress.getPostalCode(), "Zip code should match");
          Assertions.assertEquals("USA", savedAddress.getCountry(), "Country should match");
          Assertions.assertEquals(
              AddressJpaEntity.AddressType.BILLING,
              savedAddress.getAddressType(),
              "Address type should be BILLING");
        });
  }

  @Test
  void testCreateCustomerWithDuplicateEmailThrows() {
    CustomerJpaEntity customer1 = new CustomerJpaEntity();
    customer1.setFirstName("Jane");
    customer1.setLastName("Smith");
    customer1.setEmail("<EMAIL>");
    customer1.setCountry("USA");
    customer1.setSegmentId(UUID.randomUUID());
    customer1.setStatus(CustomerJpaEntity.Status.ACTIVE);
    AddressJpaEntity address1 = new AddressJpaEntity();
    address1.setStreet("1 Test St");
    address1.setCity("Testville");
    address1.setState("TS");
    address1.setPostalCode("00001");
    address1.setCountry("USA");
    address1.setAddressType(AddressJpaEntity.AddressType.BILLING);
    address1.setCustomer(customer1);
    List<AddressJpaEntity> addresses1 = new ArrayList<>();
    addresses1.add(address1);
    customer1.setAddresses(addresses1);
    customerRepository.save(customer1);

    CustomerJpaEntity customer2 = new CustomerJpaEntity();
    customer2.setFirstName("Jake");
    customer2.setLastName("Smith");
    customer2.setEmail("<EMAIL>");
    customer2.setCountry("USA");
    customer2.setSegmentId(UUID.randomUUID());
    customer2.setStatus(CustomerJpaEntity.Status.ACTIVE);
    AddressJpaEntity address2 = new AddressJpaEntity();
    address2.setStreet("2 Test St");
    address2.setCity("Testville");
    address2.setState("TS");
    address2.setPostalCode("00002");
    address2.setCountry("USA");
    address2.setAddressType(AddressJpaEntity.AddressType.SHIPPING);
    address2.setCustomer(customer2);
    List<AddressJpaEntity> addresses2 = new ArrayList<>();
    addresses2.add(address2);
    customer2.setAddresses(addresses2);

    Assertions.assertThrows(
        DataIntegrityViolationException.class,
        () -> {
          customerRepository.saveAndFlush(customer2);
        });
  }
}
