package com.avantiq.billing.infrastructure.persistence.product.serializer;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.avantiq.billing.domain.product.model.Price;
import com.avantiq.billing.domain.product.valueobject.Pricing;
import com.avantiq.billing.domain.product.valueobject.pricing.FlatPricing;
import com.avantiq.billing.domain.product.valueobject.pricing.OveragePricing;
import com.avantiq.billing.domain.product.valueobject.pricing.PerUnitPricing;
import com.avantiq.billing.domain.product.valueobject.pricing.RampPricing;
import com.avantiq.billing.domain.product.valueobject.pricing.TieredPricing;
import com.avantiq.billing.domain.product.valueobject.pricing.VolumePricing;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class PricingSerializerTest {

  private PricingSerializer serializer;

  @BeforeEach
  void setUp() {
    serializer = new PricingSerializer();
  }

  @Test
  void shouldSerializeAndDeserializeFlatPricing() {
    // Given
    FlatPricing original = FlatPricing.builder().unitPrice(new BigDecimal("10.50")).build();

    // When
    String json = serializer.serialize(original);
    Pricing deserialized = serializer.deserialize(json, Price.PriceStrategy.FLAT);

    // Then
    assertThat(deserialized).isInstanceOf(FlatPricing.class);
    FlatPricing result = (FlatPricing) deserialized;
    assertThat(result.getUnitPrice()).isEqualByComparingTo("10.50");
    assertThat(result.getStrategy()).isEqualTo(Price.PriceStrategy.FLAT);
  }

  @Test
  void shouldSerializeAndDeserializeTieredPricing() {
    // Given
    TieredPricing original =
        TieredPricing.builder()
            .tiers(
                Arrays.asList(
                    TieredPricing.Tier.builder()
                        .minQuantity(BigDecimal.ZERO)
                        .maxQuantity(new BigDecimal("10"))
                        .unitPrice(new BigDecimal("10.00"))
                        .label("Starter")
                        .build(),
                    TieredPricing.Tier.builder()
                        .minQuantity(new BigDecimal("11"))
                        .maxQuantity(null)
                        .unitPrice(new BigDecimal("8.00"))
                        .label("Growth")
                        .build()))
            .build();

    // When
    String json = serializer.serialize(original);
    Pricing deserialized = serializer.deserialize(json, Price.PriceStrategy.TIERED);

    // Then
    assertThat(deserialized).isInstanceOf(TieredPricing.class);
    TieredPricing result = (TieredPricing) deserialized;
    assertThat(result.getTiers()).hasSize(2);
    assertThat(result.getTiers().get(0).getLabel()).isEqualTo("Starter");
    assertThat(result.getTiers().get(1).getMaxQuantity()).isNull();
  }

  @Test
  void shouldSerializeAndDeserializeVolumePricing() {
    // Given
    VolumePricing original =
        VolumePricing.builder()
            .brackets(
                Arrays.asList(
                    VolumePricing.VolumeBracket.builder()
                        .minQuantity(BigDecimal.ZERO)
                        .maxQuantity(new BigDecimal("100"))
                        .unitPrice(new BigDecimal("5.00"))
                        .build()))
            .build();

    // When
    String json = serializer.serialize(original);
    Pricing deserialized = serializer.deserialize(json, Price.PriceStrategy.VOLUME);

    // Then
    assertThat(deserialized).isInstanceOf(VolumePricing.class);
    VolumePricing result = (VolumePricing) deserialized;
    assertThat(result.getBrackets()).hasSize(1);
  }

  @Test
  void shouldSerializeAndDeserializeOveragePricing() {
    // Given
    OveragePricing original =
        OveragePricing.builder()
            .includedQuantity(new BigDecimal("1000"))
            .basePrice(new BigDecimal("100.00"))
            .overageUnitPrice(new BigDecimal("0.10"))
            .build();

    // When
    String json = serializer.serialize(original);
    Pricing deserialized = serializer.deserialize(json, Price.PriceStrategy.OVERAGE);

    // Then
    assertThat(deserialized).isInstanceOf(OveragePricing.class);
    OveragePricing result = (OveragePricing) deserialized;
    assertThat(result.getIncludedQuantity()).isEqualByComparingTo("1000");
    assertThat(result.getBasePrice()).isEqualByComparingTo("100.00");
    assertThat(result.getOverageUnitPrice()).isEqualByComparingTo("0.10");
  }

  @Test
  void shouldSerializeAndDeserializePerUnitPricing() {
    // Given
    PerUnitPricing original =
        PerUnitPricing.builder()
            .pricePerUnit(new BigDecimal("0.50"))
            .pricingUnit("request")
            .blockSize(new BigDecimal("1000000"))
            .blockRounding(RoundingMode.CEILING)
            .build();

    // When
    String json = serializer.serialize(original);
    Pricing deserialized = serializer.deserialize(json, Price.PriceStrategy.PER_UNIT);

    // Then
    assertThat(deserialized).isInstanceOf(PerUnitPricing.class);
    PerUnitPricing result = (PerUnitPricing) deserialized;
    assertThat(result.getPricePerUnit()).isEqualByComparingTo("0.50");
    assertThat(result.getBlockSize()).isEqualByComparingTo("1000000");
    assertThat(result.getBlockRounding()).isEqualTo(RoundingMode.CEILING);
  }

  @Test
  void shouldSerializeAndDeserializeRampPricing() {
    // Given
    FlatPricing basePricing = FlatPricing.builder().unitPrice(new BigDecimal("100.00")).build();

    RampPricing original =
        RampPricing.builder()
            .basePricing(basePricing)
            .periods(
                Arrays.asList(
                    RampPricing.RampPeriod.builder()
                        .periodNumber(1)
                        .durationMonths(3)
                        .discountPercent(new BigDecimal("50"))
                        .label("Intro")
                        .build(),
                    RampPricing.RampPeriod.builder()
                        .periodNumber(2)
                        .durationMonths(null)
                        .discountPercent(BigDecimal.ZERO)
                        .label("Standard")
                        .build()))
            .build();

    // When
    String json = serializer.serialize(original);
    Pricing deserialized = serializer.deserialize(json, Price.PriceStrategy.RAMP);

    // Then
    assertThat(deserialized).isInstanceOf(RampPricing.class);
    RampPricing result = (RampPricing) deserialized;
    assertThat(result.getBasePricing()).isInstanceOf(FlatPricing.class);
    assertThat(result.getPeriods()).hasSize(2);
    assertThat(result.getPeriods().get(0).getDiscountPercent()).isEqualByComparingTo("50");
  }

  @Test
  void shouldThrowExceptionForStrategyMismatch() {
    // Given
    FlatPricing pricing = FlatPricing.builder().unitPrice(new BigDecimal("10.00")).build();
    String json = serializer.serialize(pricing);

    // When/Then
    assertThatThrownBy(() -> serializer.deserialize(json, Price.PriceStrategy.TIERED))
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessageContaining("Pricing strategy mismatch");
  }

  @Test
  void shouldHandleNullPricing() {
    // When
    String json = serializer.serialize(null);
    Pricing deserialized = serializer.deserialize(null);

    // Then
    assertThat(json).isNull();
    assertThat(deserialized).isNull();
  }

  @Test
  void shouldHandleEmptyJson() {
    // When
    Pricing deserialized = serializer.deserialize("");

    // Then
    assertThat(deserialized).isNull();
  }

  @Test
  void shouldThrowExceptionForInvalidJson() {
    // Given
    String invalidJson = "{ invalid json }";

    // When/Then
    assertThatThrownBy(() -> serializer.deserialize(invalidJson))
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessageContaining("Failed to deserialize pricing");
  }

  @Test
  void shouldValidatePricingAfterDeserialization() {
    // Given - Invalid pricing (negative unit price)
    String invalidJson = "{\"type\":\"FLAT\",\"unitPrice\":-10.00}";

    // When/Then
    assertThatThrownBy(() -> serializer.deserialize(invalidJson))
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessageContaining("Unit price must be non-negative");
  }
}
