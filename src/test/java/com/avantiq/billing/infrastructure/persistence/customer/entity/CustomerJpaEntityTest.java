package com.avantiq.billing.infrastructure.persistence.customer.entity;

import static org.junit.jupiter.api.Assertions.*;

import java.util.UUID;
import org.junit.jupiter.api.Test;

class CustomerJpaEntityTest {

  @Test
  void removeAddress_ShouldRemoveAddressFromCustomer() {
    // Arrange
    CustomerJpaEntity customer = new CustomerJpaEntity();
    customer.setId(UUID.randomUUID());
    customer.setFirstName("John");
    customer.setLastName("Doe");
    customer.setEmail("<EMAIL>");
    customer.setCountry("USA");
    customer.setSegmentId(UUID.randomUUID());
    customer.setStatus(CustomerJpaEntity.Status.ACTIVE);

    AddressJpaEntity address = new AddressJpaEntity();
    address.setId(UUID.randomUUID());
    address.setStreet("123 Main St");
    address.setCity("New York");
    address.setState("NY");
    address.setPostalCode("10001");
    address.setCountry("USA");
    address.setAddressType(AddressJpaEntity.AddressType.BILLING);

    customer.addAddress(address);

    // Act
    customer.removeAddress(address);

    // Assert
    assertEquals(0, customer.getAddresses().size());
    assertTrue(customer.getAddresses().isEmpty());
    assertNull(address.getCustomer());
  }

  @Test
  void removeAddress_ShouldNotRemoveOtherAddresses() {
    // Arrange
    CustomerJpaEntity customer = new CustomerJpaEntity();
    customer.setId(UUID.randomUUID());
    customer.setFirstName("Jane");
    customer.setLastName("Smith");
    customer.setEmail("<EMAIL>");
    customer.setCountry("Canada");
    customer.setSegmentId(UUID.randomUUID());
    customer.setStatus(CustomerJpaEntity.Status.ACTIVE);

    AddressJpaEntity address1 = new AddressJpaEntity();
    address1.setId(UUID.randomUUID());
    address1.setStreet("456 Market St");
    address1.setCity("Toronto");
    address1.setState("ON");
    address1.setPostalCode("M5A 1A1");
    address1.setCountry("Canada");
    address1.setAddressType(AddressJpaEntity.AddressType.SHIPPING);

    AddressJpaEntity address2 = new AddressJpaEntity();
    address2.setId(UUID.randomUUID());
    address2.setStreet("789 Elm St");
    address2.setCity("Toronto");
    address2.setState("ON");
    address2.setPostalCode("M5A 2B2");
    address2.setCountry("Canada");
    address2.setAddressType(AddressJpaEntity.AddressType.BILLING);

    customer.addAddress(address1);
    customer.addAddress(address2);

    // Act
    customer.removeAddress(address1);

    // Assert
    assertEquals(1, customer.getAddresses().size());
    assertTrue(customer.getAddresses().contains(address2));
    assertFalse(customer.getAddresses().contains(address1));
    assertNull(address1.getCustomer());
    assertEquals(customer, address2.getCustomer());
  }

  /**
   * Tests for the {@link CustomerJpaEntity#addAddress(AddressJpaEntity)} method. Ensures that the
   * address is correctly added to the customer's list of addresses and the relationship is properly
   * established.
   */
  @Test
  void addAddress_ShouldAddAddressToCustomer() {
    // Arrange
    CustomerJpaEntity customer = new CustomerJpaEntity();
    customer.setId(UUID.randomUUID());
    customer.setFirstName("John");
    customer.setLastName("Doe");
    customer.setEmail("<EMAIL>");
    customer.setCountry("USA");
    customer.setSegmentId(UUID.randomUUID());
    customer.setStatus(CustomerJpaEntity.Status.ACTIVE);

    AddressJpaEntity address = new AddressJpaEntity();
    address.setId(UUID.randomUUID());
    address.setStreet("123 Main St");
    address.setCity("New York");
    address.setState("NY");
    address.setPostalCode("10001");
    address.setCountry("USA");
    address.setAddressType(AddressJpaEntity.AddressType.BILLING);

    // Act
    customer.addAddress(address);

    // Assert
    assertEquals(1, customer.getAddresses().size());
    assertTrue(customer.getAddresses().contains(address));
    assertEquals(customer, address.getCustomer());
  }

  @Test
  void addAddress_ShouldNotReplaceExistingAddresses() {
    // Arrange
    CustomerJpaEntity customer = new CustomerJpaEntity();
    customer.setId(UUID.randomUUID());
    customer.setFirstName("Jane");
    customer.setLastName("Smith");
    customer.setEmail("<EMAIL>");
    customer.setCountry("Canada");
    customer.setSegmentId(UUID.randomUUID());
    customer.setStatus(CustomerJpaEntity.Status.ACTIVE);

    AddressJpaEntity address1 = new AddressJpaEntity();
    address1.setId(UUID.randomUUID());
    address1.setStreet("456 Market St");
    address1.setCity("Toronto");
    address1.setState("ON");
    address1.setPostalCode("M5A 1A1");
    address1.setCountry("Canada");
    address1.setAddressType(AddressJpaEntity.AddressType.SHIPPING);

    AddressJpaEntity address2 = new AddressJpaEntity();
    address2.setId(UUID.randomUUID());
    address2.setStreet("789 Elm St");
    address2.setCity("Toronto");
    address2.setState("ON");
    address2.setPostalCode("M5A 2B2");
    address2.setCountry("Canada");
    address2.setAddressType(AddressJpaEntity.AddressType.BILLING);

    // Act
    customer.addAddress(address1);
    customer.addAddress(address2);

    // Assert
    assertEquals(2, customer.getAddresses().size());
    assertTrue(customer.getAddresses().contains(address1));
    assertTrue(customer.getAddresses().contains(address2));
    assertEquals(customer, address1.getCustomer());
    assertEquals(customer, address2.getCustomer());
  }
}
