package com.avantiq.billing.infrastructure.persistence.customer.mapper;

import static org.junit.jupiter.api.Assertions.*;

import com.avantiq.billing.domain.customer.model.Customer;
import com.avantiq.billing.infrastructure.persistence.customer.entity.AddressJpaEntity;
import com.avantiq.billing.infrastructure.persistence.customer.entity.CustomerJpaEntity;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;

public class CustomerMapperTest {
  @Test
  void toDomain_shouldMapFieldsCorrectly_fromCustomerJpaEntity() {
    // Arrange
    CustomerJpaEntity.Status status = CustomerJpaEntity.Status.ACTIVE;
    List<AddressJpaEntity> addressEntities = new ArrayList<>();
    CustomerJpaEntity customerJpaEntity = new CustomerJpaEntity();
    customerJpaEntity.setId(UUID.randomUUID());
    customerJpaEntity.setFirstName("<PERSON>");
    customerJpaEntity.setLastName("Doe");
    customerJpaEntity.setEmail("<EMAIL>");
    customerJpaEntity.setCompanyName("Example Inc.");
    customerJpaEntity.setVatNumber("12345678");
    customerJpaEntity.setCountry("US");
    customerJpaEntity.setSegmentId(UUID.randomUUID());
    customerJpaEntity.setCreatedDate(LocalDateTime.now());
    customerJpaEntity.setUpdatedDate(LocalDateTime.now());
    customerJpaEntity.setStatus(status);
    customerJpaEntity.setAddresses(addressEntities);

    // Act
    Customer customer = CustomerMapper.toDomain(customerJpaEntity);

    // Assert
    assertNotNull(customer);
    assertEquals("John", customer.getFirstName());
    assertEquals("Doe", customer.getLastName());
    assertEquals("<EMAIL>", customer.getEmail());
    assertEquals("Example Inc.", customer.getCompanyName());
    assertEquals("12345678", customer.getVatNumber());
    assertEquals("US", customer.getCountry());
    assertNotNull(customer.getSegmentId());
    assertEquals(Customer.Status.ACTIVE, customer.getStatus());
    assertNotNull(customer.getAddresses());
    assertTrue(customer.getAddresses().isEmpty());
  }

  @Test
  void toDomain_withEmptyAddresses_inCustomerJpaEntity_shouldHandleGracefully() {
    // Arrange
    CustomerJpaEntity customerJpaEntity = new CustomerJpaEntity();
    customerJpaEntity.setId(UUID.randomUUID());
    customerJpaEntity.setFirstName("Jane");
    customerJpaEntity.setLastName("Smith");
    customerJpaEntity.setEmail("<EMAIL>");
    customerJpaEntity.setSegmentId(UUID.randomUUID());
    customerJpaEntity.setCountry("UK");
    customerJpaEntity.setStatus(CustomerJpaEntity.Status.ACTIVE);
    customerJpaEntity.setAddresses(new ArrayList<>());

    // Act
    Customer customer = CustomerMapper.toDomain(customerJpaEntity);

    // Assert
    assertNotNull(customer);
    assertEquals("Jane", customer.getFirstName());
    assertEquals("Smith", customer.getLastName());
    assertEquals("<EMAIL>", customer.getEmail());
    assertEquals("UK", customer.getCountry());
    assertEquals(Customer.Status.ACTIVE, customer.getStatus());
    assertNotNull(customer.getAddresses());
    assertTrue(customer.getAddresses().isEmpty());
  }
}
