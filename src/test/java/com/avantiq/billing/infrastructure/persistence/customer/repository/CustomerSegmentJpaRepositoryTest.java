package com.avantiq.billing.infrastructure.persistence.customer.repository;

import com.avantiq.billing.infrastructure.persistence.customer.entity.CustomerSegmentJpaEntity;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;

@DataJpaTest
class CustomerSegmentJpaRepositoryTest {
  @Autowired private CustomerSegmentJpaRepository repository;

  @Test
  void testSaveAndFindById() {
    CustomerSegmentJpaEntity segment = new CustomerSegmentJpaEntity();
    UUID id = UUID.randomUUID();
    segment.setId(id);
    segment.setName("Enterprise");
    segment.setDescription("Enterprise customers");
    repository.save(segment);
    CustomerSegmentJpaEntity found = repository.findById(id).orElse(null);
    Assertions.assertAll(
        () -> {
          Assertions.assertNotNull(found, "Segment should not be null");
          Assertions.assertEquals("Enterprise", found.getName(), "Segment name should match");
          Assertions.assertEquals(
              "Enterprise customers", found.getDescription(), "Segment description should match");
        });
  }
}
