package com.avantiq.billing.infrastructure.grpc.common;

import static org.junit.jupiter.api.Assertions.*;

import com.avantiq.billing.domain.common.exception.ErrorCode;
import com.avantiq.billing.domain.customer.exception.CustomerNotFoundException;
import com.avantiq.billing.domain.customer.exception.InvalidCustomerDataException;
import com.avantiq.billing.domain.product.exception.ProductNotFoundException;
import io.grpc.Metadata;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class GrpcExceptionHandlerTest {

  private static final Metadata.Key<String> ERROR_CODE_KEY =
      Metadata.Key.of("error-code", Metadata.ASCII_STRING_MARSHALLER);

  private static final Metadata.Key<String> ERROR_TYPE_KEY =
      Metadata.Key.of("error-type", Metadata.ASCII_STRING_MARSHALLER);

  @Test
  public void testHandleCustomerNotFoundException() {
    // Arrange
    CustomerNotFoundException exception = new CustomerNotFoundException("test-id");

    // Act
    StatusRuntimeException result = GrpcExceptionHandler.handleExceptionWithMetadata(exception);

    // Assert
    assertEquals(Status.Code.NOT_FOUND, result.getStatus().getCode());
    assertTrue(result.getStatus().getDescription().contains("test-id"));

    Metadata metadata = result.getTrailers();
    assertEquals(
        String.valueOf(ErrorCode.CUSTOMER_NOT_FOUND.getCode()), metadata.get(ERROR_CODE_KEY));
    assertEquals(ErrorCode.CUSTOMER_NOT_FOUND.name(), metadata.get(ERROR_TYPE_KEY));
  }

  @Test
  public void testHandleInvalidCustomerDataException() {
    // Arrange
    InvalidCustomerDataException exception =
        new InvalidCustomerDataException("Invalid email format");

    // Act
    StatusRuntimeException result = GrpcExceptionHandler.handleExceptionWithMetadata(exception);

    // Assert
    assertEquals(Status.Code.INVALID_ARGUMENT, result.getStatus().getCode());
    assertTrue(result.getStatus().getDescription().contains("Invalid email format"));

    Metadata metadata = result.getTrailers();
    assertEquals(
        String.valueOf(ErrorCode.INVALID_CUSTOMER_DATA.getCode()), metadata.get(ERROR_CODE_KEY));
    assertEquals(ErrorCode.INVALID_CUSTOMER_DATA.name(), metadata.get(ERROR_TYPE_KEY));
  }

  @Test
  public void testHandleProductNotFoundException() {
    // Arrange
    ProductNotFoundException exception = new ProductNotFoundException("test-product-id");

    // Act
    StatusRuntimeException result = GrpcExceptionHandler.handleExceptionWithMetadata(exception);

    // Assert
    assertEquals(Status.Code.NOT_FOUND, result.getStatus().getCode());
    assertTrue(result.getStatus().getDescription().contains("test-product-id"));

    Metadata metadata = result.getTrailers();
    assertEquals(
        String.valueOf(ErrorCode.PRODUCT_NOT_FOUND.getCode()), metadata.get(ERROR_CODE_KEY));
    assertEquals(ErrorCode.PRODUCT_NOT_FOUND.name(), metadata.get(ERROR_TYPE_KEY));
  }

  @Test
  public void testHandleGenericException() {
    // Arrange
    Exception exception = new RuntimeException("Generic error");

    // Act
    StatusRuntimeException result = GrpcExceptionHandler.handleExceptionWithMetadata(exception);

    // Assert
    assertEquals(Status.Code.INTERNAL, result.getStatus().getCode());
    assertTrue(result.getStatus().getDescription().contains("Generic error"));

    Metadata metadata = result.getTrailers();
    assertEquals("5000", metadata.get(ERROR_CODE_KEY));
    assertEquals("INTERNAL_ERROR", metadata.get(ERROR_TYPE_KEY));
  }
}
