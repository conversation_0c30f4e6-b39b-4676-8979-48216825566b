package com.avantiq.billing.infrastructure.grpc.mapper;

import static org.junit.jupiter.api.Assertions.*;

import com.avantiq.billing.customer.grpc.Address;
import com.avantiq.billing.customer.grpc.CreateCustomerRequest;
import com.avantiq.billing.domain.customer.model.Address.AddressType;
import com.avantiq.billing.domain.customer.model.Customer;
import java.util.UUID;
import org.junit.jupiter.api.Test;

class CustomerMapperTest {

  @Test
  void toDomain_shouldMapFieldsCorrectly() {
    // Arrange
    CreateCustomerRequest request =
        CreateCustomerRequest.newBuilder()
            .setFirstName("John")
            .setLastName("Doe")
            .setEmail("<EMAIL>")
            .setCompanyName("Example Inc.")
            .setVatNumber("12345678")
            .setCountry("US")
            .setSegmentId(UUID.randomUUID().toString())
            .addAddresses(
                Address.newBuilder()
                    .setStreet("123 Main St")
                    .setCity("Springfield")
                    .setState("IL")
                    .setPostalCode("62704")
                    .setCountry("US")
                    .setAddressType(Address.AddressType.BILLING)
                    .build())
            .build();

    // Act
    Customer customer = CustomerMapper.toDomain(request);

    // Assert
    assertNotNull(customer);
    assertEquals("John", customer.getFirstName());
    assertEquals("Doe", customer.getLastName());
    assertEquals("<EMAIL>", customer.getEmail());
    assertEquals("Example Inc.", customer.getCompanyName());
    assertEquals("12345678", customer.getVatNumber());
    assertEquals("US", customer.getCountry());
    assertNotNull(customer.getSegmentId());
    assertEquals(Customer.Status.ACTIVE, customer.getStatus());

    assertNotNull(customer.getAddresses());
    assertEquals(1, customer.getAddresses().size());
    com.avantiq.billing.domain.customer.model.Address address = customer.getAddresses().get(0);
    assertEquals("123 Main St", address.getStreet());
    assertEquals("Springfield", address.getCity());
    assertEquals("IL", address.getState());
    assertEquals("62704", address.getPostalCode());
    assertEquals("US", address.getCountry());
    assertEquals(AddressType.BILLING, address.getAddressType());
  }

  @Test
  void toDomain_withInvalidSegmentId_shouldReturnNull() {
    // Arrange
    CreateCustomerRequest request =
        CreateCustomerRequest.newBuilder()
            .setFirstName("Jane")
            .setLastName("Doe")
            .setEmail("<EMAIL>")
            .setCompanyName("Example Co.")
            .setVatNumber("87654321")
            .setCountry("US")
            .setSegmentId("invalid-uuid")
            .build();

    // Act
    Customer customer = CustomerMapper.toDomain(request);

    // Assert
    assertNull(customer);
  }

  @Test
  void toDomain_withEmptyAddresses_shouldHandleGracefully() {
    // Arrange
    CreateCustomerRequest request =
        CreateCustomerRequest.newBuilder()
            .setFirstName("Alice")
            .setLastName("Smith")
            .setEmail("<EMAIL>")
            .setCompanyName("Demo LLC")
            .setVatNumber("11223344")
            .setCountry("UK")
            .setSegmentId(UUID.randomUUID().toString())
            .build();

    // Act
    Customer customer = CustomerMapper.toDomain(request);

    // Assert
    assertNotNull(customer);
    assertEquals("Alice", customer.getFirstName());
    assertEquals("Smith", customer.getLastName());
    assertEquals("<EMAIL>", customer.getEmail());
    assertEquals("Demo LLC", customer.getCompanyName());
    assertEquals("11223344", customer.getVatNumber());
    assertEquals("UK", customer.getCountry());
    assertNotNull(customer.getSegmentId());
    assertEquals(Customer.Status.ACTIVE, customer.getStatus());
    assertNotNull(customer.getAddresses());
    assertTrue(customer.getAddresses().isEmpty());
  }
}
