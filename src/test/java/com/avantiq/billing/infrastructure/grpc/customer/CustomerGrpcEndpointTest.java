package com.avantiq.billing.infrastructure.grpc.customer;

import com.avantiq.billing.application.customer.ContactApplicationService;
import com.avantiq.billing.application.customer.CustomerApplicationService;
import com.avantiq.billing.application.customer.CustomerRelationshipApplicationService;
import com.avantiq.billing.application.customer.CustomerSegmentApplicationService;
import com.avantiq.billing.application.payment.PaymentMethodApplicationService;
import com.avantiq.billing.customer.grpc.CustomerResponse;
import com.avantiq.billing.customer.grpc.GetCustomerRequest;
import com.avantiq.billing.domain.customer.model.Customer;
import com.avantiq.billing.infrastructure.security.context.TenantContextService;
import io.grpc.stub.StreamObserver;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class CustomerGrpcEndpointTest {
  private final CustomerApplicationService customerApplicationService =
      Mockito.mock(CustomerApplicationService.class);
  private final ContactApplicationService contactApplicationService =
      Mockito.mock(ContactApplicationService.class);
  private final CustomerSegmentApplicationService segmentApplicationService =
      Mockito.mock(CustomerSegmentApplicationService.class);
  private final PaymentMethodApplicationService paymentMethodApplicationService =
      Mockito.mock(PaymentMethodApplicationService.class);
  private final CustomerRelationshipApplicationService relationshipApplicationService =
      Mockito.mock(CustomerRelationshipApplicationService.class);
  private final TenantContextService tenantContextService =
      Mockito.mock(TenantContextService.class);

  private final CustomerGrpcEndpoint endpoint =
      new CustomerGrpcEndpoint(
          customerApplicationService,
          contactApplicationService,
          segmentApplicationService,
          paymentMethodApplicationService,
          relationshipApplicationService,
          tenantContextService);

  @Test
  void testGetCustomerById() {
    UUID id = UUID.randomUUID();
    Customer customer = new Customer();
    customer.setId(id);
    Mockito.when(customerApplicationService.findById(id)).thenReturn(Optional.of(customer));
    GetCustomerRequest request = GetCustomerRequest.newBuilder().setId(id.toString()).build();
    StreamObserver<CustomerResponse> observer =
        new StreamObserver<>() {
          CustomerResponse response;

          @Override
          public void onNext(CustomerResponse value) {
            response = value;
          }

          @Override
          public void onError(Throwable t) {
            Assertions.fail(t);
          }

          @Override
          public void onCompleted() {
            Assertions.assertNotNull(response);
            Assertions.assertEquals(id.toString(), response.getCustomer().getId());
          }
        };
    endpoint.getCustomer(request, observer);
  }

  // Add more endpoint tests as actual endpoint methods are defined
}
