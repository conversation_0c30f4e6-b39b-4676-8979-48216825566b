package com.avantiq.billing.infrastructure.grpc.common;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.avantiq.billing.domain.customer.exception.CustomerNotFoundException;
import io.grpc.Metadata;
import io.grpc.ServerCall;
import io.grpc.ServerCallHandler;
import io.grpc.Status;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class GrpcExceptionInterceptorTest {

  @Mock private ServerCall<String, String> serverCall;

  @Mock private ServerCallHandler<String, String> serverCallHandler;

  @Mock private ServerCall.Listener<String> listener;

  @Test
  public void testInterceptCall_NoException() {
    // Arrange
    GrpcExceptionInterceptor interceptor = new GrpcExceptionInterceptor();
    when(serverCallHandler.startCall(any(), any())).thenReturn(listener);

    // Act
    ServerCall.Listener<String> result =
        interceptor.interceptCall(serverCall, new Metadata(), serverCallHandler);

    // Assert
    assertNotNull(result);
    verify(serverCallHandler).startCall(any(), any());
  }

  @Test
  public void testInterceptCall_WithException() {
    // Arrange
    GrpcExceptionInterceptor interceptor = new GrpcExceptionInterceptor();
    CustomerNotFoundException exception = new CustomerNotFoundException("test-id");
    doThrow(exception).when(serverCallHandler).startCall(any(), any());

    // Act
    interceptor.interceptCall(serverCall, new Metadata(), serverCallHandler);

    // Assert
    verify(serverCall)
        .close(
            argThat(
                status ->
                    status.getCode() == Status.Code.NOT_FOUND
                        && status.getDescription() != null
                        && status.getDescription().contains("test-id")),
            any(Metadata.class));
  }

  private void assertNotNull(ServerCall.Listener<String> result) {
    // Helper method to avoid static import conflicts
    org.junit.jupiter.api.Assertions.assertNotNull(result);
  }
}
