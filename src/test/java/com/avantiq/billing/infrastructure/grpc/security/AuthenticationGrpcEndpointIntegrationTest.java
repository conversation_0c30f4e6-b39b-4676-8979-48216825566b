package com.avantiq.billing.infrastructure.grpc.security;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.avantiq.billing.application.security.AuthenticationApplicationService;
import com.avantiq.billing.domain.security.model.Role;
import com.avantiq.billing.domain.security.model.User;
import com.avantiq.billing.domain.security.model.UserStatus;
import com.avantiq.billing.infrastructure.grpc.proto.AuthenticationServiceGrpc;
import com.avantiq.billing.infrastructure.grpc.proto.LoginRequest;
import com.avantiq.billing.infrastructure.grpc.proto.LoginResponse;
import com.avantiq.billing.infrastructure.grpc.proto.LogoutRequest;
import com.avantiq.billing.infrastructure.grpc.proto.LogoutResponse;
import com.avantiq.billing.infrastructure.grpc.proto.RefreshTokenRequest;
import com.avantiq.billing.infrastructure.grpc.proto.ValidateTokenRequest;
import com.avantiq.billing.infrastructure.grpc.proto.ValidateTokenResponse;
import com.avantiq.billing.infrastructure.security.jwt.JwtTokenProvider;
import io.grpc.ManagedChannel;
import io.grpc.Metadata;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import io.grpc.inprocess.InProcessChannelBuilder;
import io.grpc.inprocess.InProcessServerBuilder;
import io.grpc.stub.MetadataUtils;
import java.io.IOException;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.authentication.BadCredentialsException;

/**
 * Integration tests for AuthenticationGrpcEndpoint. Tests the complete authentication flow
 * including JWT token handling.
 */
@ExtendWith(MockitoExtension.class)
class AuthenticationGrpcEndpointIntegrationTest {

  @Mock private AuthenticationApplicationService authService;

  @Mock private JwtTokenProvider jwtTokenProvider;

  private AuthenticationServiceGrpc.AuthenticationServiceBlockingStub stub;
  private ManagedChannel channel;
  private AuthenticationGrpcEndpoint endpoint;
  private io.grpc.Server server;

  @BeforeEach
  void setUp() throws IOException {
    // Create the endpoint with mocked dependencies
    endpoint = new AuthenticationGrpcEndpoint(authService, jwtTokenProvider);

    // Generate a unique server name for the test
    String serverName = InProcessServerBuilder.generateName();

    // Create and start the server
    server =
        InProcessServerBuilder.forName(serverName)
            .directExecutor()
            .addService(endpoint)
            .build()
            .start();

    // Create a client channel
    channel = InProcessChannelBuilder.forName(serverName).directExecutor().build();

    // Create the stub
    stub = AuthenticationServiceGrpc.newBlockingStub(channel);
  }

  @AfterEach
  void tearDown() throws InterruptedException {
    if (channel != null && !channel.isShutdown()) {
      channel.shutdownNow();
      channel.awaitTermination(5, TimeUnit.SECONDS);
    }
    if (server != null && !server.isShutdown()) {
      server.shutdownNow();
      server.awaitTermination(5, TimeUnit.SECONDS);
    }
  }

  @Test
  void testSuccessfulLogin() {
    // Given
    String username = "<EMAIL>";
    String password = "password123";
    String token = "test-jwt-token";
    UUID userId = UUID.randomUUID();
    Long tenantId = 1L;

    LoginRequest request =
        LoginRequest.newBuilder().setUsername(username).setPassword(password).build();

    AuthenticationApplicationService.LoginRequest serviceRequest =
        AuthenticationApplicationService.LoginRequest.builder()
            .username(username)
            .password(password)
            .build();

    AuthenticationApplicationService.AuthenticationResult serviceResult =
        AuthenticationApplicationService.AuthenticationResult.builder()
            .token(token)
            .userId(userId)
            .username(username)
            .tenantId(tenantId)
            .expiresAt(java.time.LocalDateTime.now().plusHours(24))
            .success(true)
            .build();

    Mockito.when(authService.login(Mockito.any())).thenReturn(serviceResult);

    // When
    LoginResponse response = stub.login(request);

    // Then
    assertThat(response.getSuccess()).isTrue();
    assertThat(response.hasAuthData()).isTrue();
    assertThat(response.getAuthData().getToken()).isEqualTo(token);
    assertThat(response.getAuthData().getUserId()).isEqualTo(userId.toString());
    assertThat(response.getAuthData().getUsername()).isEqualTo(username);
    assertThat(response.getAuthData().getTenantId()).isEqualTo(tenantId);
  }

  @Test
  void testFailedLogin_InvalidCredentials() {
    // Given
    LoginRequest request =
        LoginRequest.newBuilder()
            .setUsername("<EMAIL>")
            .setPassword("wrongpassword")
            .build();

    Mockito.when(authService.login(Mockito.any()))
        .thenThrow(new BadCredentialsException("Invalid username or password"));

    // When
    LoginResponse response = stub.login(request);

    // Then
    assertThat(response.getSuccess()).isFalse();
    assertThat(response.getMessage()).contains("Invalid username or password");
    assertThat(response.hasAuthData()).isFalse();
  }

  @Test
  void testSuccessfulLogout() {
    // Given
    String token = "test-jwt-token";

    // Create metadata with authorization header
    Metadata metadata = new Metadata();
    metadata.put(
        Metadata.Key.of("authorization", Metadata.ASCII_STRING_MARSHALLER), "Bearer " + token);

    AuthenticationServiceGrpc.AuthenticationServiceBlockingStub authenticatedStub =
        stub.withInterceptors(MetadataUtils.newAttachHeadersInterceptor(metadata));

    LogoutRequest request = LogoutRequest.newBuilder().build();

    // When
    LogoutResponse response = authenticatedStub.logout(request);

    // Then
    assertThat(response.getSuccess()).isTrue();
    assertThat(response.getMessage()).contains("Logout successful");
    Mockito.verify(authService).logout();
  }

  @Test
  void testSuccessfulTokenRefresh() {
    // Given
    String oldToken = "old-jwt-token";
    String newToken = "new-jwt-token";
    UUID userId = UUID.randomUUID();
    String username = "<EMAIL>";
    Long tenantId = 1L;

    // Create metadata with authorization header
    Metadata metadata = new Metadata();
    metadata.put(
        Metadata.Key.of("authorization", Metadata.ASCII_STRING_MARSHALLER), "Bearer " + oldToken);

    AuthenticationServiceGrpc.AuthenticationServiceBlockingStub authenticatedStub =
        stub.withInterceptors(MetadataUtils.newAttachHeadersInterceptor(metadata));

    RefreshTokenRequest request = RefreshTokenRequest.newBuilder().build();

    AuthenticationApplicationService.AuthenticationResult serviceResult =
        AuthenticationApplicationService.AuthenticationResult.builder()
            .token(newToken)
            .userId(userId)
            .username(username)
            .tenantId(tenantId)
            .expiresAt(java.time.LocalDateTime.now().plusHours(24))
            .success(true)
            .build();

    Mockito.when(authService.refreshToken()).thenReturn(serviceResult);

    // When
    LoginResponse response = authenticatedStub.refreshToken(request);

    // Then
    assertThat(response.getSuccess()).isTrue();
    assertThat(response.hasAuthData()).isTrue();
    assertThat(response.getAuthData().getToken()).isEqualTo(newToken);
    assertThat(response.getAuthData().getUserId()).isEqualTo(userId.toString());
  }

  @Test
  void testTokenValidation_ValidToken() {
    // Given
    String token = "valid-jwt-token";
    ValidateTokenRequest request = ValidateTokenRequest.newBuilder().setToken(token).build();

    // Create a mock user for validation
    User mockUser =
        User.builder()
            .userId(UUID.randomUUID())
            .username("<EMAIL>")
            .email("<EMAIL>")
            .tenantId(1L)
            .roles(Set.of(Role.USER))
            .status(UserStatus.ACTIVE)
            .build();

    Mockito.when(jwtTokenProvider.validateToken(token)).thenReturn(true);
    Mockito.when(jwtTokenProvider.isTokenExpired(token)).thenReturn(false);
    Mockito.when(jwtTokenProvider.getUserIdFromToken(token)).thenReturn(mockUser.getUserId());
    Mockito.when(jwtTokenProvider.getUsernameFromToken(token)).thenReturn(mockUser.getUsername());
    Mockito.when(jwtTokenProvider.getTenantIdFromToken(token)).thenReturn(mockUser.getTenantId());
    Mockito.when(jwtTokenProvider.getRolesFromToken(token)).thenReturn(mockUser.getRoles());
    Mockito.when(jwtTokenProvider.getSegmentsFromToken(token)).thenReturn(Set.of());
    Mockito.when(jwtTokenProvider.getExpirationDateFromToken(token))
        .thenReturn(new java.util.Date(System.currentTimeMillis() + 3600000));

    // When
    ValidateTokenResponse response = stub.validateToken(request);

    // Then
    assertThat(response.getValid()).isTrue();
  }

  @Test
  void testTokenValidation_InvalidToken() {
    // Given
    String token = "invalid-jwt-token";
    ValidateTokenRequest request = ValidateTokenRequest.newBuilder().setToken(token).build();

    // When
    ValidateTokenResponse response = stub.validateToken(request);

    // Then
    assertThat(response.getValid()).isFalse();
    assertThat(response.getMessage()).contains("Token is invalid or expired");
  }

  @Test
  void testLoginWithEmptyCredentials() {
    // Given
    LoginRequest request = LoginRequest.newBuilder().setUsername("").setPassword("").build();

    Mockito.when(authService.login(Mockito.any()))
        .thenThrow(new BadCredentialsException("Username and password are required"));

    // When
    LoginResponse response = stub.login(request);

    // Then
    assertThat(response.getSuccess()).isFalse();
    assertThat(response.getMessage()).contains("Invalid username or password");
    assertThat(response.hasAuthData()).isFalse();
  }

  @Test
  void testRefreshTokenWithoutAuthentication() {
    // Given
    RefreshTokenRequest request = RefreshTokenRequest.newBuilder().build();

    Mockito.when(authService.refreshToken())
        .thenThrow(new BadCredentialsException("No authenticated user"));

    // When/Then
    assertThatThrownBy(() -> stub.refreshToken(request))
        .isInstanceOf(StatusRuntimeException.class)
        .satisfies(
            e -> {
              StatusRuntimeException sre = (StatusRuntimeException) e;
              assertThat(sre.getStatus().getCode()).isEqualTo(Status.UNAUTHENTICATED.getCode());
            });
  }
}
