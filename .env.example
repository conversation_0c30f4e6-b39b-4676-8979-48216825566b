# Database Configuration
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your-secure-password-here

# Security Configuration
JWT_SECRET=your-secure-jwt-secret-minimum-32-characters

# Application Configuration
JPA_DDL_AUTO=validate

# Rate Limiting Configuration (optional)
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_SECONDS=60

# CORS Configuration (optional)
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080