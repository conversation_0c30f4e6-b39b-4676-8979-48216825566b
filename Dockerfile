FROM eclipse-temurin:21 AS jre-build

RUN $JAVA_HOME/bin/jlink \
         --add-modules ALL-MODULE-PATH \
         --strip-debug \
         --no-man-pages \
         --no-header-files \
         --output /javaruntime

FROM debian:bookworm-slim

ENV JAVA_HOME=/opt/java/openjdk
ENV PATH "${JAVA_HOME}/bin:${PATH}"

COPY --from=jre-build /javaruntime $JAVA_HOME

WORKDIR /opt/app

COPY build/libs/avantiq-0.0.1-SNAPSHOT.jar app.jar

EXPOSE 8080 9090
ENTRYPOINT ["java","-jar","/opt/app/app.jar"]