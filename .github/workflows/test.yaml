name: Pull Requests
on:
  pull_request:
    branches: [ main ]
jobs:
  test:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 21
          cache: gradle
      - name: Run tests
        run: ./gradlew test --no-daemon
      - name: Upload test reports
        uses: actions/upload-artifact@v4
        with:
          name: junit-results
          path: |
            **/build/test-results/**/*.xml
            **/build/reports/tests/**
