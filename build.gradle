plugins {
	id 'java'
	id 'org.springframework.boot' version '3.5.0'
	id 'io.spring.dependency-management' version '1.1.7'
	id 'com.google.protobuf' version '0.9.4'
	id 'checkstyle'
	id 'pmd'
	id 'com.github.spotbugs' version '6.2.0'
	id 'com.diffplug.spotless' version '6.25.0'
	id "io.freefair.lombok" version "8.14"
}

group = 'com.avantiq.billing'
version = '0.0.1-SNAPSHOT'

checkstyle {
	toolVersion = '10.25.0'
	configFile = rootProject.file("config/checkstyle/checkstyle.xml")
	ignoreFailures = true
}

pmd {
	consoleOutput = true
	toolVersion = '7.14.0'
	rulesMinimumPriority = 5
	ruleSets = ["category/java/errorprone.xml", "category/java/bestpractices.xml"]
	ignoreFailures = true
}

spotbugs {
	ignoreFailures = true
}

spotless {
	java {
		targetExclude '**/generated/**'
		importOrder()
		removeUnusedImports()
		googleJavaFormat()
		trimTrailingWhitespace()
		endWithNewline()
	}
}

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(21)
	}
}

repositories {
	mavenCentral()
}

// Remove spring-grpc-dependencies since we're using net.devh:grpc-server-spring-boot-starter
dependencies {
	implementation 'org.springframework.boot:spring-boot-starter-actuator'
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.springframework.boot:spring-boot-starter-validation'
	implementation 'org.springframework.boot:spring-boot-starter-security'
	implementation 'org.springframework.security:spring-security-oauth2-resource-server'
	implementation 'io.micrometer:micrometer-core'
	
	// JWT dependencies
	implementation 'io.jsonwebtoken:jjwt-api:0.11.5'
	implementation 'io.jsonwebtoken:jjwt-impl:0.11.5'
	implementation 'io.jsonwebtoken:jjwt-jackson:0.11.5'
	
	// gRPC dependencies - aligned with Spring Boot starter versions
	implementation 'io.grpc:grpc-protobuf-lite:1.63.0'
	implementation 'io.grpc:grpc-stub:1.63.0'
	implementation 'io.grpc:grpc-services:1.63.0'
	implementation 'net.devh:grpc-server-spring-boot-starter:3.1.0.RELEASE'
	implementation 'net.devh:grpc-client-spring-boot-starter:3.1.0.RELEASE'
	
	// Additional protobuf dependencies - aligned with gRPC 1.63.0
	implementation 'com.google.protobuf:protobuf-java:3.25.1'
	implementation 'com.google.protobuf:protobuf-java-util:3.25.1'
	implementation 'javax.annotation:javax.annotation-api:1.3.2' // Required for generated protobuf code
	
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	// Removing spring-grpc-test dependency as it's not available
	testImplementation 'com.h2database:h2'
	testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
	runtimeOnly 'org.postgresql:postgresql'
	spotbugsPlugins 'com.h3xstream.findsecbugs:findsecbugs-plugin:1.14.0'
}

protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:3.21.12"
    }
    plugins {
        grpc {
            artifact = "io.grpc:protoc-gen-grpc-java:1.58.0"
        }
    }
    generateProtoTasks {
        all()*.plugins {
            grpc {}
        }
        all().forEach { task ->
            task.builtins {
                java {
                    option 'lite'
                }
            }
            task.plugins {
                grpc {
                    option 'lite'
                }
            }
        }
    }
    generatedFilesBaseDir = "$projectDir/build/generated/source/proto"
}

tasks.named('test') {
	useJUnitPlatform()
}

wrapper {
    gradleVersion = '8.14'
    distributionType = 'BIN'
}

bootJar {
    mainClass = 'com.avantiq.billing.Application'
}
