{"permissions": {"allow": ["Bash(grep:*)", "<PERSON><PERSON>(./gradlew:*)", "Bash(grpcurl:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(psql:*)", "<PERSON><PERSON>(docker exec:*)", "<PERSON><PERSON>(curl:*)", "Bash(git commit:*)", "Bash(git add:*)", "mcp__serena__check_onboarding_performed", "mcp__serena__get_symbols_overview", "mcp__serena__list_dir", "mcp__serena__find_file", "mcp__serena__think_about_whether_you_are_done", "mcp__serena__find_symbol", "mcp__serena__search_for_pattern", "<PERSON><PERSON>(echo:*)", "mcp__serena__think_about_collected_information"], "deny": []}}